package com.ruoyi.web.controller.system;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.framework.web.jwt.CreatorJwtUtil;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.domain.WendaoShop;
import com.ruoyi.system.service.ICreatorInfoService;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.IWendaoShopService;
import com.ruoyi.system.service.IWxLoginService;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 创作者店铺controller
 */
@Slf4j
@RestController
@RequestMapping("/creator_shop")
public class CreatorShopController extends BaseController {
    @Autowired
    private CreatorJwtUtil creatorJwtUtil;

    @Autowired
    private IWendaoShopService wendaoShopService;


    //先查询店铺是否已经超过了数量,总数只能是3个,如果为3个时不能创建
    //创建店铺,并设置店铺id
    @Anonymous
    @PostMapping("/createShop")
    public AjaxResult createShop(@RequestBody CreateShopDTO createShopDTO) {
        //获取creatorId
        String creatorId = creatorJwtUtil.getCreatorId();
        //查询店铺数量,tenant_code=creatorId
        //sysTenantService
        List<WendaoShop> wendaoShops = wendaoShopService.selectWendaoShopListByCreatorId(creatorId);
        if (wendaoShops.size() >= 3) {
            return error("店铺数量已经超过了3个,不能再创建");
        }

        //创建店铺
        WendaoShop wendaoShop = new WendaoShop();

        // 设置基本信息
        wendaoShop.setCreatorId(creatorId);
        wendaoShop.setShopName(createShopDTO.getShopName());

        // 生成店铺ID和应用ID
        String shopId = generateShopId();
        wendaoShop.setShopId(shopId);
        wendaoShop.setAppId(shopId); // 应用ID与店铺ID相同

        // 设置默认值
        Date now = new Date();
        wendaoShop.setCreatedAt(now);
        wendaoShop.setCreatedTime(now);

        // 设置过期时间为7天后(只保留日期部分)
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        // 清除时分秒
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        wendaoShop.setExpireTime(calendar.getTime());

        // 设置默认状态值
        wendaoShop.setIsSealed(0);           // 未封禁
        wendaoShop.setShopLogo("");          // 默认空LOGO
        wendaoShop.setUseCollection(0);      // 默认不使用收藏功能
        wendaoShop.setVersionType(0);        // 试用版
        wendaoShop.setRightsType(-1);        // 无权益
        wendaoShop.setHasExpired(0);         // 未过期
        wendaoShop.setHasActivateOrder(0);   // 无未支付激活订单
        wendaoShop.setStatus(0);             // 正常状态
        //最后一次登录,如果登录成功则这个店铺修改为1,其他店铺修改为0
        wendaoShop.setLastLogin(wendaoShops.isEmpty() ? 1 : 0); // 如果是第一个店铺则设为最后登录
        wendaoShop.setIsWaitSeal(0);         // 不等待封禁
        wendaoShop.setIsDrop(0);             // 未注销
        wendaoShop.setEntryMode(0);          // 默认进入模式
        wendaoShop.setIsTry(1);              // 是试用店铺
        wendaoShop.setIsShowVersion(1);      // 显示版本信息
        wendaoShop.setShowRenewal(1);        // 显示续费按钮
        wendaoShop.setShowExpireTime(1);     // 显示到期时间

        // 保存店铺
        int result = wendaoShopService.insertWendaoShop(wendaoShop);
        if (result > 0) {
            log.info("创建试用版店铺成功, creatorId={}, shopId={}, shopName={}, expireTime={}",
                    creatorId, shopId, wendaoShop.getShopName(),
                    new SimpleDateFormat("yyyy-MM-dd").format(wendaoShop.getExpireTime()));
            return AjaxResult.success("店铺创建成功", wendaoShop);
        } else {
            return error("店铺创建失败");
        }
    }

    /**
     * 生成随机店铺ID
     * @return 随机生成的店铺ID，前缀为 app
     */
    private String generateShopId() {
        String CHARACTERS = "abcdefghijklmnopqrstuvwxyz0123456789";
        int SHOP_ID_LENGTH = 12; // 不包含前缀的长度
        SecureRandom random = new SecureRandom();

        StringBuilder shopId = new StringBuilder("app"); // 添加前缀

        // 生成随机字符
        for (int i = 0; i < SHOP_ID_LENGTH; i++) {
            int index = random.nextInt(CHARACTERS.length());
            shopId.append(CHARACTERS.charAt(index));
        }

        return shopId.toString();
    }

    // DTO定义
    @Data
    @Builder
    public static class CreateShopDTO {
        private String shopName;
    }
}
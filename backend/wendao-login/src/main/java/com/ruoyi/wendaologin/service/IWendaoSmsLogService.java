package com.ruoyi.wendaologin.service;

import java.util.List;
import com.ruoyi.wendaologin.domain.WendaoSmsLog;

/**
 * 短信发送日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IWendaoSmsLogService 
{
    /**
     * 查询短信发送日志
     * 
     * @param id 短信发送日志主键
     * @return 短信发送日志
     */
    public WendaoSmsLog selectWendaoSmsLogById(Long id);

    /**
     * 查询短信发送日志列表
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 短信发送日志集合
     */
    public List<WendaoSmsLog> selectWendaoSmsLogList(WendaoSmsLog wendaoSmsLog);

    /**
     * 新增短信发送日志
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 结果
     */
    public int insertWendaoSmsLog(WendaoSmsLog wendaoSmsLog);

    /**
     * 修改短信发送日志
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 结果
     */
    public int updateWendaoSmsLog(WendaoSmsLog wendaoSmsLog);

    /**
     * 批量删除短信发送日志
     * 
     * @param ids 需要删除的短信发送日志主键集合
     * @return 结果
     */
    public int deleteWendaoSmsLogByIds(Long[] ids);

    /**
     * 删除短信发送日志信息
     * 
     * @param id 短信发送日志主键
     * @return 结果
     */
    public int deleteWendaoSmsLogById(Long id);
}

package com.ruoyi.wendaologin.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.wendaologin.mapper.WendaoSmsLogMapper;
import com.ruoyi.wendaologin.domain.WendaoSmsLog;
import com.ruoyi.wendaologin.service.IWendaoSmsLogService;

/**
 * 短信发送日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class WendaoSmsLogServiceImpl implements IWendaoSmsLogService 
{
    @Autowired
    private WendaoSmsLogMapper wendaoSmsLogMapper;

    /**
     * 查询短信发送日志
     * 
     * @param id 短信发送日志主键
     * @return 短信发送日志
     */
    @Override
    public WendaoSmsLog selectWendaoSmsLogById(Long id)
    {
        return wendaoSmsLogMapper.selectWendaoSmsLogById(id);
    }

    /**
     * 查询短信发送日志列表
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 短信发送日志
     */
    @Override
    public List<WendaoSmsLog> selectWendaoSmsLogList(WendaoSmsLog wendaoSmsLog)
    {
        return wendaoSmsLogMapper.selectWendaoSmsLogList(wendaoSmsLog);
    }

    /**
     * 新增短信发送日志
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 结果
     */
    @Override
    public int insertWendaoSmsLog(WendaoSmsLog wendaoSmsLog)
    {
        wendaoSmsLog.setCreateTime(DateUtils.getNowDate());
        return wendaoSmsLogMapper.insertWendaoSmsLog(wendaoSmsLog);
    }

    /**
     * 修改短信发送日志
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 结果
     */
    @Override
    public int updateWendaoSmsLog(WendaoSmsLog wendaoSmsLog)
    {
        wendaoSmsLog.setUpdateTime(DateUtils.getNowDate());
        return wendaoSmsLogMapper.updateWendaoSmsLog(wendaoSmsLog);
    }

    /**
     * 批量删除短信发送日志
     * 
     * @param ids 需要删除的短信发送日志主键
     * @return 结果
     */
    @Override
    public int deleteWendaoSmsLogByIds(Long[] ids)
    {
        return wendaoSmsLogMapper.deleteWendaoSmsLogByIds(ids);
    }

    /**
     * 删除短信发送日志信息
     * 
     * @param id 短信发送日志主键
     * @return 结果
     */
    @Override
    public int deleteWendaoSmsLogById(Long id)
    {
        return wendaoSmsLogMapper.deleteWendaoSmsLogById(id);
    }
}

package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysTrialPermissionMapper;
import com.ruoyi.system.domain.SysTrialPermission;
import com.ruoyi.system.service.ISysTrialPermissionService;

/**
 * 试用权限配置Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysTrialPermissionServiceImpl implements ISysTrialPermissionService 
{
    @Autowired
    private SysTrialPermissionMapper sysTrialPermissionMapper;

    /**
     * 查询试用权限配置
     * 
     * @param id 试用权限配置主键
     * @return 试用权限配置
     */
    @Override
    public SysTrialPermission selectSysTrialPermissionById(Long id)
    {
        return sysTrialPermissionMapper.selectSysTrialPermissionById(id);
    }

    /**
     * 根据权限编码查询试用权限配置
     * 
     * @param permissionCode 权限编码
     * @return 试用权限配置
     */
    @Override
    public SysTrialPermission selectByPermissionCode(String permissionCode)
    {
        return sysTrialPermissionMapper.selectByPermissionCode(permissionCode);
    }

    /**
     * 查询试用权限配置列表
     * 
     * @param sysTrialPermission 试用权限配置
     * @return 试用权限配置
     */
    @Override
    public List<SysTrialPermission> selectSysTrialPermissionList(SysTrialPermission sysTrialPermission)
    {
        return sysTrialPermissionMapper.selectSysTrialPermissionList(sysTrialPermission);
    }

    /**
     * 新增试用权限配置
     * 
     * @param sysTrialPermission 试用权限配置
     * @return 结果
     */
    @Override
    public int insertSysTrialPermission(SysTrialPermission sysTrialPermission)
    {
        return sysTrialPermissionMapper.insertSysTrialPermission(sysTrialPermission);
    }

    /**
     * 修改试用权限配置
     * 
     * @param sysTrialPermission 试用权限配置
     * @return 结果
     */
    @Override
    public int updateSysTrialPermission(SysTrialPermission sysTrialPermission)
    {
        return sysTrialPermissionMapper.updateSysTrialPermission(sysTrialPermission);
    }

    /**
     * 批量删除试用权限配置
     * 
     * @param ids 需要删除的试用权限配置主键
     * @return 结果
     */
    @Override
    public int deleteSysTrialPermissionByIds(Long[] ids)
    {
        return sysTrialPermissionMapper.deleteSysTrialPermissionByIds(ids);
    }

    /**
     * 删除试用权限配置信息
     * 
     * @param id 试用权限配置主键
     * @return 结果
     */
    @Override
    public int deleteSysTrialPermissionById(Long id)
    {
        return sysTrialPermissionMapper.deleteSysTrialPermissionById(id);
    }
} 
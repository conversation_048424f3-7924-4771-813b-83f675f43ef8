package com.ruoyi.wendaologin.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 云片短信状态报告DTO
 */
@Data
public class SmsReportDTO {
    /**
     * 短信ID
     */
    private String sid;
    
    /**
     * 用户自定义ID
     */
    @JSONField(name = "user_receive_time")
    private String userReceiveTime;
    
    /**
     * 短信错误信息
     */
    @JSONField(name = "error_msg")
    private String errorMsg;
    
    /**
     * 短信错误详情
     */
    @JSONField(name = "error_detail")
    private String errorDetail;
    
    /**
     * 短信状态
     */
    @JSONField(name = "report_status")
    private String reportStatus;

    /**
     * 用户自定义ID
     */
    private String uid;

    /**
     * 接收手机号
     */
    private String mobile;
} 
package com.ruoyi.wendaologin.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CaptchaVerifyDTO {
    /**
     * 前端验证完成后返回的票据
     */
    @NotBlank(message = "验证票据不能为空")
    private String ticket;
    
    /**
     * 前端验证完成后返回的随机字符串
     */
    @NotBlank(message = "随机字符串不能为空")
    private String randstr;
    
    /**
     * 用户操作来源的外网 IP
     */
    private String userIp;
} 
package com.ruoyi.framework.web.service;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Date;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.ISysTrialPermissionService;

/**
 * 试用权限验证服务单元测试
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TrialPermissionServiceTest {

    @Mock
    private ISysTenantService tenantService;

    @Mock
    private ISysTrialPermissionService trialPermissionService;

    @Mock
    private RedisCache redisCache;

    @InjectMocks
    private TrialPermissionService trialPermissionServiceImpl;

    private SysTenant tenant;

    @Before
    public void setUp() {
        // 设置租户信息
        tenant = new SysTenant();
        tenant.setShopId("tenant001");
        tenant.setTenantName("测试租户");
        tenant.setTrialStatus(1); // 试用中
        tenant.setTrialStartTime(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000L)); // 昨天开始
        tenant.setTrialEndTime(new Date(System.currentTimeMillis() + 6 * 24 * 60 * 60 * 1000L)); // 6天后结束
        tenant.setTrialDays(7);
    }

    @Test
    public void testStartTrial_Success() {
        // Given
        String tenantId = "tenant001";
        int trialDays = 7;
        
        SysTenant existingTenant = new SysTenant();
        existingTenant.setShopId(tenantId);
        existingTenant.setTrialStatus(0); // 未试用
        
        when(tenantService.selectTenantById(tenantId)).thenReturn(existingTenant);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(1);

        // When
        boolean result = trialPermissionServiceImpl.startTrial(tenantId, trialDays);

        // Then
        assertTrue(result);
        verify(tenantService).updateTenant(argThat(tenant -> 
            tenant.getTrialStatus().equals(1) && 
            tenant.getTrialDays().equals(trialDays) &&
            tenant.getTrialStartTime() != null &&
            tenant.getTrialEndTime() != null
        ));
        verify(redisCache).deleteObject("trial:tenant:status:" + tenantId);
    }

    @Test
    public void testStartTrial_TenantNotFound_Failed() {
        // Given
        String tenantId = "nonexistent";
        int trialDays = 7;
        
        when(tenantService.selectTenantById(tenantId)).thenReturn(null);

        // When
        boolean result = trialPermissionServiceImpl.startTrial(tenantId, trialDays);

        // Then
        assertFalse(result);
        verify(tenantService, never()).updateTenant(any());
    }

    @Test
    public void testStartTrial_UpdateFailed() {
        // Given
        String tenantId = "tenant001";
        int trialDays = 7;
        
        SysTenant existingTenant = new SysTenant();
        existingTenant.setShopId(tenantId);
        existingTenant.setTrialStatus(0);
        
        when(tenantService.selectTenantById(tenantId)).thenReturn(existingTenant);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(0); // 更新失败

        // When
        boolean result = trialPermissionServiceImpl.startTrial(tenantId, trialDays);

        // Then
        assertFalse(result);
        verify(tenantService).updateTenant(any(SysTenant.class));
        verify(redisCache, never()).deleteObject(anyString());
    }

    @Test
    public void testConvertTenant_Success() {
        // Given
        String tenantId = "tenant001";
        
        SysTenant existingTenant = new SysTenant();
        existingTenant.setShopId(tenantId);
        existingTenant.setTrialStatus(2); // 试用过期
        
        when(tenantService.selectTenantById(tenantId)).thenReturn(existingTenant);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(1);

        // When
        boolean result = trialPermissionServiceImpl.convertTenant(tenantId);

        // Then
        assertTrue(result);
        verify(tenantService).updateTenant(argThat(tenant -> 
            tenant.getTrialStatus().equals(3) // 已转正
        ));
        verify(redisCache).deleteObject("trial:tenant:status:" + tenantId);
    }

    @Test
    public void testConvertTenant_TenantNotFound_Failed() {
        // Given
        String tenantId = "nonexistent";
        
        when(tenantService.selectTenantById(tenantId)).thenReturn(null);

        // When
        boolean result = trialPermissionServiceImpl.convertTenant(tenantId);

        // Then
        assertFalse(result);
        verify(tenantService, never()).updateTenant(any());
    }

    @Test
    public void testConvertTenant_UpdateFailed() {
        // Given
        String tenantId = "tenant001";
        
        SysTenant existingTenant = new SysTenant();
        existingTenant.setShopId(tenantId);
        existingTenant.setTrialStatus(2);
        
        when(tenantService.selectTenantById(tenantId)).thenReturn(existingTenant);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(0); // 更新失败

        // When
        boolean result = trialPermissionServiceImpl.convertTenant(tenantId);

        // Then
        assertFalse(result);
        verify(tenantService).updateTenant(any(SysTenant.class));
        verify(redisCache, never()).deleteObject(anyString());
    }

    @Test
    public void testClearTenantTrialCache() {
        // Given
        String tenantId = "tenant001";

        // When
        trialPermissionServiceImpl.clearTenantTrialCache(tenantId);

        // Then
        verify(redisCache).deleteObject("trial:tenant:status:" + tenantId);
    }

    @Test
    public void testClearPermissionCache() {
        // Given
        String permission = "system:user:list";

        // When
        trialPermissionServiceImpl.clearPermissionCache(permission);

        // Then
        verify(redisCache).deleteObject("trial:permission:" + permission);
    }

    @Test
    public void testStartTrial_AlreadyInTrial() {
        // Given
        String tenantId = "tenant001";
        int trialDays = 7;
        
        SysTenant existingTenant = new SysTenant();
        existingTenant.setShopId(tenantId);
        existingTenant.setTrialStatus(1); // 已经在试用中
        
        when(tenantService.selectTenantById(tenantId)).thenReturn(existingTenant);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(1);

        // When
        boolean result = trialPermissionServiceImpl.startTrial(tenantId, trialDays);

        // Then
        assertTrue(result); // 应该允许重新开始试用
        verify(tenantService).updateTenant(any(SysTenant.class));
    }

    @Test
    public void testConvertTenant_AlreadyConverted() {
        // Given
        String tenantId = "tenant001";
        
        SysTenant existingTenant = new SysTenant();
        existingTenant.setShopId(tenantId);
        existingTenant.setTrialStatus(3); // 已经转正
        
        when(tenantService.selectTenantById(tenantId)).thenReturn(existingTenant);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(1);

        // When
        boolean result = trialPermissionServiceImpl.convertTenant(tenantId);

        // Then
        assertTrue(result); // 应该允许重复转正操作
        verify(tenantService).updateTenant(any(SysTenant.class));
    }
}
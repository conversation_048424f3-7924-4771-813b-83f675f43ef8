# 多租户试用系统设计方案

## 1. 概述

本试用系统基于现有的RBAC权限模型，通过扩展实现了试用期权限控制。系统支持以下功能：

- **试用期管理**：支持设置试用开始时间、结束时间和试用天数
- **权限分级控制**：区分读权限和写权限，试用过期后保留读权限，限制写权限
- **灵活配置**：支持针对不同权限配置不同的试用策略
- **自动过期检查**：通过定时任务自动检查和更新试用状态
- **缓存优化**：使用Redis缓存提高权限检查性能

## 2. 核心设计理念

### 2.1 权限分类
- **读权限（type=1）**：查询、导出、查看等操作，试用过期后仍可使用
- **写权限（type=2）**：新增、修改、删除等操作，试用过期后被限制

### 2.2 试用状态
- **0 - 未试用**：租户尚未开始试用
- **1 - 试用中**：租户正在试用期内
- **2 - 试用过期**：试用期已结束，写权限被限制
- **3 - 已转正**：租户已升级为正式用户，拥有全部权限

## 3. 数据库设计

### 3.1 租户表扩展（sys_tenant）
```sql
ALTER TABLE sys_tenant ADD COLUMN trial_start_time datetime COMMENT '试用开始时间';
ALTER TABLE sys_tenant ADD COLUMN trial_end_time datetime COMMENT '试用结束时间';
ALTER TABLE sys_tenant ADD COLUMN trial_status tinyint(1) DEFAULT 0 COMMENT '试用状态';
ALTER TABLE sys_tenant ADD COLUMN trial_days int DEFAULT 7 COMMENT '试用天数';
```

### 3.2 试用权限配置表（sys_trial_permission）
```sql
CREATE TABLE sys_trial_permission (
  id bigint(20) NOT NULL AUTO_INCREMENT,
  permission_code varchar(100) NOT NULL COMMENT '权限编码',
  permission_name varchar(100) NOT NULL COMMENT '权限名称',
  permission_type tinyint(1) NOT NULL DEFAULT 1 COMMENT '权限类型：1-读权限，2-写权限',
  trial_available tinyint(1) NOT NULL DEFAULT 1 COMMENT '试用期是否可用',
  expired_available tinyint(1) NOT NULL DEFAULT 0 COMMENT '试用过期后是否可用',
  PRIMARY KEY (id),
  UNIQUE KEY uk_permission_code (permission_code)
);
```

## 4. 使用方法

### 4.1 在控制器中使用试用权限

#### 方法一：使用注解（推荐）
```java
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    
    // 读权限：试用期和过期后都可用
    @PreAuthorize("@trialPermission.hasTrialPermi('system:user:list')")
    @TrialPermission(value = "system:user:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        // 业务逻辑
    }
    
    // 写权限：试用期可用，过期后不可用
    @PreAuthorize("@trialPermission.hasTrialPermi('system:user:add')")
    @TrialPermission(value = "system:user:add", type = 2, trialAvailable = true, expiredAvailable = false)
    @PostMapping
    public AjaxResult add(@RequestBody SysUser user) {
        // 业务逻辑
    }
}
```

#### 方法二：直接调用服务
```java
@Autowired
private TrialPermissionService trialPermissionService;

public AjaxResult someMethod() {
    if (!trialPermissionService.hasTrialPermi("system:user:add")) {
        return error("试用期已过期，写操作权限已被限制");
    }
    // 业务逻辑
}
```

### 4.2 试用管理操作

#### 开始试用
```java
// 开始7天试用
trialPermissionService.startTrial("tenant001", 7);
```

#### 租户转正
```java
// 租户转正，获得全部权限
trialPermissionService.convertTenant("tenant001");
```

#### 清除缓存
```java
// 清除租户试用状态缓存
trialPermissionService.clearTenantTrialCache("tenant001");
```

### 4.3 定时任务配置

在系统定时任务中添加以下配置：

```
# 每小时检查一次试用过期状态
0 0 * * * ? trialStatusTask.checkTrialExpiredStatus

# 每天上午9点检查试用到期提醒
0 0 9 * * ? trialStatusTask.trialExpirationReminder
```

## 5. 权限配置示例

### 5.1 默认权限配置

系统已预置以下权限配置：

**读权限（试用期和过期后都可用）：**
- system:user:list - 用户查询
- system:role:list - 角色查询
- system:menu:list - 菜单查询
- system:dept:list - 部门查询

**写权限（试用期可用，过期后不可用）：**
- system:user:add - 用户新增
- system:user:edit - 用户修改
- system:user:remove - 用户删除
- system:role:add - 角色新增

### 5.2 自定义权限配置

可以通过管理界面或API添加自定义权限配置：

```java
SysTrialPermission permission = new SysTrialPermission();
permission.setPermissionCode("custom:feature:add");
permission.setPermissionName("自定义功能新增");
permission.setPermissionType(2); // 写权限
permission.setTrialAvailable(1); // 试用期可用
permission.setExpiredAvailable(0); // 过期后不可用
trialPermissionService.insertSysTrialPermission(permission);
```

## 6. 前端集成

### 6.1 权限检查指令

在前端可以使用权限检查指令：

```javascript
// 检查试用权限
if (this.$auth.hasTrialPermi('system:user:add')) {
    // 显示新增按钮
}

// 检查多个权限
if (this.$auth.hasAnyTrialPermi(['system:user:add', 'system:user:edit'])) {
    // 显示操作按钮
}
```

### 6.2 试用状态显示

```vue
<template>
  <div class="trial-status">
    <el-alert 
      v-if="trialStatus === 1" 
      title="当前为试用版本" 
      type="warning" 
      :description="`试用期剩余 ${remainingDays} 天`"
      show-icon>
    </el-alert>
    
    <el-alert 
      v-if="trialStatus === 2" 
      title="试用期已过期" 
      type="error" 
      description="部分功能已被限制，请联系管理员升级账户"
      show-icon>
    </el-alert>
  </div>
</template>
```

## 7. 最佳实践

### 7.1 权限设计原则

1. **读写分离**：明确区分读权限和写权限
2. **渐进限制**：试用过期后保留基本的查看功能
3. **用户友好**：提供清晰的权限限制提示信息
4. **灵活配置**：支持针对不同功能模块的个性化配置

### 7.2 性能优化

1. **缓存策略**：租户状态缓存5分钟，权限配置缓存30分钟
2. **批量处理**：定时任务批量检查和更新过期状态
3. **索引优化**：在试用相关字段上建立合适的索引

### 7.3 监控和告警

1. **试用状态监控**：监控试用租户数量和过期情况
2. **权限检查性能**：监控权限检查的响应时间
3. **异常告警**：权限检查失败时的告警机制

## 8. 故障排查

### 8.1 常见问题

**问题1：权限检查失败**
- 检查租户试用状态是否正确
- 检查权限配置是否存在
- 检查缓存是否正常

**问题2：试用状态未及时更新**
- 检查定时任务是否正常运行
- 手动执行状态检查任务
- 清除相关缓存

**问题3：性能问题**
- 检查Redis缓存是否正常
- 优化权限检查逻辑
- 调整缓存过期时间

### 8.2 调试工具

```java
// 查看租户试用状态
GET /system/trial/tenant/{tenantId}

// 清除试用缓存
DELETE /system/trial/cache/{tenantId}

// 查看权限配置
GET /system/trial/permission/list
```

## 9. 扩展功能

### 9.1 试用功能限制

可以扩展实现更细粒度的功能限制：

```java
@TrialPermission(value = "system:user:add", type = 2, maxCount = 10)
public AjaxResult add(@RequestBody SysUser user) {
    // 试用期最多只能添加10个用户
}
```

### 9.2 试用数据隔离

可以扩展实现试用期数据的特殊处理：

```java
public class TrialDataService {
    public void markAsTrialData(Object data) {
        // 标记为试用期数据
    }
    
    public void cleanTrialData(String tenantId) {
        // 清理试用期数据
    }
}
```

## 10. 总结

本试用系统通过以下方式解决了您提出的需求：

1. **试用期间**：用户拥有完整的读写权限
2. **试用过期后**：保留读权限，限制写权限
3. **灵活配置**：支持针对不同权限的个性化配置
4. **自动管理**：通过定时任务自动检查和更新试用状态
5. **性能优化**：使用缓存机制提高权限检查效率

该方案在保持现有RBAC模型不变的基础上，通过扩展实现了试用期权限控制，满足了多租户系统的试用需求。 
package com.ruoyi.framework.interceptor;

import com.ruoyi.common.context.TenantContext;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

class TenantScopeInterceptorTest {

    private TenantScopeInterceptor interceptor;

    @Mock
    private StatementHandler statementHandler;

    @Mock
    private BoundSql boundSql;

    private AutoCloseable mocks;

    @BeforeEach
    void setUp() {
        mocks = MockitoAnnotations.openMocks(this);
        interceptor = new TenantScopeInterceptor();
        TenantContext.setShopId("1");
    }

    @AfterEach
    void tearDown() throws Exception {
        TenantContext.clear();
        if (mocks != null) {
            mocks.close();
        }
    }

    @Test
    void testSimpleSelect() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user";
        System.out.println("\n========== testSimpleSelect START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);
        assertTrue(modifiedSql.contains("where shop_id = '1'"));
        System.out.println("========== testSimpleSelect END ==========\n");
    }

    @Test
    void testSelectWithWhere() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user WHERE user_name = 'admin'";
        System.out.println("\n========== testSelectWithWhere START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);
        assertTrue(modifiedSql.contains("where"));
        assertTrue(modifiedSql.contains("shop_id = '1'"));
        assertTrue(modifiedSql.contains("user_name = 'admin'"));
        System.out.println("========== testSelectWithWhere END ==========\n");
    }

    @Test
    void testJoinSelect() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT u.*, d.dept_name FROM sys_user u LEFT JOIN sys_dept d ON u.dept_id = d.dept_id";
        System.out.println("\n========== testJoinSelect START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);
        assertTrue(modifiedSql.contains("shop_id = '1'"));
        System.out.println("========== testJoinSelect END ==========\n");
    }

    @Test
    void testInsert() throws Throwable {
        // 准备测试数据
        String originalSql = "INSERT INTO sys_user (user_name, nick_name) VALUES ('test', 'Test User')";
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        if (statement instanceof Insert) {
            Insert insert = (Insert) statement;
            System.out.println("Before modification - Columns: " + insert.getColumns());
            System.out.println("Before modification - Values: " + insert.getItemsList());

            interceptor.handleInsert(insert, "1");

            System.out.println("After modification - Columns: " + insert.getColumns());
            System.out.println("After modification - Values: " + insert.getItemsList());

            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("Modified SQL: " + modifiedSql);

            // 验证SQL包含shop_id列
            assertTrue(modifiedSql.contains("shop_id"), "SQL should contain shop_id column");

            // 验证SQL包含租户ID值
            assertTrue(modifiedSql.contains("1"), "SQL should contain tenant ID value");

            // 验证完整的SQL结构
            assertTrue(modifiedSql.matches(".*insert into sys_user \\(.*shop_id.*\\) values \\(.*1.*\\).*"),
                    "SQL should have correct structure with shop_id");
        } else {
            fail("Expected INSERT statement, but got: " + statement.getClass().getName());
        }
    }

    @Test
    void testBatchInsert() throws Throwable {
        // 准备测试数据
        String originalSql = "INSERT INTO sys_user (user_name, nick_name) VALUES ('test1', 'Test User1'), ('test2', 'Test User2')";
        System.out.println("\n========== testBatchInsert START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        if (statement instanceof Insert) {
            interceptor.handleInsert((Insert) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("Modified SQL: " + modifiedSql);
            assertTrue(modifiedSql.contains("shop_id"));
            assertEquals("insert into sys_user (user_name, nick_name, shop_id) values ('test1', 'test user1', '1'), ('test2', 'test user2', '1')", modifiedSql);
        } else {
            fail("Expected INSERT statement");
        }
        System.out.println("========== testBatchInsert END ==========\n");
    }

    @Test
    void testUpdate() throws Throwable {
        // 准备测试数据
        String originalSql = "UPDATE sys_user SET nick_name = 'New Name' WHERE user_id = 1";
        System.out.println("\n========== testUpdate START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        if (statement instanceof Update) {
            interceptor.handleUpdate((Update) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("Modified SQL: " + modifiedSql);
            assertTrue(modifiedSql.contains("shop_id = '1'"));
        } else {
            fail("Expected UPDATE statement");
        }
        System.out.println("========== testUpdate END ==========\n");
    }

    @Test
    void testDelete() throws Throwable {
        // 准备测试数据
        String originalSql = "DELETE FROM sys_user WHERE user_id = 1";
        System.out.println("\n========== testDelete START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        if (statement instanceof Delete) {
            interceptor.handleDelete((Delete) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("Modified SQL: " + modifiedSql);
            assertTrue(modifiedSql.contains("shop_id = '1'"));
        } else {
            fail("Expected DELETE statement");
        }
        System.out.println("========== testDelete END ==========\n");
    }

    @Test
    void testIgnoreTable() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_tenant";
        System.out.println("\n========== testIgnoreTable START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL没有被修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);
        assertFalse(modifiedSql.contains("shop_id = '1'"));
        System.out.println("========== testIgnoreTable END ==========\n");
    }

    @Test
    void testSubquery() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user WHERE dept_id IN (SELECT dept_id FROM sys_dept)";
        System.out.println("\n========== testSubquery START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 设置Mock对象行为
        when(boundSql.getSql()).thenReturn(originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);
        assertTrue(modifiedSql.contains("shop_id = '1'"));
        System.out.println("========== testSubquery END ==========\n");
    }

    @Test
    void testUnionSelect() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user WHERE dept_id = 1 UNION SELECT * FROM sys_user WHERE user_id = 2";

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("modifiedSql:" + modifiedSql);

        // 验证两个SELECT语句都添加了租户条件
        assertTrue(modifiedSql.contains("shop_id = '1'"));

        // 验证UNION前后的查询都有租户条件
        String[] parts = modifiedSql.split("union");
        assertEquals(2, parts.length, "SQL should be split by UNION into 2 parts");
        assertTrue(parts[0].contains("shop_id = '1'"), "First SELECT should have shop_id condition");
        assertTrue(parts[1].contains("shop_id = '1'"), "Second SELECT should have shop_id condition");
    }

    @Test
    void testWithClause() throws Throwable {
        // 准备测试数据
        String originalSql = "WITH dept_users AS (SELECT * FROM sys_user WHERE dept_id = 1) SELECT * FROM dept_users";

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("modifiedSql:" + modifiedSql);

        // 验证WITH子句和主查询都添加了租户条件
        assertTrue(modifiedSql.contains("shop_id = '1'"), "SQL should contain shop_id condition");

        // 验证WITH子句中的查询有租户条件
        assertTrue(modifiedSql.contains("where dept_id = 1 and shop_id = '1'"),
                "WITH clause should have shop_id condition");

        // 验证主查询有租户条件
        assertTrue(modifiedSql.contains("from dept_users where shop_id = '1'"),
                "Main query should have shop_id condition");
    }

    @Test
    void testInsertSelect() throws Throwable {
        // 准备测试数据
        String originalSql = "INSERT INTO sys_user (user_name, nick_name) SELECT user_name, nick_name FROM sys_user_temp WHERE dept_id = 1";
        System.out.println("\n========== testInsertSelect START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        if (statement instanceof Insert) {
            interceptor.handleInsert((Insert) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("Modified SQL: " + modifiedSql);

            // 验证INSERT和SELECT都添加了租户条件
            assertTrue(modifiedSql.contains("shop_id"));
            assertTrue(modifiedSql.contains("shop_id = '1'"));
        } else {
            fail("Expected INSERT statement");
        }
        System.out.println("========== testInsertSelect END ==========\n");
    }

    @Test
    void testComplexJoin() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT u.*, d.dept_name, r.role_name " +
                "FROM sys_user u " +
                "LEFT JOIN sys_dept d ON u.dept_id = d.dept_id " +
                "LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id " +
                "LEFT JOIN sys_role r ON ur.role_id = r.role_id";
        System.out.println("\n========== testComplexJoin START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证所有相关表都添加了租户条件
        assertTrue(modifiedSql.contains("shop_id = '1'"));
        // 验证每个需要过滤的表都有租户条件
        assertTrue(modifiedSql.contains("sys_user") && modifiedSql.contains("sys_dept") &&
                modifiedSql.contains("sys_role"));
        System.out.println("========== testComplexJoin END ==========\n");
    }

    @Test
    void testUpdateWithSubquery() throws Throwable {
        // 准备测试数据
        String originalSql = "UPDATE sys_user SET dept_id = (SELECT dept_id FROM sys_dept WHERE dept_name = 'IT')";
        System.out.println("\n========== testUpdateWithSubquery START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        if (statement instanceof Update) {
            interceptor.handleUpdate((Update) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("Modified SQL: " + modifiedSql);

            // 验证UPDATE语句有租户条件
            assertTrue(modifiedSql.contains("where shop_id = '1'"),
                    "UPDATE statement should have shop_id condition");

            // 验证子查询中有租户条件
            assertTrue(modifiedSql.contains("where dept_name = 'it' and shop_id = '1'"),
                    "Subquery should have shop_id condition");
        }
        System.out.println("========== testUpdateWithSubquery END ==========\n");
    }

    @Test
    void testDeleteWithSubquery() throws Throwable {
        // 准备测试数据
        String originalSql = "DELETE FROM sys_user WHERE dept_id IN (SELECT dept_id FROM sys_dept WHERE status = 0)";

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        if (statement instanceof Delete) {
            interceptor.handleDelete((Delete) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("modifiedSql:" + modifiedSql);

            // 验证DELETE语句有租户条件
            assertTrue(modifiedSql.contains("and shop_id = '1'"),
                    "DELETE statement should have shop_id condition");

            // 验证子查询中有租户条件
            assertTrue(modifiedSql.contains("where status = 0 and shop_id = '1'"),
                    "Subquery should have shop_id condition");
        } else {
            fail("Expected DELETE statement");
        }
    }

    @Test
    void testGroupByAndHaving() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT dept_id, COUNT(*) as count FROM sys_user GROUP BY dept_id HAVING COUNT(*) > 5";

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        // 验证租户条件被正确添加，且不影响GROUP BY和HAVING
        assertTrue(modifiedSql.contains("where shop_id = '1'"));
        assertTrue(modifiedSql.contains("group by dept_id"));
        assertTrue(modifiedSql.contains("having count(*) > 5"));
    }

    @Test
    void testOrderByAndLimit() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user ORDER BY user_id DESC LIMIT 10";

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        // 验证租户条件被正确添加，且不影响ORDER BY和LIMIT
        assertTrue(modifiedSql.contains("shop_id = '1'"),
                "SQL should contain shop_id condition with table prefix");
        assertTrue(modifiedSql.contains("order by user_id desc"),
                "SQL should contain ORDER BY clause");
        assertTrue(modifiedSql.contains("limit 10"),
                "SQL should contain LIMIT clause");
    }

    @Test
    void testNestedSubquery() throws Throwable {
        System.out.println("\n========== testNestedSubquery START ==========");
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user WHERE dept_id IN " +
                "(SELECT dept_id FROM sys_dept WHERE parent_id IN " +
                "(SELECT dept_id FROM sys_dept WHERE dept_name = 'IT'))";
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证每层子查询都添加了租户条件
        // 1. 主查询的租户条件
        assertTrue(modifiedSql.matches(".*select \\* from sys_user where.*shop_id = '1'.*"),
                "Main query should have shop_id condition");

        // 2. 第一层子查询的租户条件
        assertTrue(modifiedSql.matches(".*select dept_id from sys_dept where.*shop_id = '1'.*"),
                "First level subquery should have shop_id condition");

        // 3. 第二层子查询的租户条件
        assertTrue(modifiedSql.matches(".*select dept_id from sys_dept where dept_name = 'it'.*shop_id = '1'.*"),
                "Second level subquery should have shop_id condition");

        // 确保总共有3个租户条件
        int tenantConditionCount = modifiedSql.split("shop_id = '1'").length;
        System.out.println("Number of shop_id conditions found: " + tenantConditionCount);
        System.out.println("Expected number of shop_id conditions: 3");

        assertEquals(3, tenantConditionCount,
                "Should have exactly three shop_id conditions (main query and two subqueries)");
        System.out.println("========== testNestedSubquery END ==========\n");
    }

    @Test
    void testSubqueryInFunction() throws Throwable {
        System.out.println("\n========== testSubqueryInFunction START ==========");
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user WHERE " +
                "dept_id = COALESCE((SELECT dept_id FROM sys_dept WHERE dept_name = 'IT'), 0)";
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证主查询和函数中的子查询都添加了租户条件
        int tenantConditionCount = modifiedSql.split("shop_id = '1'").length;
        System.out.println("Number of shop_id conditions found: " + tenantConditionCount);
        System.out.println("Expected number of shop_id conditions: 2");

        assertEquals(2, tenantConditionCount,
                "Should have shop_id condition in main query and function subquery");
        System.out.println("========== testSubqueryInFunction END ==========\n");
    }

    @Test
    void testExistsClause() throws Throwable {
        System.out.println("\n========== testExistsClause START ==========");
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user u WHERE EXISTS " +
                "(SELECT 1 FROM sys_dept d WHERE d.dept_id = u.dept_id)";
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证主查询和EXISTS子查询都添加了租户条件
        int tenantConditionCount = modifiedSql.split("shop_id = '1'").length;
        System.out.println("Number of shop_id conditions found: " + tenantConditionCount);
        System.out.println("Expected number of shop_id conditions: 2");

        assertEquals(2, tenantConditionCount,
                "Should have shop_id condition in main query and EXISTS subquery");
        System.out.println("========== testExistsClause END ==========\n");
    }

    @Test
    void testJoinUsing() throws Throwable {
        System.out.println("\n========== testJoinUsing START ==========");
        // 准备测试数据
        String originalSql = "SELECT * FROM sys_user JOIN sys_dept USING (dept_id)";
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证JOIN的表都添加了租户条件
        System.out.println("Checking for shop_id conditions...");
        assertTrue(modifiedSql.contains("shop_id = '1'"), "SQL should contain shop_id condition");

        // 检查每个表的租户条件
        assertTrue(modifiedSql.contains("sys_user.shop_id = '1'"),
                "sys_user should have shop_id condition");
        assertTrue(modifiedSql.contains("sys_dept.shop_id = '1'"),
                "sys_dept should have shop_id condition");
        System.out.println("========== testJoinUsing END ==========\n");
    }

    @Test
    void testTableAlias() throws Throwable {
        System.out.println("\n========== testTableAlias START ==========");
        // 准备测试数据
        String originalSql = "SELECT u.* FROM sys_user u WHERE u.user_name = 'admin'";
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证使用了别名的租户条件
        System.out.println("Checking for shop_id condition with alias...");
        assertTrue(modifiedSql.contains("u.shop_id = '1'"),
                "SQL should contain shop_id condition with table alias");
        System.out.println("========== testTableAlias END ==========\n");
    }

    @Test
    void testTemporaryTable() throws Throwable {
        System.out.println("\n========== testTemporaryTable START ==========");
        // 准备测试数据
        String originalSql = "WITH RECURSIVE temp_table AS (" +
                "SELECT * FROM sys_dept WHERE parent_id IS NULL " +
                "UNION ALL " +
                "SELECT d.* FROM sys_dept d, temp_table t WHERE d.parent_id = t.dept_id" +
                ") SELECT * FROM temp_table";
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证WITH子句和主查询都添加了租户条件
        System.out.println("Checking for shop_id conditions...");
        assertTrue(modifiedSql.contains("shop_id = '1'"),
                "SQL should contain shop_id condition");

        // WITH RECURSIVE中的UNION ALL两边都应该有租户条件
        int tenantConditionCount = modifiedSql.split("shop_id = '1'").length;
        System.out.println("Number of shop_id conditions found: " + tenantConditionCount);
        System.out.println("Expected number of shop_id conditions: 3");

        assertEquals(3, tenantConditionCount,
                "Should have shop_id condition in both parts of UNION ALL and main query");
        System.out.println("========== testTemporaryTable END ==========\n");
    }

    @Test
    void testCaseWhenSubquery() throws Throwable {
        System.out.println("\n========== testCaseWhenSubquery START ==========");
        // 准备测试数据
        String originalSql = "SELECT user_id, " +
                "CASE WHEN dept_id IN (SELECT dept_id FROM sys_dept WHERE status = 1) " +
                "THEN 'Active' ELSE 'Inactive' END as status " +
                "FROM sys_user";
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证主查询和CASE WHEN中的子查询都添加了租户条件
        // 1. 验证主查询有租户条件
        assertTrue(modifiedSql.matches(".*from sys_user where.*shop_id = '1'.*"),
                "Main query should have shop_id condition");

        // 2. 验证子查询有租户条件
        assertTrue(modifiedSql.matches(".*from sys_dept where status = 1.*shop_id = '1'.*"),
                "Subquery should have shop_id condition");

        // 确保总共有2个租户条件
        int tenantConditionCount = modifiedSql.split("shop_id = '1'").length;
        System.out.println("Number of shop_id conditions found: " + tenantConditionCount);
        System.out.println("Expected number of shop_id conditions: 2");

        assertEquals(2, tenantConditionCount,
                "Should have shop_id condition in main query and subquery");
        System.out.println("========== testCaseWhenSubquery END ==========\n");
    }

    @Test
    void testRightJoin() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT u.*, d.dept_name FROM sys_user u RIGHT JOIN sys_dept d ON u.dept_id = d.dept_id";

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("testRightJoin modifiedSql: " + modifiedSql);

        // 验证租户条件被正确添加到两个表
        assertTrue(modifiedSql.contains("u.shop_id = '1'"),
                "sys_user should have shop_id condition");
        assertTrue(modifiedSql.contains("d.shop_id = '1'"),
                "sys_dept should have shop_id condition");
    }

    @Test
    void testInnerJoin() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT u.*, d.dept_name FROM sys_user u INNER JOIN sys_dept d ON u.dept_id = d.dept_id";

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("testInnerJoin modifiedSql: " + modifiedSql);

        // 验证租户条件被正确添加到两个表
        assertTrue(modifiedSql.contains("u.shop_id = '1'"),
                "sys_user should have shop_id condition");
        assertTrue(modifiedSql.contains("d.shop_id = '1'"),
                "sys_dept should have shop_id condition");
    }

    @Test
    void testLeftJoin() throws Throwable {
        System.out.println("\n========== testLeftJoin START ==========");
        String originalSql = "SELECT u.*, d.dept_name FROM sys_user u LEFT JOIN sys_dept d ON u.dept_id = d.dept_id";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("Modified SQL: " + modifiedSql);

        assertTrue(modifiedSql.contains("u.shop_id = '1'"),
                "sys_user should have shop_id condition");
        assertTrue(modifiedSql.contains("d.shop_id = '1'"),
                "sys_dept should have shop_id condition");
        System.out.println("========== testLeftJoin END ==========\n");
    }

    @Test
    void testCrossJoin() throws Throwable {
        System.out.println("\n========== testCrossJoin START ==========");
        String originalSql = "SELECT u.*, d.dept_name FROM sys_user u CROSS JOIN sys_dept d";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("Modified SQL: " + modifiedSql);

        assertTrue(modifiedSql.contains("u.shop_id = '1'"),
                "sys_user should have shop_id condition");
        assertTrue(modifiedSql.contains("d.shop_id = '1'"),
                "sys_dept should have shop_id condition");
        System.out.println("========== testCrossJoin END ==========\n");
    }

    @Test
    void testGroupByWithAggregation() throws Throwable {
        System.out.println("\n========== testGroupByWithAggregation START ==========");
        String originalSql = "SELECT dept_id, COUNT(*) as count, MAX(create_time) as latest FROM sys_user GROUP BY dept_id HAVING COUNT(*) > 5";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("Modified SQL: " + modifiedSql);

        assertTrue(modifiedSql.contains("shop_id = '1'"),
                "Should have shop_id condition");
        assertTrue(modifiedSql.contains("group by dept_id"),
                "Should preserve GROUP BY clause");
        assertTrue(modifiedSql.contains("having count(*) > 5"),
                "Should preserve HAVING clause");
        System.out.println("========== testGroupByWithAggregation END ==========\n");
    }

    @Test
    void testSetOperations() throws Throwable {
        System.out.println("\n========== testSetOperations START ==========");
        String originalSql = "SELECT * FROM sys_user WHERE dept_id = 1 UNION SELECT * FROM sys_user WHERE user_id = 2 " +
                "INTERSECT SELECT * FROM sys_user WHERE status = 0";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("Modified SQL: " + modifiedSql);

        assertEquals(3, modifiedSql.split("shop_id = '1'").length,
                "Should have shop_id condition in all three SELECT statements");
        System.out.println("========== testSetOperations END ==========\n");
    }

    @Test
    void testPagination() throws Throwable {
        System.out.println("\n========== testPagination START ==========");
        String originalSql = "SELECT * FROM sys_user ORDER BY create_time DESC LIMIT 10 OFFSET 20";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("Modified SQL: " + modifiedSql);

        assertTrue(modifiedSql.contains("shop_id = '1'"),
                "Should have shop_id condition");
        assertTrue(modifiedSql.contains("order by create_time desc"),
                "Should preserve ORDER BY clause");
        assertTrue(modifiedSql.contains("limit 10"),
                "Should preserve LIMIT clause");
        assertTrue(modifiedSql.contains("offset 20"),
                "Should preserve OFFSET clause");
        System.out.println("========== testPagination END ==========\n");
    }

    @Test
    void testInsertWithSubquery() throws Throwable {
        System.out.println("\n========== testInsertWithSubquery START ==========");
        String originalSql = "INSERT INTO sys_user (user_name, dept_id) SELECT user_name, dept_id FROM sys_user_temp WHERE status = 1";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        if (statement instanceof Insert) {
            interceptor.handleInsert((Insert) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();

            System.out.println("Modified SQL: " + modifiedSql);

            assertTrue(modifiedSql.contains("shop_id"),
                    "Should add shop_id column");
            assertTrue(modifiedSql.contains("shop_id = '1'"),
                    "Should add shop_id condition in subquery");
        }
        System.out.println("========== testInsertWithSubquery END ==========\n");
    }

    @Test
    void testNullTenantId() throws Throwable {
        System.out.println("\n========== testNullTenantId START ==========");
        TenantContext.clear(); // 确保租户ID为空
        String originalSql = "SELECT * FROM sys_user";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, null);
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("Modified SQL: " + modifiedSql);

        assertEquals(originalSql.toLowerCase(), modifiedSql,
                "SQL should not be modified when shop_id is null");
        System.out.println("========== testNullTenantId END ==========\n");
    }

    @Test
    void testIgnoreTableWithJoin() throws Throwable {
        System.out.println("\n========== testIgnoreTableWithJoin START ==========");
        String originalSql = "SELECT t.*, u.user_name FROM sys_tenant t LEFT JOIN sys_user u ON t.shop_id = u.shop_id";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("Modified SQL: " + modifiedSql);

        assertFalse(modifiedSql.contains("t.shop_id = '1'"),
                "Should not add tenant condition to ignored table");
        assertTrue(modifiedSql.contains("u.shop_id = '1'"),
                "Should add tenant condition to non-ignored table");
        System.out.println("========== testIgnoreTableWithJoin END ==========\n");
    }

    @Test
    void testFunctionAndExpression() throws Throwable {
        System.out.println("\n========== testFunctionAndExpression START ==========");
        String originalSql = "SELECT u.*, CONCAT(u.user_name, d.dept_name) as full_name, " +
                "CASE WHEN u.status = 0 THEN 'inactive' ELSE 'active' END as status " +
                "FROM sys_user u LEFT JOIN sys_dept d ON u.dept_id = d.dept_id " +
                "WHERE UPPER(u.user_name) LIKE '%ADMIN%'";
        System.out.println("Original SQL: " + originalSql);

        Statement statement = CCJSqlParserUtil.parse(originalSql);
        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();

        System.out.println("Modified SQL: " + modifiedSql);

        assertTrue(modifiedSql.contains("u.shop_id = '1'"),
                "Should add tenant condition for sys_user");
        assertTrue(modifiedSql.contains("d.shop_id = '1'"),
                "Should add tenant condition for sys_dept");
        assertTrue(modifiedSql.contains("upper(u.user_name) like '%admin%'"),
                "Should preserve function in WHERE clause");
        System.out.println("========== testFunctionAndExpression END ==========\n");
    }

    @Test
    void testSubqueryAsColumn() throws Throwable {
        // 准备测试数据
        String originalSql = "SELECT u.*, (SELECT COUNT(*) FROM sys_user_post p WHERE p.user_id = u.user_id) as post_count " +
                "FROM sys_user u";
        System.out.println("\n========== testSubqueryAsColumn START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "1");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        assertTrue(modifiedSql.contains("u.shop_id = '1'"),
                "Should add tenant condition for main query");
        assertTrue(modifiedSql.contains("p.shop_id = '1'"),
                "Should add tenant condition for subquery in SELECT");
        System.out.println("========== testSubqueryAsColumn END ==========\n");
    }

    @Test
    void testValuesClause() throws Throwable {
        // 准备测试数据
        String originalSql = "INSERT INTO sys_user (user_name, nick_name) VALUES ('admin', 'Administrator'), ('test', 'Test User')";
        System.out.println("\n========== testValuesClause START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        if (statement instanceof Insert) {
            interceptor.handleInsert((Insert) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("Modified SQL: " + modifiedSql);

            assertTrue(modifiedSql.contains("shop_id"),
                    "Should add shop_id column");
            assertEquals(2, modifiedSql.split("1").length - 1,
                    "Should add shop_id value for both rows");
            assertTrue(modifiedSql.contains("('admin', 'administrator', '1')"),
                    "Should add shop_id value to first row");
            assertTrue(modifiedSql.contains("('test', 'test user', '1')"),
                    "Should add shop_id value to second row");
        }
        System.out.println("========== testValuesClause END ==========\n");
    }

    @Test
    void testComplexUpdate() throws Throwable {
        // 准备测试数据
        String originalSql = "UPDATE sys_user SET dept_id = CASE " +
                "WHEN user_name = 'admin' THEN (SELECT dept_id FROM sys_dept WHERE dept_name = 'IT') " +
                "ELSE dept_id END " +
                "WHERE exists (SELECT 1 FROM sys_dept d WHERE d.dept_id = sys_user.dept_id)";
        System.out.println("\n========== testComplexUpdate START ==========");
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        if (statement instanceof Update) {
            interceptor.handleUpdate((Update) statement, "1");
            String modifiedSql = statement.toString().toLowerCase();
            System.out.println("Modified SQL: " + modifiedSql);

            // 验证主查询添加了租户条件
            assertTrue(modifiedSql.contains("where exists"),
                    "Should preserve EXISTS clause");
            assertTrue(modifiedSql.contains("and shop_id = '1'"),
                    "Should add tenant condition to main query");

            // 验证子查询中的租户条件
            assertTrue(modifiedSql.contains("where dept_name = 'it' and shop_id = '1'"),
                    "Should add tenant condition to CASE WHEN subquery");
            assertTrue(modifiedSql.contains("where d.dept_id = sys_user.dept_id and d.shop_id = '1'"),
                    "Should add tenant condition to EXISTS subquery");
        }
        System.out.println("========== testComplexUpdate END ==========\n");
    }

    @Test
    void testDeptRoleQuery() throws Throwable {
        System.out.println("\n========== testDeptRoleQuery START ==========");
        String roleId = "667889";
        // 准备测试数据
        String originalSql = "SELECT d.dept_id, d.parent_id, d.ancestors, d.dept_name, " +
                "d.order_num, d.leader, d.phone, d.email, d.STATUS, d.del_flag, " +
                "d.create_by, d.create_time " +
                "FROM sys_dept d " +
                "WHERE d.del_flag = '0' " +
                "AND (d.dept_id IN (SELECT dept_id FROM sys_role_dept WHERE role_id = " + roleId + ")) " +
                "ORDER BY d.parent_id, d.order_num";
        System.out.println("Original SQL: " + originalSql);

        // 验证SQL是否被正确修改
        Statement statement = CCJSqlParserUtil.parse(originalSql);
        System.out.println("Parsed statement type: " + statement.getClass().getName());

        interceptor.handleSelect((Select) statement, "10000");
        String modifiedSql = statement.toString().toLowerCase();
        System.out.println("Modified SQL: " + modifiedSql);

        // 验证主查询添加了租户条件
        assertTrue(modifiedSql.contains("d.shop_id = '10000'"),
                "Main query should have shop_id condition");

        // 验证子查询中的租户条件
        assertTrue(modifiedSql.contains("select dept_id from sys_role_dept where role_id = " + roleId + " and shop_id = '10000'"),
                "Subquery should have shop_id condition");

        // 验证ORDER BY子句保持不变
        assertTrue(modifiedSql.contains("order by d.parent_id, d.order_num"),
                "Should preserve ORDER BY clause");

        System.out.println("========== testDeptRoleQuery END ==========\n");
    }
} 
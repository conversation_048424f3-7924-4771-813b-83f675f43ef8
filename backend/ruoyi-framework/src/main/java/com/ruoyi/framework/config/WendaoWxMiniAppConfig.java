package com.ruoyi.framework.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WendaoWxMiniAppConfig {
    @Bean
    public WxMaService wxMaService() {
        WxMiniAppConfigBean config = new WxMiniAppConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("MGGMcNq1VVLjB0ArAisupCPDqP6xY64y1DjPDDEqJa5");
        config.setAppid("wx0e5e01d239197bb1");
        config.setSecret("76bed2dbbab0dc3c30302736ac383426");
        config.setMsgDataFormat("JSON");
        WxMaService service = new WxMaServiceImpl();
        service.setWxMaConfig(config);
        return service;
    }

}

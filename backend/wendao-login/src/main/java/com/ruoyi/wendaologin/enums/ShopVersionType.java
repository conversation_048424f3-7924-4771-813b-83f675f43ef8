package com.ruoyi.wendaologin.enums;

import lombok.Getter;

@Getter
public enum ShopVersionType {
    TRIAL(0, "试用版"),
    STANDARD(1, "标准版"),
    PROFESSIONAL(2, "专业版"),
    FLAGSHIP(3, "旗舰版");

    private final Integer code;
    private final String desc;

    ShopVersionType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShopVersionType getByCode(Integer code) {
        for (ShopVersionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return TRIAL;
    }
} 
package com.ruoyi.wendaologin.service;

import java.util.List;
import com.ruoyi.wendaologin.domain.WendaoShopInfo;

/**
 * 店铺信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IWendaoShopInfoService 
{
    /**
     * 查询店铺信息
     * 
     * @param shopId 店铺信息主键
     * @return 店铺信息
     */
    public WendaoShopInfo selectWendaoShopInfoByShopId(String shopId);

    /**
     * 查询店铺信息列表
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 店铺信息集合
     */
    public List<WendaoShopInfo> selectWendaoShopInfoList(WendaoShopInfo wendaoShopInfo);

    /**
     * 新增店铺信息
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 结果
     */
    public int insertWendaoShopInfo(WendaoShopInfo wendaoShopInfo);

    /**
     * 修改店铺信息
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 结果
     */
    public int updateWendaoShopInfo(WendaoShopInfo wendaoShopInfo);

    /**
     * 批量删除店铺信息
     * 
     * @param shopIds 需要删除的店铺信息主键集合
     * @return 结果
     */
    public int deleteWendaoShopInfoByShopIds(String[] shopIds);

    /**
     * 删除店铺信息信息
     * 
     * @param shopId 店铺信息主键
     * @return 结果
     */
    public int deleteWendaoShopInfoByShopId(String shopId);

    WendaoShopInfo createShop(WendaoShopInfo shopInfo);

    WendaoShopInfo getShopInfo(String shopId);

    void updateShopLastLogin(String shopId, String bUserId);

    List<WendaoShopInfo> getShopsByUserId(String bUserId);
}

package com.ruoyi.wendaologin.mapper;

import java.util.List;
import com.ruoyi.wendaologin.domain.WendaoShopInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 店铺信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface WendaoShopInfoMapper 
{
    /**
     * 查询店铺信息
     * 
     * @param shopId 店铺信息主键
     * @return 店铺信息
     */
    public WendaoShopInfo selectWendaoShopInfoByShopId(String shopId);

    /**
     * 查询店铺信息列表
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 店铺信息集合
     */
    public List<WendaoShopInfo> selectWendaoShopInfoList(WendaoShopInfo wendaoShopInfo);

    /**
     * 新增店铺信息
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 结果
     */
    public int insertWendaoShopInfo(WendaoShopInfo wendaoShopInfo);

    /**
     * 修改店铺信息
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 结果
     */
    public int updateWendaoShopInfo(WendaoShopInfo wendaoShopInfo);

    /**
     * 删除店铺信息
     * 
     * @param shopId 店铺信息主键
     * @return 结果
     */
    public int deleteWendaoShopInfoByShopId(String shopId);

    /**
     * 批量删除店铺信息
     * 
     * @param shopIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoShopInfoByShopIds(String[] shopIds);

    int checkShopNameExists(@Param("shopName") String shopName);

    void resetUserShopsLastLogin(@Param("bUserId") String bUserId);

    void updateShopLastLogin(@Param("shopId") String shopId, @Param("lastLogin") int lastLogin);

    List<WendaoShopInfo> selectByUserId(@Param("bUserId")String bUserId);
}

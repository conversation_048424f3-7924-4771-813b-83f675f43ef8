package com.ruoyi.wendaologin.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * 阿里云短信状态报告DTO
 */
@Data
public class AliyunSmsReportDTO {
    /**
     * 手机号码
     */
    @JSONField(name = "phone_number")
    private String phoneNumber;
    
    /**
     * 发送回执ID
     */
    @JSONField(name = "biz_id")
    private String bizId;
    
    /**
     * 错误码
     */
    @JSONField(name = "err_code")
    private String errCode;
    
    /**
     * 错误消息
     */
    @JSONField(name = "err_msg")
    private String errMsg;
    
    /**
     * 是否发送成功
     */
    private Boolean success;
    
    /**
     * 发送时间
     */
    @JSONField(name = "send_time")
    private String sendTime;
    
    /**
     * 状态报告时间
     */
    @JSONField(name = "report_time")
    private String reportTime;
    
    /**
     * 短信长度
     */
    @JSONField(name = "sms_size")
    private String smsSize;
    
    /**
     * 用户序列号
     */
    @JSONField(name = "out_id")
    private String outId;
} 
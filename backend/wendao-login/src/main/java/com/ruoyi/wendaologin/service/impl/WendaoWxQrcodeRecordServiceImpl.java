package com.ruoyi.wendaologin.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.wendaologin.mapper.WendaoWxQrcodeRecordMapper;
import com.ruoyi.wendaologin.domain.WendaoWxQrcodeRecord;
import com.ruoyi.wendaologin.service.IWendaoWxQrcodeRecordService;

/**
 * 小程序码记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class WendaoWxQrcodeRecordServiceImpl implements IWendaoWxQrcodeRecordService 
{
    @Autowired
    private WendaoWxQrcodeRecordMapper wendaoWxQrcodeRecordMapper;

    /**
     * 查询小程序码记录
     * 
     * @param id 小程序码记录主键
     * @return 小程序码记录
     */
    @Override
    public WendaoWxQrcodeRecord selectWendaoWxQrcodeRecordById(Long id)
    {
        return wendaoWxQrcodeRecordMapper.selectWendaoWxQrcodeRecordById(id);
    }

    /**
     * 查询小程序码记录列表
     * 
     * @param wendaoWxQrcodeRecord 小程序码记录
     * @return 小程序码记录
     */
    @Override
    public List<WendaoWxQrcodeRecord> selectWendaoWxQrcodeRecordList(WendaoWxQrcodeRecord wendaoWxQrcodeRecord)
    {
        return wendaoWxQrcodeRecordMapper.selectWendaoWxQrcodeRecordList(wendaoWxQrcodeRecord);
    }

    /**
     * 新增小程序码记录
     * 
     * @param wendaoWxQrcodeRecord 小程序码记录
     * @return 结果
     */
    @Override
    public int insertWendaoWxQrcodeRecord(WendaoWxQrcodeRecord wendaoWxQrcodeRecord)
    {
        wendaoWxQrcodeRecord.setCreateTime(DateUtils.getNowDate());
        return wendaoWxQrcodeRecordMapper.insertWendaoWxQrcodeRecord(wendaoWxQrcodeRecord);
    }

    /**
     * 修改小程序码记录
     * 
     * @param wendaoWxQrcodeRecord 小程序码记录
     * @return 结果
     */
    @Override
    public int updateWendaoWxQrcodeRecord(WendaoWxQrcodeRecord wendaoWxQrcodeRecord)
    {
        wendaoWxQrcodeRecord.setUpdateTime(DateUtils.getNowDate());
        return wendaoWxQrcodeRecordMapper.updateWendaoWxQrcodeRecord(wendaoWxQrcodeRecord);
    }

    /**
     * 批量删除小程序码记录
     * 
     * @param ids 需要删除的小程序码记录主键
     * @return 结果
     */
    @Override
    public int deleteWendaoWxQrcodeRecordByIds(Long[] ids)
    {
        return wendaoWxQrcodeRecordMapper.deleteWendaoWxQrcodeRecordByIds(ids);
    }

    /**
     * 删除小程序码记录信息
     * 
     * @param id 小程序码记录主键
     * @return 结果
     */
    @Override
    public int deleteWendaoWxQrcodeRecordById(Long id)
    {
        return wendaoWxQrcodeRecordMapper.deleteWendaoWxQrcodeRecordById(id);
    }
}

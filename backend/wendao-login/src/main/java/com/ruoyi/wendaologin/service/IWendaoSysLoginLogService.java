package com.ruoyi.wendaologin.service;

import java.util.List;
import com.ruoyi.wendaologin.domain.WendaoSysLoginLog;

/**
 * 登录日志Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IWendaoSysLoginLogService 
{
    /**
     * 查询登录日志
     * 
     * @param id 登录日志主键
     * @return 登录日志
     */
    public WendaoSysLoginLog selectWendaoSysLoginLogById(Long id);

    /**
     * 查询登录日志列表
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 登录日志集合
     */
    public List<WendaoSysLoginLog> selectWendaoSysLoginLogList(WendaoSysLoginLog wendaoSysLoginLog);

    /**
     * 新增登录日志
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 结果
     */
    public int insertWendaoSysLoginLog(WendaoSysLoginLog wendaoSysLoginLog);

    /**
     * 修改登录日志
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 结果
     */
    public int updateWendaoSysLoginLog(WendaoSysLoginLog wendaoSysLoginLog);

    /**
     * 批量删除登录日志
     * 
     * @param ids 需要删除的登录日志主键集合
     * @return 结果
     */
    public int deleteWendaoSysLoginLogByIds(Long[] ids);

    /**
     * 删除登录日志信息
     * 
     * @param id 登录日志主键
     * @return 结果
     */
    public int deleteWendaoSysLoginLogById(Long id);
}

package com.ruoyi.wendaologin.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.wendaologin.mapper.WendaoSysUserMapper;
import com.ruoyi.wendaologin.domain.WendaoSysUser;
import com.ruoyi.wendaologin.service.IWendaoSysUserService;

/**
 * 问到老师用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class WendaoSysUserServiceImpl implements IWendaoSysUserService 
{
    @Autowired
    private WendaoSysUserMapper wendaoSysUserMapper;

    /**
     * 查询问到老师用户
     * 
     * @param bUserId 问到老师用户主键
     * @return 问到老师用户
     */
    @Override
    public WendaoSysUser selectWendaoSysUserByBUserId(String bUserId)
    {
        return wendaoSysUserMapper.selectWendaoSysUserByBUserId(bUserId);
    }

    /**
     * 查询问到老师用户列表
     * 
     * @param wendaoSysUser 问到老师用户
     * @return 问到老师用户
     */
    @Override
    public List<WendaoSysUser> selectWendaoSysUserList(WendaoSysUser wendaoSysUser)
    {
        return wendaoSysUserMapper.selectWendaoSysUserList(wendaoSysUser);
    }

    /**
     * 新增问到老师用户
     * 
     * @param wendaoSysUser 问到老师用户
     * @return 结果
     */
    @Override
    public int insertWendaoSysUser(WendaoSysUser wendaoSysUser)
    {
        wendaoSysUser.setCreateTime(DateUtils.getNowDate());
        return wendaoSysUserMapper.insertWendaoSysUser(wendaoSysUser);
    }

    /**
     * 修改问到老师用户
     * 
     * @param wendaoSysUser 问到老师用户
     * @return 结果
     */
    @Override
    public int updateWendaoSysUser(WendaoSysUser wendaoSysUser)
    {
        wendaoSysUser.setUpdateTime(DateUtils.getNowDate());
        return wendaoSysUserMapper.updateWendaoSysUser(wendaoSysUser);
    }

    /**
     * 批量删除问到老师用户
     * 
     * @param bUserIds 需要删除的问到老师用户主键
     * @return 结果
     */
    @Override
    public int deleteWendaoSysUserByBUserIds(String[] bUserIds)
    {
        return wendaoSysUserMapper.deleteWendaoSysUserByBUserIds(bUserIds);
    }

    /**
     * 删除问到老师用户信息
     * 
     * @param bUserId 问到老师用户主键
     * @return 结果
     */
    @Override
    public int deleteWendaoSysUserByBUserId(String bUserId)
    {
        return wendaoSysUserMapper.deleteWendaoSysUserByBUserId(bUserId);
    }
}

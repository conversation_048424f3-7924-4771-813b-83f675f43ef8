package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Date;
import java.util.UUID;
import java.util.Calendar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.WxLoginMapper;
import com.ruoyi.system.domain.WxLogin;
import com.ruoyi.system.service.IWxLoginService;

/**
 * 微信扫码登录管理 服务实现
 */
@Service
public class WxLoginServiceImpl implements IWxLoginService {
    @Autowired
    private WxLoginMapper wxLoginMapper;

    /**
     * 查询微信登录信息
     */
    @Override
    public WxLogin selectWxLoginById(Long id) {
        return wxLoginMapper.selectWxLoginById(id);
    }

    /**
     * 根据UUID查询微信登录信息
     */
    @Override
    public WxLogin selectWxLoginByUuid(String uuid) {
        return wxLoginMapper.selectWxLoginByUuid(uuid);
    }

    /**
     * 根据创作者ID查询微信登录信息
     */
    @Override
    public WxLogin selectWxLoginByCreatorId(String creatorId) {
        return wxLoginMapper.selectWxLoginByCreatorId(creatorId);
    }

    /**
     * 根据OpenID查询微信登录信息
     */
    @Override
    public WxLogin selectWxLoginByOpenid(String openid) {
        return wxLoginMapper.selectWxLoginByOpenid(openid);
    }

    /**
     * 根据UnionID查询微信登录信息
     */
    @Override
    public WxLogin selectWxLoginByUnionid(String unionid) {
        return wxLoginMapper.selectWxLoginByUnionid(unionid);
    }

    /**
     * 查询微信登录信息列表
     */
    @Override
    public List<WxLogin> selectWxLoginList(WxLogin wxLogin) {
        return wxLoginMapper.selectWxLoginList(wxLogin);
    }

    /**
     * 新增微信登录信息
     */
    @Override
    public int insertWxLogin(WxLogin wxLogin) {
        // 如果没有设置过期时间，默认设置为5分钟后过期
        if (wxLogin.getExpirationTime() == null) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MINUTE, 5);
            wxLogin.setExpirationTime(calendar.getTime());
        }
        return wxLoginMapper.insertWxLogin(wxLogin);
    }

    /**
     * 修改微信登录信息
     */
    @Override
    public int updateWxLogin(WxLogin wxLogin) {
        return wxLoginMapper.updateWxLogin(wxLogin);
    }

    /**
     * 删除微信登录信息
     */
    @Override
    public int deleteWxLoginByIds(Long[] ids) {
        return wxLoginMapper.deleteWxLoginByIds(ids);
    }

    /**
     * 删除过期的微信登录记录
     */
    @Override
    public int deleteExpiredWxLogin() {
        return wxLoginMapper.deleteExpiredWxLogin();
    }

    /**
     * 校验UUID是否唯一
     */
    @Override
    public boolean checkUuidUnique(String uuid) {
        WxLogin wxLogin = wxLoginMapper.checkUuidUnique(uuid);
        return StringUtils.isNull(wxLogin);
    }

    /**
     * 根据创作者ID和微信小程序APPID查询最新的登录记录
     */
    @Override
    public WxLogin selectLatestWxLoginByCreatorIdAndAppid(String creatorId, String wxMaAppid) {
        return wxLoginMapper.selectLatestWxLoginByCreatorIdAndAppid(creatorId, wxMaAppid);
    }

    /**
     * 生成微信登录二维码UUID
     */
    @Override
    public String generateLoginUuid() {
        String uuid;
        do {
            uuid = UUID.randomUUID().toString().replace("-", "");
        } while (!checkUuidUnique(uuid));
        
        // 创建登录记录
        WxLogin wxLogin = new WxLogin();
        wxLogin.setUuid(uuid);
        wxLogin.setWxMaAppid("wx0e5e01d239197bb1"); // 默认小程序APPID
        insertWxLogin(wxLogin);
        
        return uuid;
    }

    /**
     * 确认微信登录
     */
    @Override
    public boolean confirmWxLogin(String uuid, String creatorId, String openid, String wxMaAppid) {
        WxLogin wxLogin = selectWxLoginByUuid(uuid);
        if (wxLogin != null && wxLogin.isValid()) {
            wxLogin.setCreatorId(creatorId);
            wxLogin.setOpenid(openid);
            if (StringUtils.isNotEmpty(wxMaAppid)) {
                wxLogin.setWxMaAppid(wxMaAppid);
            }
            return updateWxLogin(wxLogin) > 0;
        }
        return false;
    }

    /**
     * 检查登录状态
     */
    @Override
    public WxLogin checkLoginStatus(String uuid) {
        //if (wxLogin != null && wxLogin.isValid() && StringUtils.isNotEmpty(wxLogin.getCreatorId())) {
        return selectWxLoginByUuid(uuid);
        //}
        //return null;
    }
}

# 多租户SQL拦截器实现说明

## 1. 概述

`TenantScopeInterceptor`是一个基于MyBatis插件机制实现的多租户数据隔离解决方案。它通过拦截SQL语句，自动添加租户相关的查询条件，实现了租户数据的安全隔离。

## 2. 实现原理

### 2.1 基本原理

拦截器通过以下步骤实现多租户数据隔离：

1. 拦截MyBatis的SQL执行
2. 解析原始SQL语句
3. 根据SQL类型进行相应处理
4. 重写SQL语句
5. 继续执行修改后的SQL

### 2.2 拦截点

```java
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
```

选择在`StatementHandler`的`prepare`方法处进行拦截，这是SQL即将执行前的最后处理时机。

## 3. 功能特性

### 3.1 支持的SQL类型

- **SELECT语句**
  - 普通查询
  - 子查询
  - JOIN查询
  - UNION/UNION ALL
  - WITH子句
  
- **INSERT语句**
  - 单行插入
  - 批量插入
  - SELECT插入
  
- **UPDATE语句**
  - 条件更新
  - 批量更新
  
- **DELETE语句**
  - 条件删除
  - 批量删除

### 3.2 租户条件处理

- **SELECT语句**：在WHERE子句添加 `tenant_id = xxx` 条件
- **INSERT语句**：自动添加租户ID字段和值
- **UPDATE语句**：在WHERE子句添加租户ID条件
- **DELETE语句**：在WHERE子句添加租户ID条件

### 3.3 特殊场景处理

1. **复杂查询处理**
   - 子查询：递归处理所有子查询
   - JOIN：处理所有JOIN表的租户条件
   - UNION：处理所有UNION子句
   - WITH：处理WITH子句中的子查询

2. **忽略表处理**
   - 支持配置不需要进行租户过滤的表
   - 默认忽略：系统表、租户表、代码生成表等

### 3.4 特殊场景说明

1. **JOIN查询处理**
   - 自动为所有JOIN表添加租户过滤条件
   - 支持LEFT JOIN、RIGHT JOIN、INNER JOIN
   - 建议在JOIN条件中也添加租户ID匹配，提高查询效率

2. **子查询处理**
   - 支持独立子查询的租户过滤
   - 支持相关子查询（Correlated Subquery）
   - 递归处理所有层级的子查询

3. **聚合查询处理**
   - 支持GROUP BY场景下的租户隔离
   - 支持HAVING子句中的条件处理
   - 确保聚合结果的租户隔离

4. **批量操作处理**
   - 支持批量INSERT的租户ID注入
   - 支持批量UPDATE的租户条件限制
   - 支持批量DELETE的租户条件限制

### 3.5 性能优化

1. **SQL解析优化**
   - 使用缓存减少重复解析
   - 优化解析器配置提高性能

2. **条件优化**
   - 合理放置租户条件位置
   - 优化JOIN条件中的租户过滤

3. **批处理优化**
   - 配置合适的批处理阈值
   - 针对大数据量操作的特殊处理

## 4. 使用注意事项

### 4.1 配置要求

1. 需要进行多租户处理的表必须包含`tenant_id`字段
2. 需要在`TenantContext`中正确设置当前租户ID
3. 需要配置正确的忽略表列表

### 4.2 性能考虑

1. SQL解析会带来少量性能开销
2. 建议在`tenant_id`字段上创建索引
3. 对于特别复杂的SQL，可能需要特殊处理

### 4.3 安全考虑

1. 确保`TenantContext`中的租户ID不被篡改
2. 注意处理租户ID为空的情况
3. 避免通过SQL拼接方式绕过租户过滤

## 5. 最佳实践

### 5.1 开发建议

1. 所有涉及多租户的表都应该有统一的租户ID字段名
2. 在表设计时就考虑多租户场景
3. 对于确实需要跨租户的操作，使用忽略表配置

### 5.2 测试建议

1. 测试不同类型SQL的租户过滤是否生效
2. 测试复杂查询场景下的租户隔离
3. 测试批量操作的租户隔离
4. 测试忽略表是否正确工作

## 6. 常见问题

### 6.1 租户ID获取失败

- 检查`TenantContext`是否正确设置
- 检查用户登录信息是否包含租户ID
- 检查租户信息是否正确保存

### 6.2 SQL解析失败

- 检查SQL语法是否正确
- 检查是否是不支持的SQL类型
- 检查SQL是否过于复杂

### 6.3 性能问题

- 检查是否有不必要的子查询
- 检查租户ID字段是否建立索引
- 考虑是否需要优化SQL结构

## 7. 扩展建议

1. 支持动态配置忽略表
2. 支持自定义租户ID字段名
3. 支持更复杂的租户过滤规则
4. 添加性能监控和统计

## 8. 总结

`TenantScopeInterceptor`提供了一个透明的多租户数据隔离解决方案，通过拦截SQL并添加租户条件，实现了数据的安全隔离。它支持各种SQL场景，并提供了灵活的配置选项。在使用时需要注意性能和安全性，同时也要考虑到特殊场景的处理。 
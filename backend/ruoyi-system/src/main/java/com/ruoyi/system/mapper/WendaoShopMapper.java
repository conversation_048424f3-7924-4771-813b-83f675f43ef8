package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.WendaoShop;

/**
 * 普通店铺信息 数据层
 */
public interface WendaoShopMapper {
    /**
     * 查询普通店铺信息
     */
    public WendaoShop selectWendaoShopById(Long id);

    /**
     * 根据店铺ID查询普通店铺信息
     */
    public WendaoShop selectWendaoShopByShopId(String shopId);

    /**
     * 根据应用ID查询普通店铺信息
     */
    public WendaoShop selectWendaoShopByAppId(String appId);

    /**
     * 根据店主ID查询普通店铺信息
     */
    public WendaoShop selectWendaoShopByCreatorId(String creatorId);

    /**
     * 根据店主ID查询普通店铺信息列表
     */
    public List<WendaoShop> selectWendaoShopListByCreatorId(String creatorId);

    /**
     * 查询普通店铺信息列表
     */
    public List<WendaoShop> selectWendaoShopList(WendaoShop wendaoShop);

    /**
     * 新增普通店铺信息
     */
    public int insertWendaoShop(WendaoShop wendaoShop);

    /**
     * 修改普通店铺信息
     */
    public int updateWendaoShop(WendaoShop wendaoShop);

    /**
     * 删除普通店铺信息
     */
    public int deleteWendaoShopById(Long id);

    /**
     * 批量删除普通店铺信息
     */
    public int deleteWendaoShopByIds(Long[] ids);

    /**
     * 校验店铺ID是否唯一
     */
    public WendaoShop checkShopIdUnique(String shopId);

    /**
     * 校验应用ID是否唯一
     */
    public WendaoShop checkAppIdUnique(String appId);

    /**
     * 更新店铺过期状态
     */
    public int updateExpiredStatus();

    /**
     * 查询即将过期的店铺列表
     */
    public List<WendaoShop> selectExpiringSoonShops(@Param("days") int days);

    /**
     * 查询已过期的店铺列表
     */
    public List<WendaoShop> selectExpiredShops();

    /**
     * 清除创作者的所有店铺最后登录状态
     */
    public int clearLastLoginByCreatorId(String creatorId);

    /**
     * 设置最后登录店铺
     */
    public int setLastLoginShop(String shopId);
}

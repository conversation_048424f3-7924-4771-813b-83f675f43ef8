package com.ruoyi.wendaologin.mapper;

import java.util.List;
import com.ruoyi.wendaologin.domain.WendaoSmsLog;
import org.apache.ibatis.annotations.Param;

/**
 * 短信发送日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface WendaoSmsLogMapper 
{
    /**
     * 查询短信发送日志
     * 
     * @param id 短信发送日志主键
     * @return 短信发送日志
     */
    public WendaoSmsLog selectWendaoSmsLogById(Long id);

    /**
     * 查询短信发送日志列表
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 短信发送日志集合
     */
    public List<WendaoSmsLog> selectWendaoSmsLogList(WendaoSmsLog wendaoSmsLog);

    /**
     * 新增短信发送日志
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 结果
     */
    public int insertWendaoSmsLog(WendaoSmsLog wendaoSmsLog);

    /**
     * 修改短信发送日志
     * 
     * @param wendaoSmsLog 短信发送日志
     * @return 结果
     */
    public int updateWendaoSmsLog(WendaoSmsLog wendaoSmsLog);

    /**
     * 删除短信发送日志
     * 
     * @param id 短信发送日志主键
     * @return 结果
     */
    public int deleteWendaoSmsLogById(Long id);

    /**
     * 批量删除短信发送日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoSmsLogByIds(Long[] ids);

    WendaoSmsLog selectByUuid(@Param("uuid") String uuid);

    void updateStatusBySmsId(@Param("smsId")String sid, @Param("status")int status, @Param("errorMsg")String errorMsg);
}

package com.ruoyi.wendaologin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小程序码记录对象 wendao_wx_qrcode_record
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class WendaoWxQrcodeRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 场景值 */
    @Excel(name = "场景值")
    private String scene;

    /** 页面路径 */
    @Excel(name = "页面路径")
    private String pagePath;

    /** 业务类型:1登录,2注册 */
    @Excel(name = "业务类型:1登录,2注册")
    private Integer businessType;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireTime;

    /** 状态:0已失效,1有效 */
    @Excel(name = "状态:0已失效,1有效")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setScene(String scene) 
    {
        this.scene = scene;
    }

    public String getScene() 
    {
        return scene;
    }
    public void setPagePath(String pagePath) 
    {
        this.pagePath = pagePath;
    }

    public String getPagePath() 
    {
        return pagePath;
    }
    public void setBusinessType(Integer businessType) 
    {
        this.businessType = businessType;
    }

    public Integer getBusinessType() 
    {
        return businessType;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("scene", getScene())
            .append("pagePath", getPagePath())
            .append("businessType", getBusinessType())
            .append("expireTime", getExpireTime())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}

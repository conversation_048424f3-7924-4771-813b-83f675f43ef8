package com.ruoyi.wendaologin.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.wendaologin.annotation.Idempotent;
import com.ruoyi.wendaologin.domain.WendaoShopInfo;
import com.ruoyi.wendaologin.dto.CreateShopDTO;
import com.ruoyi.wendaologin.service.IWendaoShopInfoService;
import com.ruoyi.wendaologin.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/shop")
public class ShopController {

    @Autowired
    private IWendaoShopInfoService wendaoShopInfoService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 创建知识店铺
     */
    @PostMapping("/create")
    @Idempotent(expire = 5, message = "请勿重复提交")
    public AjaxResult createShop(@Valid @RequestBody CreateShopDTO createShopDTO) {
        // 从JWT中获取用户ID
        String bUserId = jwtUtil.getCurrentUserId();
        WendaoShopInfo shopInfo = new WendaoShopInfo();
        shopInfo.setShopName(createShopDTO.getShopName());
        shopInfo.setbUserId(bUserId);
        return AjaxResult.success(wendaoShopInfoService.createShop(shopInfo));
    }

    /**
     * 获取用户的店铺列表
     */
    @GetMapping("/list")
    public AjaxResult getShopList() {
        // 从JWT中获取用户ID
        String bUserId = jwtUtil.getCurrentUserId();
        return AjaxResult.success(wendaoShopInfoService.getShopsByUserId(bUserId));
    }

    /**
     * 获取店铺详情
     */
    @GetMapping("/{shopId}")
    public AjaxResult getShopInfo(@PathVariable String shopId) {
        // 从JWT中获取用户ID
        String bUserId = jwtUtil.getCurrentUserId();
        // 查询店铺信息并验证权限
        WendaoShopInfo shopInfo = wendaoShopInfoService.getShopInfo(shopId);
        // 验证店铺是否属于当前用户
        if (!shopInfo.getbUserId().equals(bUserId)) {
            throw new RuntimeException("无权访问该店铺");
        }
        // 更新店铺访问状态
        wendaoShopInfoService.updateShopLastLogin(shopId, bUserId);
        return AjaxResult.success(shopInfo);
    }
} 
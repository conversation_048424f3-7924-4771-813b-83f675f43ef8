package com.ruoyi.wendaologin.mapper;

import java.util.List;
import com.ruoyi.wendaologin.domain.WendaoSysUser;
import org.apache.ibatis.annotations.Param;

/**
 * 问到老师用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface WendaoSysUserMapper 
{
    /**
     * 查询问到老师用户
     * 
     * @param bUserId 问到老师用户主键
     * @return 问到老师用户
     */
    public WendaoSysUser selectWendaoSysUserByBUserId(String bUserId);

    /**
     * 查询问到老师用户列表
     * 
     * @param wendaoSysUser 问到老师用户
     * @return 问到老师用户集合
     */
    public List<WendaoSysUser> selectWendaoSysUserList(WendaoSysUser wendaoSysUser);

    /**
     * 新增问到老师用户
     * 
     * @param wendaoSysUser 问到老师用户
     * @return 结果
     */
    public int insertWendaoSysUser(WendaoSysUser wendaoSysUser);

    /**
     * 修改问到老师用户
     * 
     * @param wendaoSysUser 问到老师用户
     * @return 结果
     */
    public int updateWendaoSysUser(WendaoSysUser wendaoSysUser);

    /**
     * 删除问到老师用户
     * 
     * @param bUserId 问到老师用户主键
     * @return 结果
     */
    public int deleteWendaoSysUserByBUserId(String bUserId);

    /**
     * 批量删除问到老师用户
     * 
     * @param bUserIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoSysUserByBUserIds(String[] bUserIds);

    WendaoSysUser selectByMobile(@Param("mobile") String mobile);

    WendaoSysUser selectByWxOpenId(@Param("wxOpenId")String wxOpenId);
}

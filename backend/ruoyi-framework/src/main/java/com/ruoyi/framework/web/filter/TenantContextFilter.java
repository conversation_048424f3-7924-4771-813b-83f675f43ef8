package com.ruoyi.framework.web.filter;

import com.ruoyi.common.context.TenantContext;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 租户上下文过滤器
 */
//@Component
/**
 * 设置为最后的拦截过滤
 */
//@Order(Ordered.LOWEST_PRECEDENCE)
//public class TenantContextFilter extends OncePerRequestFilter {
public class TenantContextFilter{

    /**
     * 白名单配置
     */
    private static final List<String> WHITE_LIST = Arrays.asList(
            "/captchaImage",    // 验证码
            "/login",           // 登录
            "/register",        // 注册
            "/common/download", // 下载
            "/common/download/resource", // 下载资源
            "/profile/avatar",  // 头像
            "/system/tenant/list", // 租户列表
            "/system/tenant/getTenant", // 获取租户信息
            "/system/config/configKey", // 系统配置
            "/monitor/server",  // 服务监控
            "/tool/gen",        // 代码生成
            "/swagger-ui.html", // Swagger
            "/webjars",         // Swagger
            "/*/api-docs",      // Swagger
            "/druid",           // Druid
            "/favicon.ico"      // 网站图标
    );

    //@Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            // 白名单不处理
            String uri = request.getRequestURI();
            if (isWhiteList(uri)) {
                filterChain.doFilter(request, response);
                return;
            }
            // 获取当前登录用户
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                // 设置租户ID
                TenantContext.setShopId(loginUser.getUser().getShopId());
            }
            filterChain.doFilter(request, response);
        } finally {
            // 清除租户信息
            TenantContext.clear();
        }
    }

    /**
     * 判断是否为白名单
     */
    private boolean isWhiteList(String uri) {
        return WHITE_LIST.stream().anyMatch(pattern -> {
            if (pattern.endsWith("/**")) {
                // 去掉 /** 后进行前缀匹配
                String prefix = pattern.substring(0, pattern.length() - 3);
                return uri.startsWith(prefix);
            }
            return uri.contains(pattern);
        });
    }
}
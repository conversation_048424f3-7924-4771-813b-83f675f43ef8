package com.ruoyi.wendaologin.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WxMaConfiguration {
    @Value("${wx.miniapp.appid}")
    private String appid;
    
    @Value("${wx.miniapp.secret}")
    private String secret;
    
    @Value("${wx.miniapp.token}")
    private String token;
    
    @Value("${wx.miniapp.aesKey}")
    private String aesKey;
    
    @Value("${wx.miniapp.msgDataFormat}")
    private String msgDataFormat;
    
    @Bean
    public WxMaService wxMaService() {
        CustomWxMaConfig config = new CustomWxMaConfig();
        config.setAppid(appid);
        config.setSecret(secret);
        config.setToken(token);
        config.setAesKey(aesKey);
        config.setMsgDataFormat(msgDataFormat);
        
        WxMaService service = new WxMaServiceImpl();
        service.setWxMaConfig(config);
        return service;
    }
} 
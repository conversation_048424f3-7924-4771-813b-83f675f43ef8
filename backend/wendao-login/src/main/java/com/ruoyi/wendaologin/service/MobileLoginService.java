package com.ruoyi.wendaologin.service;


import com.ruoyi.wendaologin.dto.MobileCodeLoginDTO;
import com.ruoyi.wendaologin.dto.MobileLoginDTO;
import com.ruoyi.wendaologin.dto.SendSmsDTO;
import com.ruoyi.wendaologin.dto.SendSmsNoCaptchaDTO;

import java.util.Map;

public interface MobileLoginService {
    /**
     * 手机号密码登录
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    Map<String, Object> loginByPassword(MobileLoginDTO loginDTO);
    
    /**
     * 手机号验证码登录
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    Map<String, Object> loginByCode(MobileCodeLoginDTO loginDTO);
    
    /**
     * 发送短信验证码
     * @param smsDTO 短信参数
     * @return 发送结果
     */
    Map<String, Object> sendSmsCode(SendSmsDTO smsDTO);

    Map<String, Object> sendSmsCodeNoCaptcha(SendSmsNoCaptchaDTO smsDTO);
} 
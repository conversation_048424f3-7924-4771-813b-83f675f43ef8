package com.ruoyi.wendaologin.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

public class HmacSHA256Utils {
    
    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";
    
    /**
     * 使用HMAC-SHA256算法生成签名
     *
     * @param data 要签名的数据
     * @param key 密钥
     * @return 签名结果（十六进制字符串）
     */
    public static String sign(String data, String key) {
        try {
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), HMAC_SHA256_ALGORITHM);
            mac.init(secretKeySpec);
            
            byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            
            // 将byte数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
            
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }
} 
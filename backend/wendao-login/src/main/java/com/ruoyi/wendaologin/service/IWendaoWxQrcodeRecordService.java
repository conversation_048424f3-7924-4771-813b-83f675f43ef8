package com.ruoyi.wendaologin.service;

import java.util.List;
import com.ruoyi.wendaologin.domain.WendaoWxQrcodeRecord;

/**
 * 小程序码记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface IWendaoWxQrcodeRecordService 
{
    /**
     * 查询小程序码记录
     * 
     * @param id 小程序码记录主键
     * @return 小程序码记录
     */
    public WendaoWxQrcodeRecord selectWendaoWxQrcodeRecordById(Long id);

    /**
     * 查询小程序码记录列表
     * 
     * @param wendaoWxQrcodeRecord 小程序码记录
     * @return 小程序码记录集合
     */
    public List<WendaoWxQrcodeRecord> selectWendaoWxQrcodeRecordList(WendaoWxQrcodeRecord wendaoWxQrcodeRecord);

    /**
     * 新增小程序码记录
     * 
     * @param wendaoWxQrcodeRecord 小程序码记录
     * @return 结果
     */
    public int insertWendaoWxQrcodeRecord(WendaoWxQrcodeRecord wendaoWxQrcodeRecord);

    /**
     * 修改小程序码记录
     * 
     * @param wendaoWxQrcodeRecord 小程序码记录
     * @return 结果
     */
    public int updateWendaoWxQrcodeRecord(WendaoWxQrcodeRecord wendaoWxQrcodeRecord);

    /**
     * 批量删除小程序码记录
     * 
     * @param ids 需要删除的小程序码记录主键集合
     * @return 结果
     */
    public int deleteWendaoWxQrcodeRecordByIds(Long[] ids);

    /**
     * 删除小程序码记录信息
     * 
     * @param id 小程序码记录主键
     * @return 结果
     */
    public int deleteWendaoWxQrcodeRecordById(Long id);
}

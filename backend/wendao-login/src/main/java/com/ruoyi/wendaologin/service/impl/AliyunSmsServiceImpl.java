package com.ruoyi.wendaologin.service.impl;

import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.ruoyi.wendaologin.config.AliyunSmsConfig;
import com.ruoyi.wendaologin.model.SmsResult;
import com.ruoyi.wendaologin.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service("aliyunSmsService")
public class AliyunSmsServiceImpl implements SmsService {
    
    @Autowired
    private AliyunSmsConfig smsConfig;
    
    @Autowired
    private AsyncClient smsClient;
    
    @Override
    public SmsResult sendCode(String mobile, String code) {
        SmsResult result = new SmsResult();
        result.setSendTime(new Date());
        
        try {
            // 构建请求参数
            SendSmsRequest request = SendSmsRequest.builder()
                    .phoneNumbers(mobile)
                    .signName(smsConfig.getSignName())
                    .templateCode(smsConfig.getTemplateCode())
                    .templateParam("{\"code\":\"" + code + "\"}")
                    .build();
            
            // 发送短信
            CompletableFuture<SendSmsResponse> response = smsClient.sendSms(request);
            SendSmsResponse resp = response.get();
            
            // 处理响应结果
            if ("OK".equals(resp.getBody().getCode())) {
                result.setSuccess(true);
                result.setSid(resp.getBody().getBizId());
                result.setCode(0);
            } else {
                result.setSuccess(false);
                result.setCode(-1);
                result.setMsg(String.format("发送失败：%s(%s)", 
                        resp.getBody().getMessage(), resp.getBody().getCode()));
                log.error("短信发送失败：mobile={}, code={}, error={}", 
                        mobile, code, resp.getBody().getMessage());
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode(-1);
            result.setMsg("短信发送异常：" + e.getMessage());
            log.error("短信发送异常：mobile={}, code={}", mobile, code, e);
        }
        
        return result;
    }
} 
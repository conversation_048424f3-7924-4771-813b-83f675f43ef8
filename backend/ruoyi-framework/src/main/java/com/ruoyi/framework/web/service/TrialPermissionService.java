package com.ruoyi.framework.web.service;

import java.util.Date;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.domain.SysTrialPermission;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.ISysTrialPermissionService;

/**
 * 试用权限验证服务
 * 
 * <AUTHOR>
 */
@Service("trialPermission")
public class TrialPermissionService
{
    @Autowired
    private ISysTenantService tenantService;
    
    @Autowired
    private ISysTrialPermissionService trialPermissionService;
    
    @Autowired
    private RedisCache redisCache;
    
    private static final String TRIAL_PERMISSION_CACHE_KEY = "trial:permission:";
    private static final String TENANT_TRIAL_STATUS_CACHE_KEY = "trial:tenant:status:";
    
    /**
     * 验证用户是否具备某权限（考虑试用状态）
     * 
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public boolean hasTrialPermi(String permission)
    {
        if (StringUtils.isEmpty(permission))
        {
            return false;
        }
        
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions()))
        {
            return false;
        }
        
        // 先检查基础权限
        if (!hasBasicPermission(loginUser.getPermissions(), permission))
        {
            return false;
        }
        
        // 再检查试用权限
        return checkTrialPermission(loginUser.getUser(), permission);
    }
    
    /**
     * 验证用户是否不具备某权限（考虑试用状态）
     *
     * @param permission 权限字符串
     * @return 用户是否不具备某权限
     */
    public boolean lacksTrialPermi(String permission)
    {
        return !hasTrialPermi(permission);
    }
    
    /**
     * 验证用户是否具有以下任意一个权限（考虑试用状态）
     *
     * @param permissions 以逗号分隔的权限列表
     * @return 用户是否具有以下任意一个权限
     */
    public boolean hasAnyTrialPermi(String permissions)
    {
        if (StringUtils.isEmpty(permissions))
        {
            return false;
        }
        
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions()))
        {
            return false;
        }
        
        for (String permission : permissions.split(","))
        {
            if (permission != null && hasTrialPermi(permission.trim()))
            {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查基础权限
     */
    private boolean hasBasicPermission(Set<String> permissions, String permission)
    {
        return permissions.contains("*:*:*") || permissions.contains(StringUtils.trim(permission));
    }
    
    /**
     * 检查试用权限
     */
    private boolean checkTrialPermission(SysUser user, String permission)
    {
        if (user == null || StringUtils.isEmpty(user.getShopId()))
        {
            return false;
        }
        
        // 获取租户试用状态（带缓存）
        SysTenant tenant = getTenantTrialStatus(user.getShopId());
        if (tenant == null)
        {
            return false;
        }
        
        // 如果已转正，直接允许
        if (tenant.isConverted())
        {
            return true;
        }
        
        // 获取权限配置（带缓存）
        SysTrialPermission trialPermission = getTrialPermissionConfig(permission);
        if (trialPermission == null)
        {
            // 如果没有配置，默认允许
            return true;
        }
        
        // 根据试用状态判断权限
        if (tenant.isInTrial())
        {
            // 试用期内
            return trialPermission.getTrialAvailable() == 1;
        }
        else if (tenant.isTrialExpired())
        {
            // 试用过期
            return trialPermission.getExpiredAvailable() == 1;
        }
        else
        {
            // 未试用状态，不允许
            return false;
        }
    }
    
    /**
     * 获取租户试用状态（带缓存）
     */
    private SysTenant getTenantTrialStatus(String tenantId)
    {
        String cacheKey = TENANT_TRIAL_STATUS_CACHE_KEY + tenantId;
        SysTenant tenant = redisCache.getCacheObject(cacheKey);
        
        if (tenant == null)
        {
            tenant = tenantService.selectTenantById(tenantId);
            if (tenant != null)
            {
                // 缓存5分钟
                redisCache.setCacheObject(cacheKey, tenant, 5, TimeUnit.MINUTES);
            }
        }
        
        return tenant;
    }
    
    /**
     * 获取试用权限配置（带缓存）
     */
    private SysTrialPermission getTrialPermissionConfig(String permission)
    {
        String cacheKey = TRIAL_PERMISSION_CACHE_KEY + permission;
        SysTrialPermission trialPermission = redisCache.getCacheObject(cacheKey);
        
        if (trialPermission == null)
        {
            trialPermission = trialPermissionService.selectByPermissionCode(permission);
            if (trialPermission != null)
            {
                // 缓存30分钟
                redisCache.setCacheObject(cacheKey, trialPermission, 30, TimeUnit.MINUTES);
            }
        }
        
        return trialPermission;
    }
    
    /**
     * 清除租户试用状态缓存
     */
    public void clearTenantTrialCache(String tenantId)
    {
        String cacheKey = TENANT_TRIAL_STATUS_CACHE_KEY + tenantId;
        redisCache.deleteObject(cacheKey);
    }
    
    /**
     * 清除权限配置缓存
     */
    public void clearPermissionCache(String permission)
    {
        String cacheKey = TRIAL_PERMISSION_CACHE_KEY + permission;
        redisCache.deleteObject(cacheKey);
    }
    
    /**
     * 开始试用
     */
    public boolean startTrial(String tenantId, int trialDays)
    {
        SysTenant tenant = tenantService.selectTenantById(tenantId);
        if (tenant == null)
        {
            return false;
        }
        
        Date now = new Date();
        Date endTime = new Date(now.getTime() + trialDays * 24 * 60 * 60 * 1000L);
        
        tenant.setTrialStartTime(now);
        tenant.setTrialEndTime(endTime);
        tenant.setTrialStatus(1); // 试用中
        tenant.setTrialDays(trialDays);
        
        int result = tenantService.updateTenant(tenant);
        if (result > 0)
        {
            // 清除缓存
            clearTenantTrialCache(tenantId);
            return true;
        }
        
        return false;
    }
    
    /**
     * 转正
     */
    public boolean convertTenant(String tenantId)
    {
        SysTenant tenant = tenantService.selectTenantById(tenantId);
        if (tenant == null)
        {
            return false;
        }
        
        tenant.setTrialStatus(3); // 已转正
        
        int result = tenantService.updateTenant(tenant);
        if (result > 0)
        {
            // 清除缓存
            clearTenantTrialCache(tenantId);
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查并更新过期状态
     */
    public void checkAndUpdateExpiredStatus()
    {
        // 这个方法可以通过定时任务调用，检查所有试用过期的租户
        // 实现略，可以查询所有试用中且过期的租户，更新状态为过期
    }
} 
package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysTenant;
import org.apache.ibatis.annotations.Param;

/**
 * 租户表 数据层
 */
public interface SysTenantMapper {
    /**
     * 查询租户信息
     */
    public SysTenant selectTenantById(String shopId);

    /**
     * 查询租户列表
     */
    public List<SysTenant> selectTenantList(SysTenant tenant);

    /**
     * 新增租户
     */
    public int insertTenant(SysTenant tenant);

    /**
     * 修改租户
     */
    public int updateTenant(SysTenant tenant);

    /**
     * 删除租户
     */
    public int deleteTenantById(String shopId);

    /**
     * 批量删除租户
     */
    public int deleteTenantByIds(String[] shopIds);

    SysTenant selectNormalTenantByShopId(@Param("shopId") String shopId);
}
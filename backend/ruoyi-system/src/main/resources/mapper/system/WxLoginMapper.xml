<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.WxLoginMapper">
    
    <resultMap type="WxLogin" id="WxLoginResult">
        <id     property="id"               column="id"               />
        <result property="uuid"             column="uuid"             />
        <result property="creatorId"        column="creator_id"       />
        <result property="openid"           column="openid"           />
        <result property="unionid"          column="unionid"          />
        <result property="wxMaAppid"        column="wx_ma_appid"      />
        <result property="expirationTime"   column="expiration_time"  />
        <result property="scanSuccess"      column="scan_success"     />
        <result property="isExpired"        column="is_expired"       />
        <result property="phoneNumber"      column="phone_number"     />
        <result property="purePhoneNumber"  column="pure_phone_number"/>
        <result property="countryCode"      column="country_code"     />
        <result property="createTime"       column="create_time"      />
        <result property="updateTime"       column="update_time"      />
    </resultMap>
    
    <sql id="selectWxLoginVo">
        select id, uuid, creator_id, openid, unionid, wx_ma_appid, expiration_time,
               scan_success, is_expired, phone_number, pure_phone_number, country_code,
               create_time, update_time
        from wx_login
    </sql>
    
    <select id="selectWxLoginList" parameterType="WxLogin" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        <where>
            <if test="uuid != null and uuid != ''">
                AND uuid = #{uuid}
            </if>
            <if test="creatorId != null and creatorId != ''">
                AND creator_id = #{creatorId}
            </if>
            <if test="openid != null and openid != ''">
                AND openid = #{openid}
            </if>
            <if test="unionid != null and unionid != ''">
                AND unionid = #{unionid}
            </if>
            <if test="wxMaAppid != null and wxMaAppid != ''">
                AND wx_ma_appid = #{wxMaAppid}
            </if>
            <if test="scanSuccess != null">
                AND scan_success = #{scanSuccess}
            </if>
            <if test="isExpired != null">
                AND is_expired = #{isExpired}
            </if>
            <if test="phoneNumber != null and phoneNumber != ''">
                AND phone_number = #{phoneNumber}
            </if>
            <if test="purePhoneNumber != null and purePhoneNumber != ''">
                AND pure_phone_number = #{purePhoneNumber}
            </if>
            <if test="countryCode != null and countryCode != ''">
                AND country_code = #{countryCode}
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWxLoginById" parameterType="Long" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where id = #{id}
    </select>
    
    <select id="selectWxLoginByUuid" parameterType="String" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where uuid = #{uuid}
    </select>
    
    <select id="selectWxLoginByCreatorId" parameterType="String" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where creator_id = #{creatorId}
        order by create_time desc
        limit 1
    </select>
    
    <select id="selectWxLoginByOpenid" parameterType="String" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where openid = #{openid}
        order by create_time desc
        limit 1
    </select>

    <select id="selectWxLoginByUnionid" parameterType="String" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where unionid = #{unionid}
        order by create_time desc
        limit 1
    </select>
    
    <select id="selectLatestWxLoginByCreatorIdAndAppid" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where creator_id = #{creatorId} and wx_ma_appid = #{wxMaAppid}
        order by create_time desc
        limit 1
    </select>
    
    <select id="checkUuidUnique" parameterType="String" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where uuid = #{uuid} limit 1
    </select>

    <select id="selectWxLoginByPhoneNumber" parameterType="String" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where phone_number = #{phoneNumber}
        order by create_time desc
        limit 1
    </select>

    <select id="selectWxLoginByPurePhoneNumber" parameterType="String" resultMap="WxLoginResult">
        <include refid="selectWxLoginVo"/>
        where pure_phone_number = #{purePhoneNumber}
        order by create_time desc
        limit 1
    </select>

    <insert id="insertWxLogin" parameterType="WxLogin" useGeneratedKeys="true" keyProperty="id">
        insert into wx_login(
            <if test="uuid != null and uuid != ''">uuid,</if>
            <if test="creatorId != null and creatorId != ''">creator_id,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="unionid != null and unionid != ''">unionid,</if>
            <if test="wxMaAppid != null and wxMaAppid != ''">wx_ma_appid,</if>
            <if test="expirationTime != null">expiration_time,</if>
            <if test="scanSuccess != null">scan_success,</if>
            <if test="isExpired != null">is_expired,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="purePhoneNumber != null and purePhoneNumber != ''">pure_phone_number,</if>
            <if test="countryCode != null and countryCode != ''">country_code,</if>
            create_time
        )values(
            <if test="uuid != null and uuid != ''">#{uuid},</if>
            <if test="creatorId != null and creatorId != ''">#{creatorId},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="unionid != null and unionid != ''">#{unionid},</if>
            <if test="wxMaAppid != null and wxMaAppid != ''">#{wxMaAppid},</if>
            <if test="expirationTime != null">#{expirationTime},</if>
            <if test="scanSuccess != null">#{scanSuccess},</if>
            <if test="isExpired != null">#{isExpired},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="purePhoneNumber != null and purePhoneNumber != ''">#{purePhoneNumber},</if>
            <if test="countryCode != null and countryCode != ''">#{countryCode},</if>
            sysdate()
        )
    </insert>

    <update id="updateWxLogin" parameterType="WxLogin">
        update wx_login
        <set>
            <if test="uuid != null and uuid != ''">uuid = #{uuid},</if>
            <if test="creatorId != null">creator_id = #{creatorId},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="wxMaAppid != null">wx_ma_appid = #{wxMaAppid},</if>
            <if test="expirationTime != null">expiration_time = #{expirationTime},</if>
            <if test="scanSuccess != null">scan_success = #{scanSuccess},</if>
            <if test="isExpired != null">is_expired = #{isExpired},</if>
            <if test="phoneNumber != null">phone_number = #{phoneNumber},</if>
            <if test="purePhoneNumber != null">pure_phone_number = #{purePhoneNumber},</if>
            <if test="countryCode != null">country_code = #{countryCode},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <delete id="deleteWxLoginById" parameterType="Long">
        delete from wx_login where id = #{id}
    </delete>

    <delete id="deleteWxLoginByIds" parameterType="String">
        delete from wx_login where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteExpiredWxLogin">
        delete from wx_login where expiration_time &lt; sysdate()
    </delete>
</mapper>

-- ----------------------------
-- 1、用户和租户关联表  用户N-N租户
-- ----------------------------
drop table if exists sys_user_tenant;
create table sys_user_tenant (
  user_id   bigint(20) not null comment '用户ID',
  tenant_id bigint(20) not null comment '租户ID',
  primary key(user_id, tenant_id)
) engine=innodb comment = '用户和租户关联表';

-- ----------------------------
-- 2、租户表
-- ----------------------------
drop table if exists sys_tenant;
create table sys_tenant (
  tenant_id    bigint(20)      not null auto_increment    comment '租户ID',
  tenant_code  varchar(64)     not null                   comment '租户编码',
  tenant_name  varchar(50)     not null                   comment '租户名称',
  status       char(1)         default '0'                comment '租户状态（0正常 1停用）',
  create_by    varchar(64)     default ''                 comment '创建者',
  create_time  datetime                                   comment '创建时间',
  update_by    varchar(64)     default ''                 comment '更新者',
  update_time  datetime                                   comment '更新时间',
  remark       varchar(500)    default null               comment '备注',
  primary key (tenant_id),
  unique key uk_tenant_code (tenant_code)
) engine=innodb auto_increment=100 comment = '租户表'; 
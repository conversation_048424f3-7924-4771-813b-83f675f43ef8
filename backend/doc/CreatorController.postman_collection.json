{"info": {"_postman_id": "creator-controller-api", "name": "CreatorController API", "description": "创作者后台登录逻辑相关API接口", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "登录状态查询", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/creator/login_status?uuid={{uuid}}", "host": ["{{baseUrl}}"], "path": ["creator", "login_status"], "query": [{"key": "uuid", "value": "{{uuid}}", "description": "二维码UUID"}]}, "description": "1秒请求一次,前端轮询此接口来判断扫码登录是否完成\nwxLogin.getScanSuccess()==3为登录成功\nwxLogin.getIsExpired()==1则为二维码过期,需要页面刷新新的二维码"}, "response": [{"name": "成功响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/creator/login_status?uuid=test-uuid", "host": ["{{baseUrl}}"], "path": ["creator", "login_status"], "query": [{"key": "uuid", "value": "test-uuid"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"id\": 1,\n        \"uuid\": \"test-uuid\",\n        \"creatorId\": \"creator123\",\n        \"openid\": \"wx_openid\",\n        \"unionid\": \"wx_unionid\",\n        \"wxMaAppid\": \"wx_appid\",\n        \"expirationTime\": \"2024-01-01 12:00:00\",\n        \"scanSuccess\": 1,\n        \"isExpired\": 0,\n        \"phoneNumber\": \"+*************\",\n        \"purePhoneNumber\": \"***********\",\n        \"countryCode\": \"+86\",\n        \"createTime\": \"2024-01-01 11:55:00\",\n        \"updateTime\": \"2024-01-01 11:56:00\"\n    }\n}"}, {"name": "失败响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/creator/login_status?uuid=invalid-uuid", "host": ["{{baseUrl}}"], "path": ["creator", "login_status"], "query": [{"key": "uuid", "value": "invalid-uuid"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 500,\n    \"msg\": \"登录未完成或二维码已过期\"\n}"}]}, {"name": "获取创作者Token", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/creator/getCreatorToken?uuid={{uuid}}&creatorId={{creatorId}}", "host": ["{{baseUrl}}"], "path": ["creator", "getCreatorToken"], "query": [{"key": "uuid", "value": "{{uuid}}", "description": "二维码UUID"}, {"key": "creatorId", "value": "{{creatorId}}", "description": "创作者ID"}]}, "description": "登录成功后,创建创作者的登录token\n是上一个接口wxLogin.getScanSuccess()==3时然后调用此接口"}, "response": [{"name": "成功响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/creator/getCreatorToken?uuid=test-uuid&creatorId=creator123", "host": ["{{baseUrl}}"], "path": ["creator", "getCreatorToken"], "query": [{"key": "uuid", "value": "test-uuid"}, {"key": "creatorId", "value": "creator123"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n        \"creatorInfo\": {\n            \"id\": 1,\n            \"creatorId\": \"creator123\",\n            \"avatarUrl\": \"https://example.com/avatar.jpg\",\n            \"openid\": \"wx_openid\",\n            \"unionid\": \"wx_unionid\",\n            \"wxAccount\": \"***********\",\n            \"phoneNumber\": \"+*************\",\n            \"purePhoneNumber\": \"***********\",\n            \"countryCode\": \"+86\",\n            \"wxMaAppid\": \"wx_appid\"\n        }\n    }\n}"}]}, {"name": "创建二维码", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/creator/createQrcode", "host": ["{{baseUrl}}"], "path": ["creator", "createQrcode"]}, "description": "创建小程序扫码二维码并返回Base64编码和uuid\nuuid以scene参数传入小程序页面,pages/creatorLogin/creatorLogin"}, "response": [{"name": "成功响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/creator/createQrcode", "host": ["{{baseUrl}}"], "path": ["creator", "createQrcode"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"二维码生成成功\",\n    \"data\": {\n        \"uuid\": \"a1b2c3d4e5f6g7h8i9j0\",\n        \"base64\": \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...\"\n    }\n}"}, {"name": "失败响应", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/creator/createQrcode", "host": ["{{baseUrl}}"], "path": ["creator", "createQrcode"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 500,\n    \"msg\": \"微信接口调用失败\"\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string", "description": "API基础URL"}, {"key": "uuid", "value": "test-uuid-123456", "type": "string", "description": "测试用UUID"}, {"key": "creatorId", "value": "creator123", "type": "string", "description": "测试用创作者ID"}]}
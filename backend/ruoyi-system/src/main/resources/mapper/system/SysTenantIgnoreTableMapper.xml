<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysTenantIgnoreTableMapper">
    
    <resultMap type="SysTenantIgnoreTable" id="SysTenantIgnoreTableResult">
        <id     property="id"           column="id"           />
        <result property="tableName"    column="table_name"   />
        <result property="description"  column="description"  />
        <result property="status"       column="status"       />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
    </resultMap>
    
    <sql id="selectTenantIgnoreTableVo">
        select id, table_name, description, status, create_by, create_time, update_by, update_time, remark 
        from sys_tenant_ignore_tables
    </sql>
    
    <select id="selectTenantIgnoreTableList" parameterType="SysTenantIgnoreTable" resultMap="SysTenantIgnoreTableResult">
        <include refid="selectTenantIgnoreTableVo"/>
        <where>
            <if test="tableName != null and tableName != ''">
                AND table_name like concat('%', #{tableName}, '%')
            </if>
            <if test="description != null and description != ''">
                AND description like concat('%', #{description}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
            </if>
        </where>
    </select>
    
    <select id="selectTenantIgnoreTableById" parameterType="Long" resultMap="SysTenantIgnoreTableResult">
        <include refid="selectTenantIgnoreTableVo"/>
        where id = #{id}
    </select>
    
    <select id="selectActiveTableNames" resultType="String">
        select table_name from sys_tenant_ignore_tables 
        where status = '0'
    </select>
    
    <select id="checkTableNameUnique" parameterType="String" resultMap="SysTenantIgnoreTableResult">
        <include refid="selectTenantIgnoreTableVo"/>
        where table_name = #{tableName} limit 1
    </select>
    
    <insert id="insertTenantIgnoreTable" parameterType="SysTenantIgnoreTable" useGeneratedKeys="true" keyProperty="id">
        insert into sys_tenant_ignore_tables (
            <if test="tableName != null and tableName != ''">table_name,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time
        )values(
            <if test="tableName != null and tableName != ''">#{tableName},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate()
        )
    </insert>
     
    <update id="updateTenantIgnoreTable" parameterType="SysTenantIgnoreTable">
        update sys_tenant_ignore_tables 
        <set>
            <if test="tableName != null and tableName != ''">table_name = #{tableName},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>
    
    <delete id="deleteTenantIgnoreTableById" parameterType="Long">
        delete from sys_tenant_ignore_tables where id = #{id}
    </delete>
    
    <delete id="deleteTenantIgnoreTableByIds" parameterType="String">
        delete from sys_tenant_ignore_tables where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
</mapper> 
# @Anonymous注解使用说明

## 1. 功能说明
@Anonymous注解用于标记接口或控制器允许匿名访问，即不需要登录认证就可以直接访问。这个注解可以应用在方法级别或类级别上。

## 2. 使用场景
- 登录接口
- 注册接口
- 公开的API接口
- 不需要认证的静态资源
- 其他无需认证即可访问的接口

## 3. 使用方法

### 3.1 方法级别使用
```java
@GetMapping("/login")
@Anonymous
public AjaxResult login() {
    // 方法实现
}
```

### 3.2 类级别使用
```java
@Anonymous
@RestController
@RequestMapping("/public")
public class PublicController {
    // 控制器实现
}
```

## 4. 实现原理
1. 系统启动时，`PermitAllUrlProperties`类会扫描所有带有@Anonymous注解的接口
2. 将这些接口URL添加到白名单中
3. 在Spring Security的过滤器链中，这些URL会被允许匿名访问
4. 其他未标记@Anonymous的接口则需要进行正常的认证流程

## 5. 注意事项
1. 谨慎使用@Anonymous注解，只用于确实需要匿名访问的接口
2. 类级别的@Anonymous会使该控制器下所有接口都允许匿名访问
3. 涉及敏感数据或操作的接口不应使用此注解
4. 建议在接口文档中明确标注哪些接口允许匿名访问

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysTrialPermissionMapper">
    
    <resultMap type="SysTrialPermission" id="SysTrialPermissionResult">
        <result property="id"                column="id"                />
        <result property="permissionCode"    column="permission_code"   />
        <result property="permissionName"    column="permission_name"   />
        <result property="permissionType"    column="permission_type"   />
        <result property="trialAvailable"    column="trial_available"   />
        <result property="expiredAvailable"  column="expired_available" />
        <result property="createTime"        column="create_time"       />
        <result property="updateTime"        column="update_time"       />
    </resultMap>

    <sql id="selectSysTrialPermissionVo">
        select id, permission_code, permission_name, permission_type, trial_available, expired_available, create_time, update_time from sys_trial_permission
    </sql>

    <select id="selectSysTrialPermissionList" parameterType="SysTrialPermission" resultMap="SysTrialPermissionResult">
        <include refid="selectSysTrialPermissionVo"/>
        <where>  
            <if test="permissionCode != null  and permissionCode != ''"> and permission_code like concat('%', #{permissionCode}, '%')</if>
            <if test="permissionName != null  and permissionName != ''"> and permission_name like concat('%', #{permissionName}, '%')</if>
            <if test="permissionType != null "> and permission_type = #{permissionType}</if>
            <if test="trialAvailable != null "> and trial_available = #{trialAvailable}</if>
            <if test="expiredAvailable != null "> and expired_available = #{expiredAvailable}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectSysTrialPermissionById" parameterType="Long" resultMap="SysTrialPermissionResult">
        <include refid="selectSysTrialPermissionVo"/>
        where id = #{id}
    </select>

    <select id="selectByPermissionCode" parameterType="String" resultMap="SysTrialPermissionResult">
        <include refid="selectSysTrialPermissionVo"/>
        where permission_code = #{permissionCode}
    </select>
        
    <insert id="insertSysTrialPermission" parameterType="SysTrialPermission" useGeneratedKeys="true" keyProperty="id">
        insert into sys_trial_permission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="permissionCode != null and permissionCode != ''">permission_code,</if>
            <if test="permissionName != null and permissionName != ''">permission_name,</if>
            <if test="permissionType != null">permission_type,</if>
            <if test="trialAvailable != null">trial_available,</if>
            <if test="expiredAvailable != null">expired_available,</if>
            create_time,
            update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="permissionCode != null and permissionCode != ''">#{permissionCode},</if>
            <if test="permissionName != null and permissionName != ''">#{permissionName},</if>
            <if test="permissionType != null">#{permissionType},</if>
            <if test="trialAvailable != null">#{trialAvailable},</if>
            <if test="expiredAvailable != null">#{expiredAvailable},</if>
            now(),
            now()
         </trim>
    </insert>

    <update id="updateSysTrialPermission" parameterType="SysTrialPermission">
        update sys_trial_permission
        <trim prefix="SET" suffixOverrides=",">
            <if test="permissionCode != null and permissionCode != ''">permission_code = #{permissionCode},</if>
            <if test="permissionName != null and permissionName != ''">permission_name = #{permissionName},</if>
            <if test="permissionType != null">permission_type = #{permissionType},</if>
            <if test="trialAvailable != null">trial_available = #{trialAvailable},</if>
            <if test="expiredAvailable != null">expired_available = #{expiredAvailable},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysTrialPermissionById" parameterType="Long">
        delete from sys_trial_permission where id = #{id}
    </delete>

    <delete id="deleteSysTrialPermissionByIds" parameterType="String">
        delete from sys_trial_permission where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 
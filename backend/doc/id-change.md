# 多租户ID生成方案

## 1. 需求背景

在多租户系统中，需要实现以下功能：
1. 每个租户的ID从1开始独立计数
2. 保持ID自增的特性
3. 确保ID生成的并发安全
4. 支持数据隔离

## 2. 实现方案

### 2.1 ID生成器实现

采用基于Redis的ID生成器实现，主要包含以下组件：

1. ID生成器接口
```java
public interface IdGenerator {
    Long nextId(String tableName, Long tenantId);
}
```

2. Redis实现类
```java
@Component
public class RedisIdGenerator implements IdGenerator {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    private static final String ID_KEY_PREFIX = "id_generator:";
    
    // 表名到ID字段名的映射
    private static final Map<String, String> TABLE_ID_MAPPING = new HashMap<>();
    
    static {
        TABLE_ID_MAPPING.put("sys_user", "user_id");
        TABLE_ID_MAPPING.put("sys_role", "role_id");
        TABLE_ID_MAPPING.put("sys_menu", "menu_id");
        // ... 其他表映射
    }
    
    @Override
    public Long nextId(String tableName, Long tenantId) {
        String key = ID_KEY_PREFIX + tableName + ":" + tenantId;
        // 如果key不存在，需要初始化
        if (!redisTemplate.hasKey(key)) {
            synchronized (this) {
                if (!redisTemplate.hasKey(key)) {
                    String idColumn = TABLE_ID_MAPPING.getOrDefault(tableName, "id");
                    String sql = "SELECT COALESCE(MAX(" + idColumn + "), 0) FROM " + tableName + " WHERE tenant_id = ?";
                    Long maxId = jdbcTemplate.queryForObject(sql, Long.class, tenantId);
                    redisTemplate.opsForValue().set(key, maxId);
                }
            }
        }
        return redisTemplate.opsForValue().increment(key);
    }
}
```

### 2.2 MyBatis配置修改

1. 移除全局的useGeneratedKeys配置
```xml
<!-- 移除以下配置 -->
<setting name="useGeneratedKeys" value="true"/>
```

2. 移除各Mapper XML中的useGeneratedKeys和keyProperty属性：
- SysUserMapper.xml
- SysRoleMapper.xml
- SysPostMapper.xml
- SysJobMapper.xml

### 2.3 多租户数据隔离

使用MyBatis拦截器（TenantScopeInterceptor）自动处理：
1. SELECT语句添加tenant_id条件
2. INSERT语句注入tenant_id字段
3. UPDATE语句添加tenant_id条件
4. DELETE语句添加tenant_id条件

## 3. 注意事项

1. ID生成相关：
   - Redis key使用 "id_generator:表名:租户ID" 的格式
   - 首次使用时会从数据库获取当前最大ID
   - 使用Redis的increment操作保证并发安全
   - 使用双重检查锁确保初始化的线程安全
   - 确保所有insert语句都包含主键字段，条件为 `<if test="xxxId != null">xxx_id,</if>`

2. 忽略的表：
   - gen_table
   - gen_table_column
   - sys_tenant
   这些表保持原有的自增ID策略

3. 数据隔离：
   - 所有SQL操作都会自动添加tenant_id条件
   - 主键查询会自动变成联合主键查询
   - 忽略表不会添加tenant_id条件

4. 性能考虑：
   - ID生成在Redis中进行，避免数据库压力
   - 使用本地缓存减少Redis访问
   - 可以考虑批量预分配ID提升性能

## 4. 后续优化建议

1. ID生成优化：
   - 实现ID段分配，减少Redis访问
   - 添加本地缓存机制
   - 考虑添加降级方案

2. 数据迁移：
   - 提供租户数据迁移工具
   - 支持ID重新生成

3. 监控告警：
   - 添加ID生成性能监控
   - 异常情况告警机制

4. 容灾考虑：
   - Redis集群部署
   - 添加降级策略

package com.ruoyi.wendaologin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 短信发送日志对象 wendao_sms_log
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class WendaoSmsLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 唯一标识 */
    @Excel(name = "唯一标识")
    private String uuid;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobile;

    /** 验证码 */
    @Excel(name = "验证码")
    private String code;

    /** 业务类型：1-登录，2-注册，3-重置密码 */
    @Excel(name = "业务类型：1-登录，2-注册，3-重置密码")
    private Integer type;

    /** 发送IP */
    @Excel(name = "发送IP")
    private String ip;

    /** 短信平台返回的消息ID */
    @Excel(name = "短信平台返回的消息ID")
    private String smsId;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMsg;

    /** 发送状态：0-失败，1-成功，2-未回执 */
    @Excel(name = "发送状态：0-失败，1-成功，2-未回执")
    private Integer status;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sendTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUuid(String uuid) 
    {
        this.uuid = uuid;
    }

    public String getUuid() 
    {
        return uuid;
    }
    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }
    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setIp(String ip) 
    {
        this.ip = ip;
    }

    public String getIp() 
    {
        return ip;
    }
    public void setSmsId(String smsId) 
    {
        this.smsId = smsId;
    }

    public String getSmsId() 
    {
        return smsId;
    }
    public void setErrorMsg(String errorMsg) 
    {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() 
    {
        return errorMsg;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setSendTime(Date sendTime) 
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime() 
    {
        return sendTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uuid", getUuid())
            .append("mobile", getMobile())
            .append("code", getCode())
            .append("type", getType())
            .append("ip", getIp())
            .append("smsId", getSmsId())
            .append("errorMsg", getErrorMsg())
            .append("status", getStatus())
            .append("sendTime", getSendTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}

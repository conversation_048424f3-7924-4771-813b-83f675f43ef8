package com.ruoyi.wendaologin.config;

import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
public class CustomWxMaConfig extends WxMaDefaultConfigImpl {

//    public static void main(String[] args) {
//        System.out.println(new CustomWxMaConfig().getAccessToken());
//    }
    
    private static final String TOKEN_URL = "https://goodminiapp.wendao101.com/kt_access_token_controller/getKtWxToken?requestKey=87er8wefsd5f4e8wr8ew78rew";
    
    @Override
    public String getAccessToken() {
        try {
            // 创建请求参数
            //Map<String, String> params = new HashMap<>();
            //params.put("appId", getAppid());
            //params.put("password", "xiaoetong007877");
            
            // 使用RestTemplate或其他HTTP客户端调用接口
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.getForEntity(
                TOKEN_URL,
                String.class
            );
            
            // 使用FastJSON2解析响应
            //JSONObject jsonObject = JSON.parseObject(response.getBody());
            //return jsonObject.getString("access_token");
            return response.getBody();
            
        } catch (Exception e) {
            throw new RuntimeException("获取access_token失败", e);
        }
    }
    @Override
    public boolean isAccessTokenExpired() {
        return false;
    }
    
    @Override
    public boolean autoRefreshToken() {
        return false; // 禁用SDK的自动刷新token功能
    }
} 
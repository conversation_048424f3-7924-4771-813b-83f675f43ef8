package com.ruoyi.web.controller.trial;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.TrialPermissionService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.domain.SysTrialPermission;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.ISysTrialPermissionService;

/**
 * 试用权限API控制器（前端专用）
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/trial")
public class TrialApiController extends BaseController
{
    @Autowired
    private TrialPermissionService trialPermissionService;
    
    @Autowired
    private ISysTenantService tenantService;
    
    @Autowired
    private ISysTrialPermissionService trialPermissionConfigService;

    /**
     * 检查试用权限
     */
    @PostMapping("/checkPermission")
    public AjaxResult checkPermission(@RequestBody TrialPermissionRequest request)
    {
        try {
            String permission = request.getPermission();
            Integer type = request.getType();
            
            if (permission == null || permission.trim().isEmpty()) {
                return error("权限代码不能为空");
            }
            
            // 检查试用权限
            boolean hasPermission = trialPermissionService.hasTrialPermi(permission);
            
            return success(hasPermission);
        } catch (Exception e) {
            logger.error("检查试用权限失败", e);
            return error("权限检查失败");
        }
    }

    /**
     * 获取当前租户试用信息
     */
    @GetMapping("/tenantInfo")
    public AjaxResult getTenantInfo()
    {
        try {
            SysUser currentUser = SecurityUtils.getLoginUser().getUser();
            if (currentUser == null || currentUser.getShopId() == null) {
                return error("用户信息异常");
            }
            
            SysTenant tenant = tenantService.selectTenantById(currentUser.getShopId());
            if (tenant == null) {
                return error("租户信息不存在");
            }
            
            // 构建返回的租户试用信息
            TrialTenantInfo trialInfo = new TrialTenantInfo();
            trialInfo.setTenantId(tenant.getShopId());
            trialInfo.setTenantName(tenant.getTenantName());
            trialInfo.setTrialStatus(tenant.getTrialStatus());
            trialInfo.setTrialDays(tenant.getTrialDays());
            trialInfo.setTrialStartTime(tenant.getTrialStartTime());
            trialInfo.setTrialEndTime(tenant.getTrialEndTime());
            
            // 计算剩余天数
            if (tenant.getTrialEndTime() != null && tenant.getTrialStatus() == 1) {
                long remainingTime = tenant.getTrialEndTime().getTime() - System.currentTimeMillis();
                int remainingDays = Math.max(0, (int) (remainingTime / (24 * 60 * 60 * 1000)));
                trialInfo.setRemainingDays(remainingDays);
            }
            
            return success(trialInfo);
        } catch (Exception e) {
            logger.error("获取租户试用信息失败", e);
            return error("获取租户信息失败");
        }
    }

    /**
     * 申请试用
     */
    @Log(title = "申请试用", businessType = BusinessType.UPDATE)
    @PostMapping("/apply")
    public AjaxResult applyTrial(@RequestParam(defaultValue = "7") int trialDays)
    {
        try {
            SysUser currentUser = SecurityUtils.getLoginUser().getUser();
            if (currentUser == null || currentUser.getShopId() == null) {
                return error("用户信息异常");
            }
            
            String tenantId = currentUser.getShopId();
            SysTenant tenant = tenantService.selectTenantById(tenantId);
            if (tenant == null) {
                return error("租户信息不存在");
            }
            
            // 检查是否已经试用过
            if (tenant.getTrialStatus() != null && tenant.getTrialStatus() != 0) {
                return error("该租户已经申请过试用");
            }
            
            // 开始试用
            boolean result = trialPermissionService.startTrial(tenantId, trialDays);
            if (result) {
                return success("试用申请成功，试用期为 " + trialDays + " 天");
            } else {
                return error("试用申请失败");
            }
        } catch (Exception e) {
            logger.error("申请试用失败", e);
            return error("申请试用失败");
        }
    }

    /**
     * 获取试用权限列表
     */
    @GetMapping("/permissions")
    public AjaxResult getTrialPermissions()
    {
        try {
            SysTrialPermission queryParam = new SysTrialPermission();
            List<SysTrialPermission> permissions = trialPermissionConfigService.selectSysTrialPermissionList(queryParam);
            return success(permissions);
        } catch (Exception e) {
            logger.error("获取试用权限列表失败", e);
            return error("获取权限列表失败");
        }
    }

    /**
     * 获取试用状态统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getTrialStatistics()
    {
        try {
            SysUser currentUser = SecurityUtils.getLoginUser().getUser();
            if (currentUser == null || currentUser.getShopId() == null) {
                return error("用户信息异常");
            }
            
            String tenantId = currentUser.getShopId();
            SysTenant tenant = tenantService.selectTenantById(tenantId);
            if (tenant == null) {
                return error("租户信息不存在");
            }
            
            TrialStatistics statistics = new TrialStatistics();
            statistics.setTenantId(tenantId);
            statistics.setTrialStatus(tenant.getTrialStatus());
            
            // 统计权限信息
            SysTrialPermission queryParam = new SysTrialPermission();
            List<SysTrialPermission> allPermissions = trialPermissionConfigService.selectSysTrialPermissionList(queryParam);
            
            long readPermissions = allPermissions.stream().filter(p -> p.getPermissionType() == 1).count();
            long writePermissions = allPermissions.stream().filter(p -> p.getPermissionType() == 2).count();
            
            statistics.setTotalReadPermissions((int) readPermissions);
            statistics.setTotalWritePermissions((int) writePermissions);
            
            // 根据试用状态计算可用权限
            if (tenant.getTrialStatus() == 1) { // 试用中
                statistics.setAvailableReadPermissions((int) readPermissions);
                statistics.setAvailableWritePermissions((int) writePermissions);
            } else if (tenant.getTrialStatus() == 2) { // 试用过期
                long expiredReadPermissions = allPermissions.stream()
                    .filter(p -> p.getPermissionType() == 1 && p.getExpiredAvailable() == 1)
                    .count();
                statistics.setAvailableReadPermissions((int) expiredReadPermissions);
                statistics.setAvailableWritePermissions(0); // 写权限全部不可用
            } else { // 未试用或已转正
                statistics.setAvailableReadPermissions((int) readPermissions);
                statistics.setAvailableWritePermissions((int) writePermissions);
            }
            
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取试用统计信息失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 试用权限检查请求对象
     */
    public static class TrialPermissionRequest {
        private String permission;
        private Integer type;

        public String getPermission() {
            return permission;
        }

        public void setPermission(String permission) {
            this.permission = permission;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }
    }

    /**
     * 试用租户信息返回对象
     */
    public static class TrialTenantInfo {
        private String tenantId;
        private String tenantName;
        private Integer trialStatus;
        private Integer trialDays;
        private java.util.Date trialStartTime;
        private java.util.Date trialEndTime;
        private Integer remainingDays;

        // Getters and Setters
        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public String getTenantName() {
            return tenantName;
        }

        public void setTenantName(String tenantName) {
            this.tenantName = tenantName;
        }

        public Integer getTrialStatus() {
            return trialStatus;
        }

        public void setTrialStatus(Integer trialStatus) {
            this.trialStatus = trialStatus;
        }

        public Integer getTrialDays() {
            return trialDays;
        }

        public void setTrialDays(Integer trialDays) {
            this.trialDays = trialDays;
        }

        public java.util.Date getTrialStartTime() {
            return trialStartTime;
        }

        public void setTrialStartTime(java.util.Date trialStartTime) {
            this.trialStartTime = trialStartTime;
        }

        public java.util.Date getTrialEndTime() {
            return trialEndTime;
        }

        public void setTrialEndTime(java.util.Date trialEndTime) {
            this.trialEndTime = trialEndTime;
        }

        public Integer getRemainingDays() {
            return remainingDays;
        }

        public void setRemainingDays(Integer remainingDays) {
            this.remainingDays = remainingDays;
        }
    }

    /**
     * 试用统计信息返回对象
     */
    public static class TrialStatistics {
        private String tenantId;
        private Integer trialStatus;
        private Integer totalReadPermissions;
        private Integer totalWritePermissions;
        private Integer availableReadPermissions;
        private Integer availableWritePermissions;

        // Getters and Setters
        public String getTenantId() {
            return tenantId;
        }

        public void setTenantId(String tenantId) {
            this.tenantId = tenantId;
        }

        public Integer getTrialStatus() {
            return trialStatus;
        }

        public void setTrialStatus(Integer trialStatus) {
            this.trialStatus = trialStatus;
        }

        public Integer getTotalReadPermissions() {
            return totalReadPermissions;
        }

        public void setTotalReadPermissions(Integer totalReadPermissions) {
            this.totalReadPermissions = totalReadPermissions;
        }

        public Integer getTotalWritePermissions() {
            return totalWritePermissions;
        }

        public void setTotalWritePermissions(Integer totalWritePermissions) {
            this.totalWritePermissions = totalWritePermissions;
        }

        public Integer getAvailableReadPermissions() {
            return availableReadPermissions;
        }

        public void setAvailableReadPermissions(Integer availableReadPermissions) {
            this.availableReadPermissions = availableReadPermissions;
        }

        public Integer getAvailableWritePermissions() {
            return availableWritePermissions;
        }

        public void setAvailableWritePermissions(Integer availableWritePermissions) {
            this.availableWritePermissions = availableWritePermissions;
        }
    }
} 
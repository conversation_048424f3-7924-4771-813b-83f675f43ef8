package com.ruoyi.framework.task;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ruoyi.framework.web.service.TrialPermissionService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.service.ISysTenantService;

/**
 * 试用状态检查定时任务
 * 
 * <AUTHOR>
 */
@Component("trialStatusTask")
public class TrialStatusTask
{
    private static final Logger log = LoggerFactory.getLogger(TrialStatusTask.class);
    
    @Autowired
    private ISysTenantService tenantService;
    
    @Autowired
    private TrialPermissionService trialPermissionService;
    
    /**
     * 检查试用过期状态
     */
    public void checkTrialExpiredStatus()
    {
        log.info("开始检查试用过期状态");
        
        try
        {
            // 查询所有试用中的租户
            SysTenant queryTenant = new SysTenant();
            queryTenant.setTrialStatus(1); // 试用中
            List<SysTenant> trialTenants = tenantService.selectTenantList(queryTenant);
            
            Date now = new Date();
            int expiredCount = 0;
            
            for (SysTenant tenant : trialTenants)
            {
                if (tenant.getTrialEndTime() != null && tenant.getTrialEndTime().before(now))
                {
                    // 试用已过期，更新状态
                    tenant.setTrialStatus(2); // 试用过期
                    int updateResult = tenantService.updateTenant(tenant);
                    
                    // 只有更新成功时才清除缓存
                    if (updateResult > 0)
                    {
                        // 清除缓存
                        trialPermissionService.clearTenantTrialCache(tenant.getShopId());
                        
                        expiredCount++;
                        log.info("租户 {} 试用已过期，状态已更新", tenant.getShopId());
                    }
                    else
                    {
                        log.warn("租户 {} 试用状态更新失败", tenant.getShopId());
                    }
                }
            }
            
            log.info("试用状态检查完成，共处理 {} 个过期租户", expiredCount);
        }
        catch (Exception e)
        {
            log.error("检查试用过期状态时发生异常", e);
        }
    }
    
    /**
     * 试用到期提醒
     */
    public void trialExpirationReminder()
    {
        log.info("开始检查试用到期提醒");
        
        try
        {
            // 查询所有试用中的租户
            SysTenant queryTenant = new SysTenant();
            queryTenant.setTrialStatus(1); // 试用中
            List<SysTenant> trialTenants = tenantService.selectTenantList(queryTenant);
            
            Date now = new Date();
            long oneDayMillis = 24 * 60 * 60 * 1000L;
            int reminderCount = 0;
            
            for (SysTenant tenant : trialTenants)
            {
                if (tenant.getTrialEndTime() != null)
                {
                    long timeLeft = tenant.getTrialEndTime().getTime() - now.getTime();
                    
                    // 剩余时间小于等于1天时发送提醒
                    if (timeLeft > 0 && timeLeft <= oneDayMillis)
                    {
                        // 这里可以发送邮件、短信或系统通知
                        sendTrialExpirationNotification(tenant);
                        reminderCount++;
                        log.info("已向租户 {} 发送试用到期提醒", tenant.getShopId());
                    }
                }
            }
            
            log.info("试用到期提醒检查完成，共发送 {} 个提醒", reminderCount);
        }
        catch (Exception e)
        {
            log.error("检查试用到期提醒时发生异常", e);
        }
    }
    
    /**
     * 发送试用到期通知
     */
    private void sendTrialExpirationNotification(SysTenant tenant)
    {
        // 这里实现具体的通知逻辑
        // 可以发送邮件、短信、系统内消息等
        log.info("发送试用到期通知给租户: {}, 租户名称: {}", tenant.getShopId(), tenant.getTenantName());
        
        // 示例：可以调用消息服务
        // messageService.sendTrialExpirationMessage(tenant);
    }
} 
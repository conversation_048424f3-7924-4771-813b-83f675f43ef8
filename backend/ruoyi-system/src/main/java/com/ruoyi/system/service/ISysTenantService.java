package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysTenant;

/**
 * 租户管理 服务层
 */
public interface ISysTenantService {
    /**
     * 查询租户信息
     */
    public SysTenant selectTenantById(String shopId);

    /**
     * 查询租户列表
     */
    public List<SysTenant> selectTenantList(SysTenant tenant);

    /**
     * 新增租户
     */
    public int insertTenant(SysTenant tenant);

    /**
     * 修改租户
     */
    public int updateTenant(SysTenant tenant);

    /**
     * 删除租户信息
     */
    public int deleteTenantByIds(String[] shopIds);

    SysTenant selectNormalTenantByShopId(String shopId);
}
package com.ruoyi.wendaologin.util;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.StringUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class JwtUtil {
    
    // 令牌自定义标识
    @Value("${token.header}")
    private String header;

    // 令牌秘钥
    @Value("${token.secret}")
    private String secret;

    // 令牌有效期（默认30分钟）
    @Value("${token.expireTime}")
    private int expireTime;

    /**
     * 生成token
     */
    public String generateToken(String bUserId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("bUserId", bUserId);
        return createToken(claims);
    }
    
    /**
     * 从token中获取用户ID
     */
    public String getBUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.get("bUserId", String.class);
    }
    
    /**
     * 验证token是否有效
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return !claims.getExpiration().before(new Date());
        } catch (Exception e) {
            return false;
        }
    }
    
    private String createToken(Map<String, Object> claims) {
        return Jwts.builder()
            .setClaims(claims)
            .signWith(SignatureAlgorithm.HS512, secret).compact();
    }
    
    private Claims getClaimsFromToken(String token) {
        return Jwts.parser()
            .setSigningKey(secret)
            .parseClaimsJws(token)
            .getBody();
    }
    
    /**
     * 从请求头中获取当前登录用户ID
     */
    public String getCurrentUserId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new RuntimeException("获取用户信息失败");
        }
        HttpServletRequest request = attributes.getRequest();
        String token = request.getHeader(header);
        if(StringUtils.isBlank(token)){
            throw new RuntimeException("token不存在");
        }
        if (token.startsWith(Constants.TOKEN_PREFIX)) {
            // 去掉Bearer 前缀
            token = token.substring(Constants.TOKEN_PREFIX.length());
        }
        try {
            Claims claims = getClaimsFromToken(token);
            return claims.get("bUserId", String.class);
        } catch (Exception e) {
            log.error("解析token失败", e);
            throw new RuntimeException("token无效");
        }
    }
} 
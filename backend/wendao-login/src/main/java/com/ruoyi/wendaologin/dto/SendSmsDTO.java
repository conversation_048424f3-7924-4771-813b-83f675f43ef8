package com.ruoyi.wendaologin.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
public class SendSmsDTO {
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String mobile;
    
    /**
     * 验证码票据
     */
    @NotBlank(message = "验证码票据不能为空")
    private String ticket;
    
    /**
     * 验证码随机字符串
     */
    @NotBlank(message = "验证码随机字符串不能为空")
    private String randstr;
} 
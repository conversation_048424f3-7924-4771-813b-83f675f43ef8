package com.ruoyi.common.core.id.impl;

import com.ruoyi.common.core.id.IdGenerator;
import com.ruoyi.common.core.redis.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class RedisIdGenerator implements IdGenerator {

    //    @Autowired
//    private RedisCache redisCache;
    @Autowired
    public StringRedisTemplate stringRedisTemplate;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final String ID_KEY_PREFIX = "id_generator:";

    // 表名到ID字段名的映射
//    private static final Map<String, String> TABLE_ID_MAPPING = new HashMap<>();

//    static {
//        TABLE_ID_MAPPING.put("sys_user", "user_id");
//        TABLE_ID_MAPPING.put("sys_role", "role_id");
//        TABLE_ID_MAPPING.put("sys_menu", "menu_id");
//        TABLE_ID_MAPPING.put("sys_dept", "dept_id");
//        TABLE_ID_MAPPING.put("sys_post", "post_id");
//        TABLE_ID_MAPPING.put("sys_dict_type", "dict_id");
//        TABLE_ID_MAPPING.put("sys_dict_data", "dict_code");
//        TABLE_ID_MAPPING.put("sys_config", "config_id");
//        TABLE_ID_MAPPING.put("sys_notice", "notice_id");
//        TABLE_ID_MAPPING.put("sys_oper_log", "oper_id");
//        TABLE_ID_MAPPING.put("sys_job", "job_id");
//        TABLE_ID_MAPPING.put("sys_job_log", "job_log_id");
//    }

    @Override
    public Long nextId(String tableName, String shopId, String idColumnName) {
        String key = ID_KEY_PREFIX + tableName + ":" + shopId;
        // 如果key不存在，需要初始化
        if (!stringRedisTemplate.hasKey(key)) {
            synchronized (this) {
                if (!stringRedisTemplate.hasKey(key)) {
                    // 获取ID字段名
                    //String idColumn = TABLE_ID_MAPPING.getOrDefault(tableName, "id");
                    // 从数据库获取当前最大ID
                    String sql = "SELECT COALESCE(MAX(" + idColumnName + "), 0) FROM " + tableName + " WHERE shop_id = ?";
                    Long maxId = jdbcTemplate.queryForObject(sql, Long.class, shopId);
                    // 设置初始值
                    stringRedisTemplate.opsForValue().set(key, String.valueOf(maxId));
                }
            }
        }
        return stringRedisTemplate.opsForValue().increment(key);
    }
} 
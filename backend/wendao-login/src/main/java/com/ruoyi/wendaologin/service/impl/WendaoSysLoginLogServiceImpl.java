package com.ruoyi.wendaologin.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.wendaologin.mapper.WendaoSysLoginLogMapper;
import com.ruoyi.wendaologin.domain.WendaoSysLoginLog;
import com.ruoyi.wendaologin.service.IWendaoSysLoginLogService;

/**
 * 登录日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class WendaoSysLoginLogServiceImpl implements IWendaoSysLoginLogService 
{
    @Autowired
    private WendaoSysLoginLogMapper wendaoSysLoginLogMapper;

    /**
     * 查询登录日志
     * 
     * @param id 登录日志主键
     * @return 登录日志
     */
    @Override
    public WendaoSysLoginLog selectWendaoSysLoginLogById(Long id)
    {
        return wendaoSysLoginLogMapper.selectWendaoSysLoginLogById(id);
    }

    /**
     * 查询登录日志列表
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 登录日志
     */
    @Override
    public List<WendaoSysLoginLog> selectWendaoSysLoginLogList(WendaoSysLoginLog wendaoSysLoginLog)
    {
        return wendaoSysLoginLogMapper.selectWendaoSysLoginLogList(wendaoSysLoginLog);
    }

    /**
     * 新增登录日志
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 结果
     */
    @Override
    public int insertWendaoSysLoginLog(WendaoSysLoginLog wendaoSysLoginLog)
    {
        wendaoSysLoginLog.setCreateTime(DateUtils.getNowDate());
        return wendaoSysLoginLogMapper.insertWendaoSysLoginLog(wendaoSysLoginLog);
    }

    /**
     * 修改登录日志
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 结果
     */
    @Override
    public int updateWendaoSysLoginLog(WendaoSysLoginLog wendaoSysLoginLog)
    {
        wendaoSysLoginLog.setUpdateTime(DateUtils.getNowDate());
        return wendaoSysLoginLogMapper.updateWendaoSysLoginLog(wendaoSysLoginLog);
    }

    /**
     * 批量删除登录日志
     * 
     * @param ids 需要删除的登录日志主键
     * @return 结果
     */
    @Override
    public int deleteWendaoSysLoginLogByIds(Long[] ids)
    {
        return wendaoSysLoginLogMapper.deleteWendaoSysLoginLogByIds(ids);
    }

    /**
     * 删除登录日志信息
     * 
     * @param id 登录日志主键
     * @return 结果
     */
    @Override
    public int deleteWendaoSysLoginLogById(Long id)
    {
        return wendaoSysLoginLogMapper.deleteWendaoSysLoginLogById(id);
    }
}

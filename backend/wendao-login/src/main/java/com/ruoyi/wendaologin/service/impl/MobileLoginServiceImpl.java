package com.ruoyi.wendaologin.service.impl;


import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.wendaologin.domain.WendaoSmsLog;
import com.ruoyi.wendaologin.domain.WendaoSysUser;
import com.ruoyi.wendaologin.dto.*;
import com.ruoyi.wendaologin.mapper.WendaoSmsLogMapper;
import com.ruoyi.wendaologin.mapper.WendaoSysUserMapper;
import com.ruoyi.wendaologin.model.SmsResult;
import com.ruoyi.wendaologin.service.CaptchaService;
import com.ruoyi.wendaologin.service.MobileLoginService;
import com.ruoyi.wendaologin.service.SmsService;
import com.ruoyi.wendaologin.util.JwtUtil;
import com.ruoyi.wendaologin.util.PasswordEncoder;
import com.ruoyi.wendaologin.util.UserIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class MobileLoginServiceImpl implements MobileLoginService {
    
    @Autowired
    private WendaoSysUserMapper wendaoSysUserMapper;
    
    @Autowired
    private RedisTemplate redisTemplate;
    
    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private WendaoSmsLogMapper wendaoSmsLogMapper;
    
    @Autowired
    private CaptchaService captchaService;
    
    @Autowired
    @Qualifier("yunpianSmsService")
    private SmsService smsService;
    
    private static final String SMS_CODE_KEY = "sms:code:";
    private static final int SMS_CODE_EXPIRE_MINUTES = 5;
    
    @Override
    public Map<String, Object> loginByPassword(MobileLoginDTO loginDTO) {
        try {
            // 1. 验证图形验证码
            CaptchaVerifyDTO verifyDTO = new CaptchaVerifyDTO();
            verifyDTO.setTicket(loginDTO.getTicket());
            verifyDTO.setRandstr(loginDTO.getRandstr());
            verifyDTO.setUserIp(IpUtils.getIpAddr());
            
            if (!captchaService.verify(verifyDTO)) {
                throw new ServiceException("图形验证码验证失败");
            }
            
            // 2. 查询用户是否存在
            WendaoSysUser user = wendaoSysUserMapper.selectByMobile(loginDTO.getMobile());
            if (user == null) {
                throw new ServiceException("用户不存在");
            }

            //TODO:判断用户状态
            
            // 3. 校验密码
            if (!PasswordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
                throw new ServiceException("密码错误");
            }
            
            // 4. 生成token
            String token = jwtUtil.generateToken(user.getbUserId());
            
            // 5. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", user);
            return result;
        } catch (Exception e) {
            log.error("密码登录失败：mobile={}", loginDTO.getMobile(), e);
            throw new ServiceException(e.getMessage());
        }
    }
    
    @Override
    public Map<String, Object> loginByCode(MobileCodeLoginDTO loginDTO) {
        // 1. 校验验证码
        String cacheKey = SMS_CODE_KEY + loginDTO.getMobile();
        String cacheCode = (String) redisTemplate.opsForValue().get(cacheKey);
        if (!StringUtils.hasText(cacheCode)) {
            throw new ServiceException("验证码已过期");
        }
        if (!cacheCode.equals(loginDTO.getCode())) {
            throw new ServiceException("验证码错误");
        }
        
        // 2. 查询短信记录
        WendaoSmsLog smsLog = wendaoSmsLogMapper.selectByUuid(loginDTO.getUuid());
        if (smsLog == null || !smsLog.getMobile().equals(loginDTO.getMobile()) || !smsLog.getCode().equals(loginDTO.getCode())) {
            throw new ServiceException("验证码错误");
        }
        
        // 3. 查询用户是否存在
        WendaoSysUser user = wendaoSysUserMapper.selectByMobile(loginDTO.getMobile());
        if (user == null) {
            // 4. 用户不存在，自动注册
            user = new WendaoSysUser();
            user.setbUserId(UserIdGenerator.generateUserId());
            user.setMobile(loginDTO.getMobile());
            user.setStatus(1);
            user.setGender(0);  // 默认未知
            user.setCreateTime(new Date());
            user.setUpdateTime(new Date());
            wendaoSysUserMapper.insertWendaoSysUser(user);
        }
        
        // 5. 生成token
        String token = jwtUtil.generateToken(user.getbUserId());
        
        // 6. 删除验证码
        redisTemplate.delete(cacheKey);
        
        // 7. 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);
        return result;
    }
    
    @Override
    public Map<String, Object> sendSmsCode(SendSmsDTO smsDTO) {
        try {
            // 1. 验证图形验证码
            CaptchaVerifyDTO verifyDTO = new CaptchaVerifyDTO();
            verifyDTO.setTicket(smsDTO.getTicket());
            verifyDTO.setRandstr(smsDTO.getRandstr());
            
            // 获取请求IP
            String ip = IpUtils.getIpAddr();
            verifyDTO.setUserIp(ip);
            
            if (!captchaService.verify(verifyDTO)) {
                throw new ServiceException("图形验证码验证失败");
            }
            
            Date now = new Date();
            String uuid = UUID.randomUUID().toString().replace("-", "");
            
            // 2. 生成6位随机验证码
            String code = String.format("%06d", (int) (Math.random() * 1000000));
            
            // 3. 保存验证码到Redis
            String cacheKey = SMS_CODE_KEY + smsDTO.getMobile();
            redisTemplate.opsForValue().set(cacheKey, code, SMS_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            // 4. 调用短信服务发送验证码
            SmsResult smsResult = smsService.sendCode(smsDTO.getMobile(), code);
            if (!smsResult.isSuccess()) {
                throw new ServiceException("短信发送失败：" + smsResult.getMsg());
            }
            log.info("发送短信验证码：mobile={}, code={}", smsDTO.getMobile(), code);
            
            // 5. 记录短信发送日志
            WendaoSmsLog smsLog = new WendaoSmsLog();
            smsLog.setUuid(uuid);
            smsLog.setMobile(smsDTO.getMobile());
            smsLog.setCode(code);
            smsLog.setType(1); // 1-登录
            smsLog.setStatus(1); // 1-成功
            smsLog.setIp(ip);
            smsLog.setSmsId(smsResult.getSid());
            smsLog.setSendTime(smsResult.getSendTime());
            smsLog.setCreateTime(now);
            smsLog.setUpdateTime(now);
            
            wendaoSmsLogMapper.insertWendaoSmsLog(smsLog);
            
            // 6. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("uuid", uuid);
            return result;
        } catch (Exception e) {
            log.error("发送短信验证码失败", e);
            throw new ServiceException("发送验证码失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> sendSmsCodeNoCaptcha(SendSmsNoCaptchaDTO smsDTO) {
        String ip = IpUtils.getIpAddr();
        Date now = new Date();
        String uuid = UUID.randomUUID().toString().replace("-", "");

        // 2. 生成6位随机验证码
        String code = String.format("%06d", (int) (Math.random() * 1000000));

        // 3. 保存验证码到Redis
        String cacheKey = SMS_CODE_KEY + smsDTO.getMobile();
        redisTemplate.opsForValue().set(cacheKey, code, SMS_CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 4. 调用短信服务发送验证码
        SmsResult smsResult = smsService.sendCode(smsDTO.getMobile(), code);
        if (!smsResult.isSuccess()) {
            throw new ServiceException("短信发送失败：" + smsResult.getMsg());
        }
        log.info("发送短信验证码：mobile={}, code={}", smsDTO.getMobile(), code);

        // 5. 记录短信发送日志
        WendaoSmsLog smsLog = new WendaoSmsLog();
        smsLog.setUuid(uuid);
        smsLog.setMobile(smsDTO.getMobile());
        smsLog.setCode(code);
        smsLog.setType(1); // 1-登录
        smsLog.setStatus(1); // 1-成功
        smsLog.setIp(ip);
        smsLog.setSmsId(smsResult.getSid());
        smsLog.setSendTime(smsResult.getSendTime());
        smsLog.setCreateTime(now);
        smsLog.setUpdateTime(now);

        wendaoSmsLogMapper.insertWendaoSmsLog(smsLog);

        // 6. 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("uuid", uuid);
        return result;
    }
} 
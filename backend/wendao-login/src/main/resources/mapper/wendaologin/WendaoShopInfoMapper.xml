<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wendaologin.mapper.WendaoShopInfoMapper">
    
    <resultMap type="WendaoShopInfo" id="WendaoShopInfoResult">
        <result property="shopId"    column="shop_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="shopLogo"    column="shop_logo"    />
        <result property="appId"    column="app_id"    />
        <result property="versionType"    column="version_type"    />
        <result property="useCollection"    column="use_collection"    />
        <result property="status"    column="status"    />
        <result property="isSealed"    column="is_sealed"    />
        <result property="isSeal"    column="is_seal"    />
        <result property="isWaitSeal"    column="is_wait_seal"    />
        <result property="sealApplyTime"    column="seal_apply_time"    />
        <result property="readySealAt"    column="ready_seal_at"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="hasExpired"    column="has_expired"    />
        <result property="hasActivateOrder"    column="has_activate_order"    />
        <result property="lastLogin"    column="last_login"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="bUserId"    column="b_user_id"    />
    </resultMap>

    <sql id="selectWendaoShopInfoVo">
        select shop_id, shop_name, shop_logo, app_id, version_type, use_collection, status, is_sealed, is_seal, is_wait_seal, seal_apply_time, ready_seal_at, expire_time, has_expired, has_activate_order, last_login, create_time, update_time, b_user_id from wendao_shop_info
    </sql>

    <select id="selectWendaoShopInfoList" parameterType="WendaoShopInfo" resultMap="WendaoShopInfoResult">
        <include refid="selectWendaoShopInfoVo"/>
        <where>  
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="shopLogo != null  and shopLogo != ''"> and shop_logo = #{shopLogo}</if>
            <if test="appId != null  and appId != ''"> and app_id = #{appId}</if>
            <if test="versionType != null "> and version_type = #{versionType}</if>
            <if test="useCollection != null "> and use_collection = #{useCollection}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isSealed != null "> and is_sealed = #{isSealed}</if>
            <if test="isSeal != null "> and is_seal = #{isSeal}</if>
            <if test="isWaitSeal != null "> and is_wait_seal = #{isWaitSeal}</if>
            <if test="sealApplyTime != null "> and seal_apply_time = #{sealApplyTime}</if>
            <if test="readySealAt != null "> and ready_seal_at = #{readySealAt}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="hasExpired != null "> and has_expired = #{hasExpired}</if>
            <if test="hasActivateOrder != null "> and has_activate_order = #{hasActivateOrder}</if>
            <if test="lastLogin != null "> and last_login = #{lastLogin}</if>
            <if test="bUserId != null  and bUserId != ''"> and b_user_id = #{bUserId}</if>
        </where>
    </select>
    
    <select id="selectWendaoShopInfoByShopId" parameterType="String" resultMap="WendaoShopInfoResult">
        <include refid="selectWendaoShopInfoVo"/>
        where shop_id = #{shopId}
    </select>
    <select id="checkShopNameExists" resultType="java.lang.Integer">
        select count(*) from wendao_shop_info where shop_name = #{shopName}
    </select>
    <select id="selectByUserId" resultMap="WendaoShopInfoResult">
        <include refid="selectWendaoShopInfoVo"/>
        where b_user_id = #{bUserId}
    </select>

    <insert id="insertWendaoShopInfo" parameterType="WendaoShopInfo">
        insert into wendao_shop_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="shopName != null and shopName != ''">shop_name,</if>
            <if test="shopLogo != null">shop_logo,</if>
            <if test="appId != null and appId != ''">app_id,</if>
            <if test="versionType != null">version_type,</if>
            <if test="useCollection != null">use_collection,</if>
            <if test="status != null">status,</if>
            <if test="isSealed != null">is_sealed,</if>
            <if test="isSeal != null">is_seal,</if>
            <if test="isWaitSeal != null">is_wait_seal,</if>
            <if test="sealApplyTime != null">seal_apply_time,</if>
            <if test="readySealAt != null">ready_seal_at,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="hasExpired != null">has_expired,</if>
            <if test="hasActivateOrder != null">has_activate_order,</if>
            <if test="lastLogin != null">last_login,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="bUserId != null and bUserId != ''">b_user_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="shopName != null and shopName != ''">#{shopName},</if>
            <if test="shopLogo != null">#{shopLogo},</if>
            <if test="appId != null and appId != ''">#{appId},</if>
            <if test="versionType != null">#{versionType},</if>
            <if test="useCollection != null">#{useCollection},</if>
            <if test="status != null">#{status},</if>
            <if test="isSealed != null">#{isSealed},</if>
            <if test="isSeal != null">#{isSeal},</if>
            <if test="isWaitSeal != null">#{isWaitSeal},</if>
            <if test="sealApplyTime != null">#{sealApplyTime},</if>
            <if test="readySealAt != null">#{readySealAt},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="hasExpired != null">#{hasExpired},</if>
            <if test="hasActivateOrder != null">#{hasActivateOrder},</if>
            <if test="lastLogin != null">#{lastLogin},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="bUserId != null and bUserId != ''">#{bUserId},</if>
         </trim>
    </insert>

    <update id="updateWendaoShopInfo" parameterType="WendaoShopInfo">
        update wendao_shop_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopName != null and shopName != ''">shop_name = #{shopName},</if>
            <if test="shopLogo != null">shop_logo = #{shopLogo},</if>
            <if test="appId != null and appId != ''">app_id = #{appId},</if>
            <if test="versionType != null">version_type = #{versionType},</if>
            <if test="useCollection != null">use_collection = #{useCollection},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isSealed != null">is_sealed = #{isSealed},</if>
            <if test="isSeal != null">is_seal = #{isSeal},</if>
            <if test="isWaitSeal != null">is_wait_seal = #{isWaitSeal},</if>
            <if test="sealApplyTime != null">seal_apply_time = #{sealApplyTime},</if>
            <if test="readySealAt != null">ready_seal_at = #{readySealAt},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="hasExpired != null">has_expired = #{hasExpired},</if>
            <if test="hasActivateOrder != null">has_activate_order = #{hasActivateOrder},</if>
            <if test="lastLogin != null">last_login = #{lastLogin},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="bUserId != null and bUserId != ''">b_user_id = #{bUserId},</if>
        </trim>
        where shop_id = #{shopId}
    </update>
    <update id="resetUserShopsLastLogin">
        UPDATE wendao_shop_info SET last_login = 0 WHERE b_user_id = #{bUserId}
    </update>
    <update id="updateShopLastLogin">
        UPDATE wendao_shop_info SET last_login = #{lastLogin} WHERE shop_id = #{shopId}
    </update>

    <delete id="deleteWendaoShopInfoByShopId" parameterType="String">
        delete from wendao_shop_info where shop_id = #{shopId}
    </delete>

    <delete id="deleteWendaoShopInfoByShopIds" parameterType="String">
        delete from wendao_shop_info where shop_id in 
        <foreach item="shopId" collection="array" open="(" separator="," close=")">
            #{shopId}
        </foreach>
    </delete>
</mapper>
# RuoYi项目

## 一、基础提示原则

### 1. 明确角色定位
```
你是一位专业的Java企业级开发专家，精通RuoYi框架的开发。请基于以下技术栈帮助我完成开发任务：

后端技术栈：
- 核心框架：Spring Boot 2.5.15
- 安全框架：Spring Security 5.7.12
- 缓存中间件：Redis
- 认证机制：JWT 0.9.1
- 数据库：MySQL
- 连接池：Druid 1.2.23
- ORM框架：MyBatis
- 工具库：Hutool 5.8.35
- 接口文档：Swagger 3.0.0
- 定时任务：Quartz
- 模板引擎：Velocity 2.3
- 验证码：Kaptcha 2.3.3

前端技术栈：
- 核心框架：Vue
- UI框架：Element UI
- 构建工具：Vite
- 包管理：npm/yarn
- 图表：Echarts
- 富文本编辑器：Quill
- 代码语法高亮：Prism
```

### 2. 任务描述规范
- 提供具体的业务场景
- 明确功能需求
- 指定技术限制
- 说明期望输出

示例：
```
请帮我实现一个基于RuoYi框架的[具体业务]模块，要求：
1. 需要包含的功能：[具体功能列表]
2. 数据表结构：[表结构描述]
3. 权限要求：[权限设计]
4. 期望输出：[代码/文档等具体交付物]
5. 技术限制：[版本/依赖等限制]
6. 性能要求：[响应时间/并发数等]
```

### 3. 代码生成相关
```
请帮我使用RuoYi的代码生成器，生成[模块名称]模块的基础代码：
1. 表名：[表名]
2. 字段结构：[详细的字段信息]
3. 特殊要求：
   - 是否需要树结构：[是/否]
   - 是否需要导入导出：[是/否]
   - 是否需要主子表：[是/否]
   - 是否需要图片上传：[是/否]
   - 是否需要富文本编辑器：[是/否]
4. 生成配置：
   - 作者：[作者名称]
   - 包路径：[包名]
   - 模块名：[模块名]
   - 功能名：[功能名]
```

## 二、场景化提示模板

### 1. 新增功能模块
```
我需要在RuoYi框架中新增[模块名称]功能，具体要求：
1. 业务背景：[描述业务场景]
2. 功能清单：
   - [功能1]
   - [功能2]
3. 技术要求：
   - 后端技术：[具体技术要求]
   - 前端要求：[UI/交互要求]
4. 数据结构：[表结构设计]
5. 权限设计：[权限矩阵]
6. 缓存设计：[缓存策略]
7. 接口文档：[Swagger配置]
```

### 2. 问题排查模板
```
我在RuoYi项目中遇到了[问题描述]，环境信息：
1. 环境信息：
   - 操作系统：[系统版本]
   - JDK版本：[JDK版本]
   - 项目版本：[RuoYi版本]
   - 浏览器版本：[浏览器信息]
2. 问题现象：
   - 具体表现：[详细描述]
   - 错误日志：[日志内容]
   - 复现步骤：[详细步骤]
3. 排查方向：
   - 配置检查：[配置信息]
   - 日志分析：[日志内容]
   - 数据库检查：[SQL分析]
   - 缓存检查：[缓存状态]
4. 已尝试的解决方案：[已尝试的方案]
```

### 3. 性能优化
```
需要优化RuoYi项目中的[具体模块]性能，当前情况：
1. 性能指标：
   - 响应时间：[具体数据]
   - 并发数：[具体数据]
   - CPU使用率：[具体数据]
   - 内存使用：[具体数据]
2. 问题描述：[性能瓶颈]
3. 期望目标：[优化目标]
4. 优化方向：
   - SQL优化
   - 缓存优化
   - JVM调优
   - 代码优化
5. 限制条件：[优化限制]
```

### 4. 安全加固
```
请帮我检查并加固[具体模块]的安全性：
1. 重点关注：
   - 权限控制：[权限矩阵]
   - 数据加密：[加密算法]
   - SQL注入：[防护措施]
   - XSS防护：[防护配置]
   - CSRF防护：[Token配置]
2. 当前配置：[现有安全配置]
3. 安全要求：[具体安全要求]
4. 安全扫描：[漏洞扫描结果]
```

## 三、配置示例

### 1. application.yml配置
```yaml
# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: ${ruoyi.version}
  # 版权年份
  copyrightYear: 2024
  # 文件路径
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: true

# 开发环境配置
server:
  # 服务器的HTTP端口
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数
    accept-count: 1000
    threads:
      # tomcat最大线程数
      max: 800
      # Tomcat启动初始化的线程数
      min-spare: 100

# Spring配置
spring:
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: root
    password: password
    druid:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20

  # redis配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: 
    # 连接超时时间
    timeout: 10s
```

### 2. logback-spring.xml配置
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志存放路径 -->
    <property name="log.path" value="/home/<USER>/logs" />
    <!-- 日志输出格式 -->
    <property name="log.pattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - [%method,%line] - %msg%n" />

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>
    
    <!-- 系统日志输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sys-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${log.path}/sys-info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>
</configuration>
```

### 3. 多数据源配置
```java
@Configuration
public class DataSourceConfig {
    @Bean
    @ConfigurationProperties("spring.datasource.druid.master")
    public DataSource masterDataSource(DruidProperties druidProperties) {
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        return druidProperties.dataSource(dataSource);
    }

    @Bean
    @ConfigurationProperties("spring.datasource.druid.slave")
    @ConditionalOnProperty(prefix = "spring.datasource.druid.slave", name = "enabled", havingValue = "true")
    public DataSource slaveDataSource(DruidProperties druidProperties) {
        DruidDataSource dataSource = DruidDataSourceBuilder.create().build();
        return druidProperties.dataSource(dataSource);
    }
}
```

## 四、部署相关

### 1. 部署脚本
```bash
#!/bin/bash
# 项目路径
APP_PATH=/usr/local/ruoyi
# 项目名称
APP_NAME=ruoyi-admin.jar
# JVM参数
JVM_OPTS="-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m"

# 停止服务
stop(){
    echo "停止应用..."
    pid=`ps -ef | grep ${APP_NAME} | grep -v grep | awk '{print $2}'`
    if [ -n "$pid" ]; then
        kill -9 $pid
    fi
}

# 启动服务
start(){
    echo "启动应用..."
    nohup java ${JVM_OPTS} -jar ${APP_PATH}/${APP_NAME} --spring.profiles.active=prod > /dev/null 2>&1 &
}

# 重启服务
restart(){
    stop
    sleep 2
    start
}

case "$1" in
    "start")
        start
        ;;
    "stop")
        stop
        ;;
    "restart")
        restart
        ;;
    *)
        echo "请输入: start|stop|restart"
        ;;
esac
```

### 2. Nginx配置
```nginx
server {
    listen       80;
    server_name  ruoyi.com;
    
    location / {
        root   /usr/local/ruoyi/dist;
        try_files $uri $uri/ /index.html;
        index  index.html index.htm;
    }
    
    location /prod-api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 五、常见问题解决方案

### 1. 跨域问题
```java
@Configuration
public class CorsConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowCredentials(true)
                .maxAge(3600)
                .allowedHeaders("*");
    }
}
```

### 2. 文件上传配置
```java
@Configuration
public class ResourcesConfig implements WebMvcConfigurer {
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        /** 本地文件上传路径 */
        registry.addResourceHandler("/profile/**")
                .addResourceLocations("file:" + RuoYiConfig.getProfile() + "/");
    }
}
```

### 3. 定时任务配置
```java
@Configuration
@EnableAsync
public class ScheduleConfig {
    @Bean
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(20);
        scheduler.setThreadNamePrefix("task-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(60);
        return scheduler;
    }
}
```

## 六、代码示例参考

### 1. Controller层示例
```java
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;
    
    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }
    
    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user) {
        // 业务逻辑
        return toAjax(userService.insertUser(user));
    }
}
```

### 2. Service层示例
```java
@Service
public class SysUserServiceImpl implements ISysUserService {
    @Autowired
    private SysUserMapper userMapper;
    
    /**
     * 查询用户列表
     */
    @Override
    public List<SysUser> selectUserList(SysUser user) {
        return userMapper.selectUserList(user);
    }
    
    /**
     * 新增用户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUser user) {
        // 加密密码
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        // 新增用户信息
        return userMapper.insertUser(user);
    }
}
```

### 3. Mapper层示例
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">
    
    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
        select u.* from sys_user u
        where u.del_flag = '0'
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
    </select>
    
    <insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user(
            user_name,
            nick_name,
            password
        )values(
            #{userName},
            #{nickName},
            #{password}
        )
    </insert>
</mapper>
```

### 4. 实体类示例
```java
@Data
@TableName("sys_user")
public class SysUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Excel(name = "用户序号", cellType = ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;

    @Excel(name = "用户名称")
    private String userName;

    @Excel(name = "用户昵称")
    private String nickName;

    @Excel(name = "密码")
    private String password;
}
```

### 5. 前端Vue组件示例
```vue
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="userList">
      <el-table-column label="用户编号" align="center" prop="userId" />
      <el-table-column label="用户名称" align="center" prop="userName" />
      <el-table-column label="用户昵称" align="center" prop="nickName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { listUser, delUser } from "@/api/system/user";

export default {
  name: "User",
  data() {
    return {
      loading: true,
      userList: [],
      queryParams: {
        userName: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.loading = false;
      });
    }
  }
};
</script>
```

### 6. API请求示例
```javascript
// api/system/user.js
import request from '@/utils/request'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}
```

### 7. 权限注解使用示例
```java
// 菜单权限
@PreAuthorize("@ss.hasPermi('system:user:list')")

// 角色权限
@PreAuthorize("@ss.hasRole('admin')")

// 数据权限
@DataScope(deptAlias = "d", userAlias = "u")

// 日志注解
@Log(title = "用户管理", businessType = BusinessType.INSERT)

// 防重复提交
@RepeatSubmit(interval = 5000)
```

### 8. 异常处理示例
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(CustomException.class)
    public AjaxResult customException(CustomException e) {
        return AjaxResult.error(e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e) {
        return AjaxResult.error("服务器错误，请联系管理员");
    }
}
```

### 9. 定时任务示例
```java
@Component("ryTask")
public class RyTask {
    
    @Async
    public void ryMultipleParams(String s, Boolean b, Long l, Double d, Integer i) {
        System.out.println("执行多参方法：" + s + " " + b + " " + l + " " + d + " " + i);
    }
}
```

### 10. Redis缓存使用示例
```java
@Component
public class RedisCache {
    @Autowired
    public RedisTemplate redisTemplate;
    
    public <T> void setCacheObject(final String key, final T value, final Integer timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }
    
    public <T> T getCacheObject(final String key) {
        ValueOperations<String, T> operation = redisTemplate.opsForValue();
        return operation.get(key);
    }
}
```

这些代码示例可以帮助AI更好地理解RuoYi框架的各个组件和最佳实践。在提问时，可以参考这些示例来说明你的需求或问题。

## 七、最佳实践建议

1. **编码规范**
   - 遵循阿里巴巴Java开发手册
   - 使用统一的代码格式化工具
   - 保持命名规范统一
   - 编写完整的注释和文档

2. **安全实践**
   - 使用HTTPS
   - 实施密码强度策略
   - 实现登录失败次数限制
   - 定期更新安全补丁
   - 对敏感数据加密

3. **性能优化**
   - 合理使用缓存
   - 优化SQL查询
   - 使用批量操作
   - 避免大事务
   - 合理设置线程池参数

4. **部署建议**
   - 使用CI/CD自动化部署
   - 实施监控和告警
   - 定期备份数据
   - 制定回滚方案
   - 做好环境隔离

5. **测试策略**
   - 单元测试覆盖核心业务
   - 进行接口自动化测试
   - 压力测试关键接口
   - 安全漏洞扫描

package com.ruoyi.wendaologin.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.wendaologin.domain.WendaoSysLoginLog;
import com.ruoyi.wendaologin.domain.WendaoSysUser;
import com.ruoyi.wendaologin.domain.WendaoWxQrcodeRecord;
import com.ruoyi.wendaologin.dto.CheckSessionResultDTO;
import com.ruoyi.wendaologin.dto.LoginInfoDTO;
import com.ruoyi.wendaologin.dto.WxCheckSessionDTO;
import com.ruoyi.wendaologin.dto.WxPhoneDTO;
import com.ruoyi.wendaologin.mapper.WendaoSysLoginLogMapper;
import com.ruoyi.wendaologin.mapper.WendaoSysUserMapper;
import com.ruoyi.wendaologin.service.WxLoginService;
import com.ruoyi.wendaologin.util.HmacSHA256Utils;
import com.ruoyi.wendaologin.util.JwtUtil;
import com.ruoyi.wendaologin.util.UserIdGenerator;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WxLoginServiceImpl implements WxLoginService {

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private WendaoSysUserMapper sysUserMapper;

    @Autowired
    private WendaoSysLoginLogMapper loginLogMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RestTemplate restTemplate;

    private static final String SCAN_STATUS_KEY = "wx:scan:status:";
    private static final String USER_SESSION_KEY = "wx:user:session:";
    private static final String LOGIN_INFO_KEY = "wx:login:info:";
    private static final String CHECK_SESSION_URL = "https://api.weixin.qq.com/wxa/checksession?access_token=%s&signature=%s&openid=%s&sig_method=%s";
    private static final int EXPIRE_MINUTES = 5;

    @Override
    public Map<String, Object> generateWxaCode(Integer businessType) {
        try {
            String scene = IdUtil.simpleUUID();
            String page = "pages/login/login";

            // 生成小程序码,这个小程序码不能带参数只能是页面路径,要带参数可以通过scene来传递
            byte[] qrcodeBytes = wxMaService.getQrcodeService()
                    .createWxaCodeUnlimitBytes(
                            scene,                          // 场景值，最大32个字符
                            page,                           // 小程序页面路径
                            false,                          // checkPath: 检查页面是否存在
                            "develop",                     // envVersion: 正式版
                            430,                           // width: 二维码宽度
                            true,                          // autoColor: 自动配置线条颜色
                            new WxMaCodeLineColor("0", "0", "0"),  // lineColor: 使用黑色
                            false                          // isHyaline: 不使用透明底色
                    );

            // 保存记录
            WendaoWxQrcodeRecord record = new WendaoWxQrcodeRecord();
            record.setScene(scene);
            record.setPagePath(page);
            record.setBusinessType(businessType);
            record.setExpireTime(DateUtil.offsetMinute(new Date(), EXPIRE_MINUTES));
            record.setStatus(1);
            // TODO: 保存记录到数据库

            // 设置扫码状态
            redisCache.setCacheObject(SCAN_STATUS_KEY + scene, 0, EXPIRE_MINUTES, TimeUnit.MINUTES);

            Map<String, Object> result = new HashMap<>();
            result.put("scene", scene);
            result.put("qrcode", qrcodeBytes);
            return result;
        } catch (Exception e) {
            log.error("生成小程序码失败", e);
            throw new ServiceException("生成小程序码失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> handleScan(String scene, String code) {
        try {
            // 1. 检查code是否已被使用
            String cacheKey = "wx:code:" + code;
            if (Boolean.TRUE.equals(redisCache.hasKey(cacheKey))) {
                throw new ServiceException("code已被使用");
            }

            // 2. 调用微信接口获取session信息
            WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(code);

            // 3. 标记code已使用，防止重复调用
            redisCache.setCacheObject(cacheKey, "1", 5, TimeUnit.MINUTES);

            // 4. 缓存sessionKey，用于后续手机号解密
            String sessionKey = session.getSessionKey();
            String openid = session.getOpenid();
            String unionid = session.getUnionid();
            System.out.println("session:"+JSON.toJSONString(session));
            // 同时保存scene和openid对应的session_key
            redisCache.setCacheObject(USER_SESSION_KEY + scene, sessionKey, EXPIRE_MINUTES, TimeUnit.MINUTES);
            redisCache.setCacheObject(USER_SESSION_KEY + openid, sessionKey, EXPIRE_MINUTES, TimeUnit.MINUTES);

            // 5. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("openId", openid);
            result.put("unionId", unionid);
            return result;
        } catch (WxErrorException e) {
            log.error("调用微信接口失败", e);
            throw new ServiceException("处理扫码失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginInfoDTO decryptPhone(WxPhoneDTO phoneDTO) {
        try {
            String sessionKey;
            String openId;
            String unionId = null;

            if (StringUtils.hasText(phoneDTO.getOpenid())) {
                // 1. 使用openid获取session_key
                openId = phoneDTO.getOpenid();
                unionId = phoneDTO.getUnionid();
                sessionKey = redisCache.getCacheObject(USER_SESSION_KEY + openId);
                if (!StringUtils.hasText(sessionKey)) {
                    throw new ServiceException("session已失效，请重新获取");
                }
            } else if (StringUtils.hasText(phoneDTO.getCode())) {
                // 2. 使用code获取session_key
                String cacheKey = "wx:code:" + phoneDTO.getCode();
                if (Boolean.TRUE.equals(redisCache.hasKey(cacheKey))) {
                    throw new ServiceException("code已被使用");
                }

                // 获取会话信息
                WxMaJscode2SessionResult session = wxMaService.getUserService().getSessionInfo(phoneDTO.getCode());
                sessionKey = session.getSessionKey();
                openId = session.getOpenid();
                unionId = session.getUnionid();
                System.out.println("session:"+JSON.toJSONString(session));

                // 标记code已使用
                redisCache.setCacheObject(cacheKey, "1", 5, TimeUnit.MINUTES);

                // 保存新的session_key
                redisCache.setCacheObject(USER_SESSION_KEY + openId, sessionKey, EXPIRE_MINUTES, TimeUnit.MINUTES);
            } else {
                throw new ServiceException("openid和code不能同时为空");
            }

            // 3. 解密手机号
            WxMaPhoneNumberInfo phoneNumberInfo = wxMaService.getUserService().getPhoneNoInfo(sessionKey, phoneDTO.getEncryptedData(), phoneDTO.getIv());
            String phone = phoneNumberInfo.getPhoneNumber();

            // 4. 查询用户是否存在
            WendaoSysUser user = sysUserMapper.selectByWxOpenId(openId);
            boolean isNewUser = false;

            if (user == null) {
                // 检查手机号是否被其他用户使用
                WendaoSysUser existUser = sysUserMapper.selectByMobile(phone);
                if (existUser != null) {
                    throw new ServiceException("该手机号已被其他用户绑定");
                }

                // 5. 新用户，创建用户信息
                user = new WendaoSysUser();
                user.setbUserId(UserIdGenerator.generateUserId());
                user.setWxOpenId(openId);
                if (StringUtils.hasText(unionId)) {
                    user.setWxUnionId(unionId);
                }
                user.setMobile(phone);
                user.setStatus(1);
                user.setGender(0);  // 默认未知
                user.setCreateTime(new Date());
                user.setUpdateTime(new Date());
                sysUserMapper.insertWendaoSysUser(user);
                isNewUser = true;
            } else {
                // 6. 老用户，更新手机号和unionid
                user.setMobile(phone);
                if (StringUtils.hasText(unionId)) {
                    user.setWxUnionId(unionId);
                }
                user.setUpdateTime(new Date());
                sysUserMapper.updateWendaoSysUser(user);
            }

            // 7. 生成登录token
            String token = jwtUtil.generateToken(user.getbUserId());

            // 8. 记录登录日志
            saveLoginLog(user.getbUserId(), phoneDTO.getScene());

            // 9. 更新扫码状态为已完成，并保存登录信息
            redisCache.setCacheObject(SCAN_STATUS_KEY + phoneDTO.getScene(), 2, EXPIRE_MINUTES, TimeUnit.MINUTES);

            LoginInfoDTO loginInfo = new LoginInfoDTO();
            loginInfo.setToken(token);
            loginInfo.setUser(user);
            loginInfo.setIsNewUser(isNewUser);
            redisCache.setCacheObject(LOGIN_INFO_KEY + phoneDTO.getScene(), loginInfo, EXPIRE_MINUTES, TimeUnit.MINUTES);

            return loginInfo;
        } catch (Exception e) {
            log.error("手机号解密失败", e);
            throw new ServiceException("手机号解密失败: " + e.getMessage());
        }
    }

    @Override
    public LoginInfoDTO checkScanStatus(String scene) {
        // 获取扫码状态
        Integer status = redisCache.getCacheObject(SCAN_STATUS_KEY + scene);
        //result.put("status", status != null ? status : -1);

        // 如果已登录成功，返回登录信息
        if (status != null && status == 2) {
            LoginInfoDTO loginInfo = redisCache.getCacheObject(LOGIN_INFO_KEY + scene);
            if (loginInfo != null) {
                loginInfo.setStatus(status);
                // 登录成功后删除Redis中的信息
                redisCache.deleteObject(SCAN_STATUS_KEY + scene);
                redisCache.deleteObject(LOGIN_INFO_KEY + scene);
                redisCache.deleteObject(USER_SESSION_KEY + scene);
                return loginInfo;
            }
        }
        //其他情况返回
        LoginInfoDTO loginInfoDTO = new LoginInfoDTO();
        loginInfoDTO.setStatus(status);
        return loginInfoDTO;
    }

    /**
     * 保存登录日志
     */
    private void saveLoginLog(String bUserId, String scene) {
        try {
            WendaoSysLoginLog loginLog = new WendaoSysLoginLog();
            loginLog.setbUserId(bUserId);
            loginLog.setLoginType(1); // 1: 微信扫码
            loginLog.setIp(IpUtils.getIpAddr());
            loginLog.setDeviceInfo("小程序");
            loginLog.setCreateTime(new Date());
            loginLogMapper.insertWendaoSysLoginLog(loginLog);
            log.info("保存登录日志成功，bUserId={}", bUserId);
        } catch (Exception e) {
            log.error("保存登录日志失败，bUserId={}", bUserId, e);
        }
    }

    @Override
    public Boolean checkSession(WxCheckSessionDTO dto) {
        try {
            // 从Redis获取session_key
            String sessionKey = redisCache.getCacheObject(USER_SESSION_KEY + dto.getOpenid());
            if (!StringUtils.hasText(sessionKey)) {
                return false;
            }

            // 计算签名 (使用空字符串进行签名)
            String signature = HmacSHA256Utils.sign("", sessionKey);

            // 获取access_token
            String accessToken = wxMaService.getAccessToken();

            //wxMaService.getWxMaSchemeService()

            // 调用微信接口检查session
            String url = String.format(CHECK_SESSION_URL,
                    accessToken,
                    signature,
                    dto.getOpenid(),
                    "hmac_sha256"
            );

            CheckSessionResultDTO response = restTemplate.getForObject(url, CheckSessionResultDTO.class);

            // 检查响应结果
            if (response != null && response.getErrcode() != null && response.getErrcode() == 0) {
                return true;
            }

            // session无效，删除Redis中的记录
            redisCache.deleteObject(USER_SESSION_KEY + dto.getOpenid());
            return false;

        } catch (Exception e) {
            log.error("检查session失败", e);
            return false;
        }
    }
} 
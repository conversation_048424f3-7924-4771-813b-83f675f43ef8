package com.ruoyi.wendaologin.util;

import java.security.SecureRandom;

public class ShopIdGenerator {
    
    private static final String CHARACTERS = "abcdefghijklmnopqrstuvwxyz0123456789";
    private static final int SHOP_ID_LENGTH = 12; // 不包含前缀的长度
    private static final SecureRandom random = new SecureRandom();

    /**
     * 生成随机店铺ID
     *
     * @return 随机生成的店铺ID，前缀为 app
     */
    public static String generateShopId() {
        StringBuilder shopId = new StringBuilder("app"); // 添加前缀
        
        // 生成随机字符
        for (int i = 0; i < SHOP_ID_LENGTH; i++) {
            int index = random.nextInt(CHARACTERS.length());
            shopId.append(CHARACTERS.charAt(index));
        }
        
        return shopId.toString();
    }

    /**
     * 验证店铺ID格式是否正确
     *
     * @param shopId 店铺ID
     * @return true:格式正确 false:格式错误
     */
    public static boolean validateShopId(String shopId) {
        if (shopId == null || shopId.length() != SHOP_ID_LENGTH + 3) { // 3是前缀"app"的长度
            return false;
        }
        
        if (!shopId.startsWith("app")) {
            return false;
        }
        
        // 验证剩余字符是否都是小写字母或数字
        String remaining = shopId.substring(3);
        return remaining.matches("^[a-z0-9]+$");
    }
} 
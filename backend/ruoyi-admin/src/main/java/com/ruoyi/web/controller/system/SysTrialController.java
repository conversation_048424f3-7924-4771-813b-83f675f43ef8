package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.TrialPermission;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.service.TrialPermissionService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.domain.SysTrialPermission;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.ISysTrialPermissionService;

/**
 * 试用管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/trial")
public class SysTrialController extends BaseController
{
    @Autowired
    private ISysTenantService tenantService;
    
    @Autowired
    private ISysTrialPermissionService trialPermissionService;
    
    @Autowired
    private TrialPermissionService trialService;

    /**
     * 查询试用权限配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:trial:list')")
    @TrialPermission(value = "system:trial:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/permission/list")
    public TableDataInfo permissionList(SysTrialPermission sysTrialPermission)
    {
        startPage();
        List<SysTrialPermission> list = trialPermissionService.selectSysTrialPermissionList(sysTrialPermission);
        return getDataTable(list);
    }

    /**
     * 导出试用权限配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:trial:export')")
    @TrialPermission(value = "system:trial:export", type = 1, trialAvailable = true, expiredAvailable = true)
    @Log(title = "试用权限配置", businessType = BusinessType.EXPORT)
    @PostMapping("/permission/export")
    public void export(HttpServletResponse response, SysTrialPermission sysTrialPermission)
    {
        List<SysTrialPermission> list = trialPermissionService.selectSysTrialPermissionList(sysTrialPermission);
        ExcelUtil<SysTrialPermission> util = new ExcelUtil<SysTrialPermission>(SysTrialPermission.class);
        util.exportExcel(response, list, "试用权限配置数据");
    }

    /**
     * 获取试用权限配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:trial:query')")
    @TrialPermission(value = "system:trial:query", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/permission/{id}")
    public AjaxResult getPermissionInfo(@PathVariable("id") Long id)
    {
        return success(trialPermissionService.selectSysTrialPermissionById(id));
    }

    /**
     * 新增试用权限配置
     */
    @PreAuthorize("@ss.hasPermi('system:trial:add')")
    @TrialPermission(value = "system:trial:add", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "试用权限配置", businessType = BusinessType.INSERT)
    @PostMapping("/permission")
    public AjaxResult addPermission(@Validated @RequestBody SysTrialPermission sysTrialPermission)
    {
        return toAjax(trialPermissionService.insertSysTrialPermission(sysTrialPermission));
    }

    /**
     * 修改试用权限配置
     */
    @PreAuthorize("@ss.hasPermi('system:trial:edit')")
    @TrialPermission(value = "system:trial:edit", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "试用权限配置", businessType = BusinessType.UPDATE)
    @PutMapping("/permission")
    public AjaxResult editPermission(@Validated @RequestBody SysTrialPermission sysTrialPermission)
    {
        return toAjax(trialPermissionService.updateSysTrialPermission(sysTrialPermission));
    }

    /**
     * 删除试用权限配置
     */
    @PreAuthorize("@ss.hasPermi('system:trial:remove')")
    @TrialPermission(value = "system:trial:remove", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "试用权限配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/permission/{ids}")
    public AjaxResult removePermission(@PathVariable Long[] ids)
    {
        return toAjax(trialPermissionService.deleteSysTrialPermissionByIds(ids));
    }

    /**
     * 查询租户试用状态列表
     */
    @PreAuthorize("@ss.hasPermi('system:trial:list')")
    @TrialPermission(value = "system:trial:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/tenant/list")
    public TableDataInfo tenantList(SysTenant sysTenant)
    {
        startPage();
        List<SysTenant> list = tenantService.selectTenantList(sysTenant);
        return getDataTable(list);
    }

    /**
     * 开始试用
     */
    @PreAuthorize("@ss.hasPermi('system:trial:start')")
    @Log(title = "开始试用", businessType = BusinessType.UPDATE)
    @PostMapping("/start")
    public AjaxResult startTrial(@RequestParam String tenantId, @RequestParam(defaultValue = "7") int trialDays)
    {
        boolean result = trialService.startTrial(tenantId, trialDays);
        return result ? success("试用开始成功") : error("试用开始失败");
    }

    /**
     * 租户转正
     */
    @PreAuthorize("@ss.hasPermi('system:trial:convert')")
    @Log(title = "租户转正", businessType = BusinessType.UPDATE)
    @PostMapping("/convert")
    public AjaxResult convertTenant(@RequestParam String tenantId)
    {
        boolean result = trialService.convertTenant(tenantId);
        return result ? success("转正成功") : error("转正失败");
    }

    /**
     * 获取租户试用状态
     */
    @PreAuthorize("@ss.hasPermi('system:trial:query')")
    @TrialPermission(value = "system:trial:query", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/tenant/{tenantId}")
    public AjaxResult getTenantTrialStatus(@PathVariable("tenantId") String tenantId)
    {
        SysTenant tenant = tenantService.selectTenantById(tenantId);
        if (tenant == null)
        {
            return error("租户不存在");
        }
        return success(tenant);
    }

    /**
     * 清除试用缓存
     */
    @PreAuthorize("@ss.hasPermi('system:trial:cache')")
    @Log(title = "清除试用缓存", businessType = BusinessType.CLEAN)
    @DeleteMapping("/cache/{tenantId}")
    public AjaxResult clearTrialCache(@PathVariable("tenantId") String tenantId)
    {
        trialService.clearTenantTrialCache(tenantId);
        return success("缓存清除成功");
    }
} 

提高结果质量的六大策略
写清晰的指令
这些模型无法读懂你的想法。如果输出过长，要求简短回复；如果输出过于简单，要求专家级写作；如果你不喜欢格式，展示你想要看到的格式。模型猜测你想要的内容越少，你获得期望结果的可能性就越大。

实用技巧：
在查询中包含细节以获取更相关的答案。

要求模型采用特定的角色。

使用分隔符清楚地标明输入的不同部分。

明确完成任务所需的步骤。

提供示例。

指定输出的期望长度。

提供参考文本
语言模型在被问及深奥话题或引用和URL时，可能会自信地编造虚假答案。就像笔记可以帮助学生在考试中取得更好的成绩一样，向模型提供参考文本可以帮助其提供更少虚构的答案。

实用技巧：
指示模型使用参考文本回答。

指示模型使用参考文本中的引用回答。

将复杂任务分解为更简单的子任务
就像在软件工程中将复杂系统分解为一组模块化组件一样，提交给语言模型的任务也是如此。复杂任务的错误率往往比简单任务更高。此外，复杂任务通常可以重新定义为简单任务的工作流，其中较早任务的输出用于构建后续任务的输入。

实用技巧：
使用意图分类来识别用户查询中最相关的指令。

对于需要非常长对话的对话应用程序，概括或过滤以前的对话。

分段概括长文档，并递归地构建完整摘要。

给模型时间“思考”
如果被要求计算17乘以28，你可能不会立即知道答案，但仍然可以花时间计算出来。同样地，模型在试图立即回答问题时，更容易犯推理错误，而不是花时间计算答案。在回答之前要求“思考链”可以帮助模型更可靠地推理出正确答案。

实用技巧：
指示模型在匆忙得出结论之前，先计算出自己的解决方案。

使用内部独白或一系列查询来隐藏模型的推理过程。

询问模型是否在之前的尝试中遗漏了什么。

使用外部工具
通过使用其他工具的输出来弥补模型的弱点。例如，文本检索系统（有时称为RAG或检索增强生成）可以告诉模型有关相关文档的信息。像OpenAI的代码解释器这样的代码执行引擎可以帮助模型进行数学运算和运行代码。如果某个任务可以通过工具而不是语言模型更可靠或有效地完成，则将其卸载以获得两者的最佳效果。

实用技巧：
使用基于嵌入的搜索来实现高效的知识检索。

使用代码执行进行更准确的计算或调用外部API。

让模型访问特定功能。

系统性测试变化
如果能够衡量性能，改善性能就更容易。在某些情况下，对提示词的修改可能会在一些孤立的示例上提高性能，但在更具代表性的示例集上导致整体性能下降。因此，为了确定更改对性能的净增益，可能需要定义一个全面的测试套件（也称为“评估”）。

实用技巧：
根据金标准答案评估模型输出。

每种策略的具体技巧
以上列出的每种策略都可以通过具体的技巧来实现。这些技巧旨在提供尝试的想法。它们可能不是全面的，你可以自由地尝试这里没有提到的创造性想法。

策略：写清晰的指令
技巧：在查询中包含细节以获得更相关的答案
为了获得高度相关的回应，请确保请求提供任何重要的细节或背景。否则，你就把意图交给模型来猜测。

差的例子 vs 更好的例子
如何在Excel中加数字？ -> 如何在Excel中计算一行美元金额的总和？我想在一个表格的所有行自动完成这个操作，并且所有的总和都在右边名为“总计”的一列中。

技巧：要求模型采用特定角色
系统消息可以用来指定模型在回复中使用的角色。

系统：当我请求帮助写作时，你将以每段至少包含一个笑话或俏皮评论的文档回复。

用户：给我的供应商写一封感谢信，感谢他们及时和短通知内完成交付。这让我们能够及时交付重要订单。

技巧：使用分隔符清晰标明输入的不同部分
分隔符，如三重引号、XML标签、节标题等，可以帮助区分需要不同处理的文本部分。

用户：总结由三重引号分隔的文本。
"""在此处插入文本"""

技巧：明确完成任务所需的步骤
某些任务最好用一系列步骤来指定。明确写出步骤可以让模型更容易遵循。

系统：使用以下逐步指令来响应用户输入。
步骤1 - 用户将提供带有三重引号的文本。用“摘要：”作为前缀，将此文本总结为一句话。

步骤2 - 将步骤1的摘要翻译成西班牙语，前缀为“翻译：”。

技巧：提供示例
提供适用于所有示例的通用指令通常比通过示例展示任务的所有变化更高效，但在某些情况下提供示例可能更容易。例如，如果你打算让模型复制一种难以明确描述的响应用户查询的特定风格。这被称为“少量示例”提示。

系统：以一致的风格回答。

用户：教我如何耐心。
助理：雕刻最深谷的河流源自谦逊的泉水；最宏伟的交响乐起源于单一的音符；最复杂的挂毯始于孤独的线索。

技巧：指定输出的期望长度
你可以要求模型生成特定目标长度的输出。目标输出长度可以用词数、句子数、段落数、项目符号列表等来指定。然而，请注意，指示模型生成特定数量的单词并不会非常准确。模型可以更可靠地生成特定数量的段落或项目符号列表。

用户：用大约50个单词总结由三重引号分隔的文本。
"""在此处插入文本"""

策略：提供参考文本
技巧：指示模型使用参考文本回答
如果我们能为模型提供与当前查询相关的可信信息，那么我们可以指示模型使用提供的信息来构建答案。

系统：使用由三重引号分隔的提供文章回答问题。如果答案不能在文章中找到，请写“我找不到答案。”

技巧：使用引用文本回答问题
如果输入中补充了相关知识，直接要求模型通过引用提供的文档中的段落来增加答案中的引用就很简单。注意，输出中的引用随后可以通过字符串匹配在提供的文档中进行验证。

系统：您将获得一个由三重引号分隔的文档和一个问题。您的任务是仅使用提供的文档回答问题，并引用文档中用于回答问题的段落。如果文档不包含回答此问题所需的信息，只需写：“信息不足。”如果提供了问题的答案，则必须附有引用。使用以下格式引用相关段落（{"citation": …}）。

用户："""<插入文档>""" 问题：<插入问题>

策略：将复杂任务分解为更简单的子任务
技巧：使用意图分类来识别用户查询中最相关的指令
对于需要处理不同情况的大量独立指令集的任务，首先对查询类型进行分类，并使用该分类来确定所需的指令是有益的。这可以通过定义固定类别并硬编码处理给定类别任务的相关指令来实现。这个过程也可以递归地应用于将任务分解为一系列阶段。这种方法的优势在于，每个查询只包含执行任务下一阶段所需的指令，与使用单一查询执行整个任务相比，可以降低错误率。这也可能导致成本降低，因为更大的提示词运行成本更高（参见定价信息）。

假设对于客户服务应用程序，查询可以有用地分类如下：

系统：你将提供客户服务查询。将每个查询分类为主要类别和次要类别。以json格式提供输出，键为：主要和次要。

主要类别：账单、技术支持、账户管理或一般咨询。

账单次要类别：

退订或升级

添加支付方式

对费用的解释

对费用提出异议

技术支持次要类别：

故障排除

设备兼容性

软件更新

账户管理次要类别：

密码重置

更新个人信息

关闭账户

账户安全

一般咨询次要类别：

产品信息

定价

反馈

与人交谈

用户：我需要重新让我的互联网工作。

根据客户查询的分类，可以为模型提供更具体的指令来处理下一步。例如，假设客户需要“故障排除”方面的帮助。

系统：你将得到需要在技术支持背景下故障排除的客户服务咨询。通过以下方式帮助用户：
询问他们是否检查了连接到/来自路由器的所有电缆。请注意，电缆随着时间的推移很容易松动。

如果所有电缆都连接好且问题仍然存在，请询问他们使用的是哪种型号的路由器。

现在你将指导他们如何重启设备： -- 如果型号是MTD-327J，建议他们按住红色按钮5秒钟，然后等待5分钟再测试连接。 -- 如果型号是MTD-327S，建议他们拔下并重新插入，然后等待5分钟再测试连接。

如果客户在重启设备并等待5分钟后问题仍然存在，请输出{"IT support requested"}，将他们连接到IT支持。

如果用户开始询问与此话题无关的问题，请确认他们是否希望结束当前关于故障排除的聊天，并根据以下方案对他们的请求进行分类：

<在此处插入上述主要/次要分类方案>

用户：我需要重新让我的互联网工作。

请注意，模型被指示发出特殊字符串以指示对话状态的变化。这使我们能够将系统变为状态机，其中状态决定注入的指令。通过跟踪状态、该状态下相关的指令，以及可选地跟踪从该状态允许的状态转换，我们可以为用户体验设置保护措施，这在较不结构化的方法中难以实现。

技巧：对话应用需要非常长的对话时，总结或过滤之前的对话
由于模型具有固定的上下文长度，用户与助手之间的对话，其中整个对话都包含在上下文窗口中，无法无限期地继续进行。

该问题有多种解决方法，其中之一是总结对话中的先前转折。一旦输入的大小达到预定的阈值长度，这可能会触发一个总结对话部分的查询，并且先前对话的总结可以作为系统消息的一部分包含在内。或者，先前的对话也可以在整个对话过程中异步地在后台进行总结。

另一种解决方案是动态选择与当前查询最相关的对话的先前部分。

技巧：逐段总结长篇文档，并递归构建完整的摘要
由于模型具有固定的上下文长度，因此无法在单个查询中用它们来总结超出上下文长度减去生成的摘要长度的文本。

要总结非常长的文档，例如一本书，我们可以使用一系列查询来总结文档的每个部分。部分摘要可以连接并总结，产生摘要的摘要。此过程可以递归进行，直到整个文档被总结。如果需要使用早期部分的信息来理解后面部分，则进一步的技巧可能是在总结该点的内容时包含文本的运行摘要。

策略：给模型时间“思考”
技巧：在决定之前指导模型先自行找出解决方案
有时在明确指导模型首先根据原理推理后再得出结论时，我们能获得更好的结果。比如，我们希望模型评估学生解决数学问题的答案。最直接的方法是简单地询问模型学生的解决方案是否正确。

系统：确定学生的解决方案是否正确。

用户：问题陈述：我正在建立太阳能发电装置，需要帮助计算财务数据。
土地成本 $100/平方英尺

我可以以每平方英尺 $250 的价格购买太阳能板

我谈判了一份维护合同，每年固定花费 10 第一年运营的总成本是多少，作为平方英尺数量的函数。

学生的解决方案：让 x 代表装置的大小，以平方英尺计。

土地成本：100x

太阳能板成本：250x

维护成本：100,000 + 100x 总成本：100x + 250x + 100,000 + 100x = 450x + 100,000

助手：学生的解决方案是正确的。

但学生的解决方案实际上是错误的！我们可以通过提示模型先生成自己的解决方案来使其成功注意到这一点。

系统：首先自行解决问题。然后比较你的解决方案和学生的解决方案，并评估学生的解决方案是否正确。在自己解决问题之前，不要决定学生的解决方案是否正确。

用户：问题陈述：我正在建立太阳能发电装置，需要帮助计算财务数据。

土地成本 $100/平方英尺

我可以以每平方英尺 $250 的价格购买太阳能板

我谈判了一份维护合同，每年固定花费 10 第一年运营的总成本是多少，作为平方英尺数量的函数。

学生的解决方案：让 x 代表装置的大小，以平方英尺计。

土地成本：100x

太阳能板成本：250x

维护成本：100,000 + 10x 总成本：100x + 250x + 100,000 + 10x = 360x + 100,000

学生的解决方案是错误的。他们在计算维护成本时犯了错误，使用了 100x 而不是 10x。第一年运营的正确总成本是 360x + 100,000。

策略：使用外部工具
技巧：使用基于嵌入的搜索实现有效的知识检索
如果模型的输入中提供了外部信息源，模型可以利用这些信息生成更具信息性和更新的回应。例如，如果用户询问特定电影的问题，可能有用的做法是将电影的高质量信息（例如演员、导演等）添加到模型的输入中。嵌入可以用来实现有效的知识检索，从而可以在运行时动态地将相关信息添加到模型输入中。

文本嵌入是一种向量，可以衡量文本字符串之间的相关性。相似或相关的字符串在嵌入空间中彼此更接近。这一事实，加上快速向量搜索算法的存在，意味着嵌入可以用来实现有效的知识检索。特别是，文本语料库可以被分割成块，每个块都可以被嵌入并存储。然后，可以对给定的查询进行嵌入，并执行向量搜索，以找到语料库中与查询最相关的嵌入文本块（即在嵌入空间中彼此最接近）。

你可以在OpenAI Cookbook中找到示例实现。

技巧：使用代码执行来进行更准确的计算或调用外部API
不能依赖语言模型自行准确地执行算术或长时间的计算。在需要此类操作的情况下，可以指导模型编写并运行代码，而不是自行进行计算。特别是，可以指导模型将要运行的代码放入指定的格式中，比如三个反引号。生成输出后，可以提取并运行代码。最后，如果需要，代码执行引擎（例如Python解释器）的输出可以作为下一个查询的输入提供给模型。

系统：你可以通过将代码包含在三个反引号中来编写和执行Python代码，例如代码放在这里。使用此功能进行计算。

用户：找出以下多项式的所有实数根：3x**5 - 5x4 - 3*x3 - 7*x - 10。

另一个代码执行的好用例是调用外部API。如果模型得到适当的API使用指导，它可以编写利用该API的代码。可以通过提供文档和/或显示如何使用API的代码示例来指导模型如何使用API。

系统：你可以通过将代码包含在三个反引号中来编写和执行Python代码。另外，请注意你可以使用以下模块帮助用户向朋友发送消息： python import message message.write(to="John", message="Hey, want to meetup after work?")

警告：执行模型产生的代码本身并不安全，任何寻求执行此操作的应用程序都应采取预防措施。特别是，需要一个沙盒化的代码执行环境来限制不受信任代码可能造成的危害。

技巧：给模型提供特定功能的访问权限
Chat Completions API允许在请求中传递函数描述列表。这使模型能够根据提供的架构生成函数参数。API以JSON格式返回生成的函数参数，并可用于执行函数调用。然后，可以将函数调用提供的输出反馈给模型，用于下一个请求，以闭合循环。这是使用OpenAI模型调用外部函数的推荐方式。要了解更多，请参阅我们的文本生成指南中的函数调用部分以及OpenAI Cookbook中的更多函数调用示例。

策略：系统性地测试变更
有时很难判断某一变更（例如，新指令或新设计）是使你的系统变得更好还是更糟。看几个例子可能会暗示哪个更好，但在小样本量的情况下很难区分真正的改进还是偶然的运气。也许这种变化有助于某些输入的性能，但对其他输入的性能有害。

评估程序（或“评估”）有助于优化系统设计。好的评估应该：

代表真实世界的使用（或至少是多样的）

包含许多测试用例，以获得更大的统计能力（参见下表的指导）

易于自动化或重复

评估输出可以由计算机、人类或两者结合来完成。计算机可以使用客观标准（例如，有单一正确答案的问题）以及一些主观或模糊标准来自动化评估，其中模型输出由其他模型查询进行评估。OpenAI Evals是一个开源软件框架，提供创建自动化评估的工具。

基于模型的评估在存在一系列可能被视为同等高质量的可能输出时非常有用（例如，对于有长答案的问题）。可以实际评估的边界和需要人类评估的边界是模糊的，并且随着模型变得更加强大而不断变化。我们鼓励进行实验，以了解基于模型的评估对您的用例有多有效。

技巧：根据标准答案评估模型输出
假设已知正确答案应该涉及一组特定的已知事实。然后，我们可以使用模型查询来计算答案中包含了多少所需事实。

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wendaologin.mapper.WendaoWxQrcodeRecordMapper">
    
    <resultMap type="WendaoWxQrcodeRecord" id="WendaoWxQrcodeRecordResult">
        <result property="id"    column="id"    />
        <result property="scene"    column="scene"    />
        <result property="pagePath"    column="page_path"    />
        <result property="businessType"    column="business_type"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWendaoWxQrcodeRecordVo">
        select id, scene, page_path, business_type, expire_time, status, create_time, update_time from wendao_wx_qrcode_record
    </sql>

    <select id="selectWendaoWxQrcodeRecordList" parameterType="WendaoWxQrcodeRecord" resultMap="WendaoWxQrcodeRecordResult">
        <include refid="selectWendaoWxQrcodeRecordVo"/>
        <where>  
            <if test="scene != null  and scene != ''"> and scene = #{scene}</if>
            <if test="pagePath != null  and pagePath != ''"> and page_path = #{pagePath}</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectWendaoWxQrcodeRecordById" parameterType="Long" resultMap="WendaoWxQrcodeRecordResult">
        <include refid="selectWendaoWxQrcodeRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertWendaoWxQrcodeRecord" parameterType="WendaoWxQrcodeRecord" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_wx_qrcode_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scene != null and scene != ''">scene,</if>
            <if test="pagePath != null">page_path,</if>
            <if test="businessType != null">business_type,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scene != null and scene != ''">#{scene},</if>
            <if test="pagePath != null">#{pagePath},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWendaoWxQrcodeRecord" parameterType="WendaoWxQrcodeRecord">
        update wendao_wx_qrcode_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="scene != null and scene != ''">scene = #{scene},</if>
            <if test="pagePath != null">page_path = #{pagePath},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWendaoWxQrcodeRecordById" parameterType="Long">
        delete from wendao_wx_qrcode_record where id = #{id}
    </delete>

    <delete id="deleteWendaoWxQrcodeRecordByIds" parameterType="String">
        delete from wendao_wx_qrcode_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysTenantIgnoreTable;

/**
 * 租户忽略表配置 数据层
 * 
 * <AUTHOR>
 */
public interface SysTenantIgnoreTableMapper
{
    /**
     * 查询租户忽略表配置信息
     * 
     * @param id 主键ID
     * @return 租户忽略表配置信息
     */
    public SysTenantIgnoreTable selectTenantIgnoreTableById(Long id);

    /**
     * 查询租户忽略表配置列表
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 租户忽略表配置集合
     */
    public List<SysTenantIgnoreTable> selectTenantIgnoreTableList(SysTenantIgnoreTable sysTenantIgnoreTable);

    /**
     * 查询所有有效的忽略表名
     * 
     * @return 表名集合
     */
    public List<String> selectActiveTableNames();

    /**
     * 根据表名查询租户忽略表配置信息
     * 
     * @param tableName 表名
     * @return 租户忽略表配置信息
     */
    public SysTenantIgnoreTable checkTableNameUnique(String tableName);

    /**
     * 新增租户忽略表配置
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 结果
     */
    public int insertTenantIgnoreTable(SysTenantIgnoreTable sysTenantIgnoreTable);

    /**
     * 修改租户忽略表配置
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 结果
     */
    public int updateTenantIgnoreTable(SysTenantIgnoreTable sysTenantIgnoreTable);

    /**
     * 删除租户忽略表配置
     * 
     * @param id 主键ID
     * @return 结果
     */
    public int deleteTenantIgnoreTableById(Long id);

    /**
     * 批量删除租户忽略表配置
     * 
     * @param ids 需要删除的主键ID
     * @return 结果
     */
    public int deleteTenantIgnoreTableByIds(Long[] ids);
} 
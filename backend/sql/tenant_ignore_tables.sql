-- 租户忽略表配置
CREATE TABLE `sys_tenant_ignore_tables` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_name` varchar(64) NOT NULL COMMENT '表名',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_name` (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户忽略表配置';

-- 初始化数据
INSERT INTO `sys_tenant_ignore_tables` (`table_name`, `description`, `status`, `create_by`, `create_time`) VALUES
('sys_tenant', '租户表', '0', 'admin', NOW()),
('gen_table', '代码生成表', '0', 'admin', NOW()),
('gen_table_column', '代码生成字段表', '0', 'admin', NOW()),
('information_schema.tables', '系统表信息', '0', 'admin', NOW()),
('information_schema.columns', '系统列信息', '0', 'admin', NOW()),
('wendao_shop_info', '商店信息表', '0', 'admin', NOW()),
('wendao_sms_log', '短信日志表', '0', 'admin', NOW()),
('wendao_sys_login_log', '系统登录日志表', '0', 'admin', NOW()),
('wendao_sys_user', '系统用户表', '0', 'admin', NOW()),
('wendao_wx_qrcode_record', '微信二维码记录表', '0', 'admin', NOW()),
('sys_trial_permission', '试用权限表', '0', 'admin', NOW()),
('sys_tenant_ignore_tables', '租户忽略表配置表', '0', 'admin', NOW()); 
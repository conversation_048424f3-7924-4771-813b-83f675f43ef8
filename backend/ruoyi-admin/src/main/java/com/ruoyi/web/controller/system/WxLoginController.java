package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.WxLogin;
import com.ruoyi.system.service.IWxLoginService;

/**
 * 微信扫码登录管理
 */
@RestController
@RequestMapping("/system/wxLogin")
public class WxLoginController extends BaseController {
    @Autowired
    private IWxLoginService wxLoginService;

    /**
     * 获取微信登录信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(WxLogin wxLogin) {
        startPage();
        List<WxLogin> list = wxLoginService.selectWxLoginList(wxLogin);
        return getDataTable(list);
    }

    /**
     * 导出微信登录信息列表
     */
    @Log(title = "微信登录管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WxLogin wxLogin) {
        List<WxLogin> list = wxLoginService.selectWxLoginList(wxLogin);
        ExcelUtil<WxLogin> util = new ExcelUtil<WxLogin>(WxLogin.class);
        util.exportExcel(response, list, "微信登录数据");
    }

    /**
     * 根据微信登录信息编号获取详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wxLoginService.selectWxLoginById(id));
    }

    /**
     * 根据UUID获取详细信息
     */
    @GetMapping(value = "/uuid/{uuid}")
    public AjaxResult getInfoByUuid(@PathVariable("uuid") String uuid) {
        return success(wxLoginService.selectWxLoginByUuid(uuid));
    }

    /**
     * 根据创作者ID获取详细信息
     */
    @GetMapping(value = "/creatorId/{creatorId}")
    public AjaxResult getInfoByCreatorId(@PathVariable("creatorId") String creatorId) {
        return success(wxLoginService.selectWxLoginByCreatorId(creatorId));
    }

    /**
     * 根据OpenID获取详细信息
     */
    @GetMapping(value = "/openid/{openid}")
    public AjaxResult getInfoByOpenid(@PathVariable("openid") String openid) {
        return success(wxLoginService.selectWxLoginByOpenid(openid));
    }

    /**
     * 根据UnionId获取详细信息
     */
    @GetMapping(value = "/unionid/{unionid}")
    public AjaxResult getInfoByUnionid(@PathVariable("unionid") String unionid) {
        return success(wxLoginService.selectWxLoginByUnionid(unionid));
    }

    /**
     * 新增微信登录信息
     */
    @Log(title = "微信登录管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody WxLogin wxLogin) {
        // 校验UUID唯一性
        if (!wxLoginService.checkUuidUnique(wxLogin.getUuid())) {
            return error("新增微信登录记录失败，UUID已存在");
        }
        return toAjax(wxLoginService.insertWxLogin(wxLogin));
    }

    /**
     * 修改微信登录信息
     */
    @Log(title = "微信登录管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody WxLogin wxLogin) {
        return toAjax(wxLoginService.updateWxLogin(wxLogin));
    }

    /**
     * 删除微信登录信息
     */
    @Log(title = "微信登录管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wxLoginService.deleteWxLoginByIds(ids));
    }

    /**
     * 生成微信登录二维码UUID
     */
    @PostMapping("/generateUuid")
    public AjaxResult generateLoginUuid() {
        String uuid = wxLoginService.generateLoginUuid();
        return success("生成成功", uuid);
    }

    /**
     * 确认微信登录
     */
    @PostMapping("/confirm")
    public AjaxResult confirmWxLogin(@RequestParam String uuid, 
                                   @RequestParam String creatorId, 
                                   @RequestParam String openid,
                                   @RequestParam(required = false) String wxMaAppid) {
        boolean result = wxLoginService.confirmWxLogin(uuid, creatorId, openid, wxMaAppid);
        if (result) {
            return success("确认登录成功");
        }
        return error("确认登录失败，UUID无效或已过期");
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/checkStatus/{uuid}")
    public AjaxResult checkLoginStatus(@PathVariable("uuid") String uuid) {
        WxLogin wxLogin = wxLoginService.checkLoginStatus(uuid);
        if (wxLogin != null) {
            return success("登录成功", wxLogin);
        }
        return error("登录未完成或已过期");
    }

    /**
     * 清理过期的登录记录
     */
    @Log(title = "微信登录管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/cleanExpired")
    public AjaxResult cleanExpiredRecords() {
        int count = wxLoginService.deleteExpiredWxLogin();
        return success("清理完成，共清理" + count + "条过期记录");
    }

    /**
     * 校验UUID唯一性
     */
    @GetMapping("/checkUuidUnique")
    public AjaxResult checkUuidUnique(String uuid) {
        return success(wxLoginService.checkUuidUnique(uuid));
    }

    /**
     * 根据创作者ID和微信小程序APPID查询最新的登录记录
     */
    @GetMapping("/latest")
    public AjaxResult getLatestLogin(@RequestParam String creatorId, 
                                   @RequestParam(required = false) String wxMaAppid) {
        WxLogin wxLogin = wxLoginService.selectLatestWxLoginByCreatorIdAndAppid(creatorId, wxMaAppid);
        return success(wxLogin);
    }
}

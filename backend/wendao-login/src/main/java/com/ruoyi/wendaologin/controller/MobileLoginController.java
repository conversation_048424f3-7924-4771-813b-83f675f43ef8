package com.ruoyi.wendaologin.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.wendaologin.annotation.Idempotent;
import com.ruoyi.wendaologin.config.TencentCaptchaConfig;
import com.ruoyi.wendaologin.dto.*;
import com.ruoyi.wendaologin.mapper.WendaoSmsLogMapper;
import com.ruoyi.wendaologin.service.MobileLoginService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.validation.Valid;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Anonymous
@RestController
@RequestMapping("/mobile/login")
public class MobileLoginController {

    @Autowired
    private MobileLoginService mobileLoginService;

    @Autowired
    private WendaoSmsLogMapper wendaoSmsLogMapper;

    @Autowired
    private TencentCaptchaConfig captchaConfig;

    /**
     * 初始化加密滑动验证码数据
     *
     * @return
     */
    @GetMapping("/encryptAppid")
    public AjaxResult encryptAppid() {
        int remainder = 32 % captchaConfig.getAppSecretKey().length();
        String key = captchaConfig.getAppSecretKey() + captchaConfig.getAppSecretKey().substring(0, remainder);
        long curTime = new Date().getTime() / 1000;
        long expireTime = 86400L;
        String plaintext = captchaConfig.getCaptchaAppId() + "&" + curTime + "&" + expireTime;
        String iv = RandomStringUtils.randomNumeric(16);
        String ciphertext = null;
        try {
            ciphertext = encrypt(plaintext, key, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String captchaAppidEncrypted = ciphertext;
        return AjaxResult.success("success", captchaAppidEncrypted);
    }

    /**
     * 发送短信验证码没有加载滑块验证
     */
    @PostMapping("/send/codeNoCaptcha")
    @Idempotent(expire = 60, message = "请勿频繁发送验证码")
    public AjaxResult codeNoCaptcha(@Valid @RequestBody SendSmsNoCaptchaDTO smsDTO) {
        return AjaxResult.success(mobileLoginService.sendSmsCodeNoCaptcha(smsDTO));
    }

    /**
     * 手机号密码登录
     */
    @PostMapping("/password")
    public AjaxResult loginByPassword(@Valid @RequestBody MobileLoginDTO loginDTO) {
        return AjaxResult.success(mobileLoginService.loginByPassword(loginDTO));
    }

    /**
     * 手机号验证码登录
     */
    @PostMapping("/code")
    public AjaxResult loginByCode(@Valid @RequestBody MobileCodeLoginDTO loginDTO) {
        return AjaxResult.success(mobileLoginService.loginByCode(loginDTO));
    }

    /**
     * 发送短信验证码
     */
    @PostMapping("/send/code")
    @Idempotent(expire = 60, message = "请勿频繁发送验证码")
    public AjaxResult sendSmsCode(@Valid @RequestBody SendSmsDTO smsDTO) {
        return AjaxResult.success(mobileLoginService.sendSmsCode(smsDTO));
    }

    /**
     * 接收短信状态报告
     */
    @PostMapping("/sms/report")
    public String receiveReport(@RequestBody List<SmsReportDTO> reports) {
        try {
            for (SmsReportDTO report : reports) {
                log.info("收到短信状态报告：{}", report);
                // 更新短信记录状态
                int status = "SUCCESS".equals(report.getReportStatus()) ? 1 : 0;
                String errorMsg = status == 0 ?
                        String.format("发送失败：%s，%s", report.getErrorMsg(), report.getErrorDetail()) : null;
                wendaoSmsLogMapper.updateStatusBySmsId(report.getSid(), status, errorMsg);
            }
            return "success";
        } catch (Exception e) {
            log.error("处理短信状态报告失败", e);
            return "fail";
        }
    }

    /**
     * 接收阿里云短信发送状态报告
     * 文档：https://help.aliyun.com/zh/sms/developer-reference/smsreport-2
     */
    @PostMapping("/sms/aliyun/report")
    @ResponseBody
    public AjaxResult smsReport(@RequestBody List<AliyunSmsReportDTO> reports) {
        try {
            for (AliyunSmsReportDTO report : reports) {
                // 记录短信发送状态
                log.info("收到短信发送状态报告：{}", report);

                // 更新数据库中的短信发送状态
                int status = report.getSuccess() ? 1 : 0;
                String errorMessage = report.getSuccess() ? null :
                        String.format("%s: %s", report.getErrCode(), report.getErrMsg());
                wendaoSmsLogMapper.updateStatusBySmsId(report.getBizId(), status, errorMessage);
            }
            // 返回成功响应
            return new AjaxResult(0, "接收成功");
        } catch (Exception e) {
            log.error("处理短信发送状态报告异常", e);
            return new AjaxResult(-1, "接收失败");
        }
    }

    private String encrypt(String plaintext, String key, String iv) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
        byte[] ivAndEncrypted = new byte[iv.length() + encrypted.length];
        System.arraycopy(iv.getBytes(StandardCharsets.UTF_8), 0, ivAndEncrypted, 0, iv.length());
        System.arraycopy(encrypted, 0, ivAndEncrypted, iv.length(), encrypted.length);
        return Base64.getEncoder().encodeToString(ivAndEncrypted);
    }
} 
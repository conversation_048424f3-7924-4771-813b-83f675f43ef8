# 试用系统实现完整说明

## 概述

本文档说明了为 RuoYi-Vue v3.8.9 多租户系统实现的试用功能。该系统实现了试用期间用户拥有完整权限，试用过期后保留读权限但限制写权限的需求。

## 已创建的文件清单

### 1. 数据库相关文件

#### 1.1 数据库扩展脚本
- **文件路径**: `sql/trial_extension.sql`
- **功能**: 扩展租户表，创建试用权限配置表，插入默认权限配置

#### 1.2 实体类
- **文件路径**: `ruoyi-system/src/main/java/com/ruoyi/system/domain/SysTrialPermission.java`
- **功能**: 试用权限配置实体类

- **文件路径**: `ruoyi-system/src/main/java/com/ruoyi/system/domain/SysTenant.java`
- **功能**: 租户实体类（已扩展试用相关字段）

### 2. 数据访问层

#### 2.1 Mapper接口
- **文件路径**: `ruoyi-system/src/main/java/com/ruoyi/system/mapper/SysTrialPermissionMapper.java`
- **功能**: 试用权限配置数据访问接口

#### 2.2 Mapper XML
- **文件路径**: `ruoyi-system/src/main/resources/mapper/system/SysTrialPermissionMapper.xml`
- **功能**: 试用权限配置SQL映射文件

- **文件路径**: `ruoyi-system/src/main/resources/mapper/system/SysTenantMapper.xml`
- **功能**: 租户SQL映射文件（已更新支持试用字段）

### 3. 业务逻辑层

#### 3.1 服务接口
- **文件路径**: `ruoyi-system/src/main/java/com/ruoyi/system/service/ISysTrialPermissionService.java`
- **功能**: 试用权限配置服务接口

#### 3.2 服务实现
- **文件路径**: `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/SysTrialPermissionServiceImpl.java`
- **功能**: 试用权限配置服务实现

### 4. 权限控制核心

#### 4.1 权限验证服务
- **文件路径**: `ruoyi-framework/src/main/java/com/ruoyi/framework/web/service/TrialPermissionService.java`
- **功能**: 核心权限验证逻辑，支持缓存和试用状态检查

#### 4.2 注解定义
- **文件路径**: `ruoyi-common/src/main/java/com/ruoyi/common/annotation/TrialPermission.java`
- **功能**: 试用权限验证注解

#### 4.3 AOP切面
- **文件路径**: `ruoyi-framework/src/main/java/com/ruoyi/framework/aspectj/TrialPermissionAspect.java`
- **功能**: 试用权限验证切面处理

### 5. 控制器层

#### 5.1 试用管理控制器
- **文件路径**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysTrialController.java`
- **功能**: 试用管理API接口

#### 5.2 示例控制器
- **文件路径**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/SysUserTrialController.java`
- **功能**: 用户管理试用权限使用示例

### 6. 定时任务

#### 6.1 试用状态检查任务
- **文件路径**: `ruoyi-framework/src/main/java/com/ruoyi/framework/task/TrialStatusTask.java`
- **功能**: 自动检查试用过期状态和发送提醒

### 7. 单元测试

#### 7.1 Service层单元测试
- **文件路径**: `ruoyi-system/src/test/java/com/ruoyi/system/service/impl/SysTrialPermissionServiceImplTest.java`
- **功能**: 试用权限配置服务测试

- **文件路径**: `ruoyi-framework/src/test/java/com/ruoyi/framework/web/service/TrialPermissionServiceTest.java`
- **功能**: 核心权限验证服务测试

- **文件路径**: `ruoyi-framework/src/test/java/com/ruoyi/framework/task/TrialStatusTaskTest.java`
- **功能**: 定时任务测试

#### 7.2 Controller层单元测试
- **文件路径**: `ruoyi-admin/src/test/java/com/ruoyi/web/controller/system/SysTrialControllerTest.java`
- **功能**: 试用管理控制器测试

### 8. 文档

#### 8.1 使用指南
- **文件路径**: `doc/trial-system-guide.md`
- **功能**: 详细的使用指南和最佳实践

#### 8.2 单元测试说明
- **文件路径**: `doc/trial-system-unit-tests.md`
- **功能**: 单元测试详细说明和运行指南

## 部署步骤

### 1. 执行数据库脚本
```sql
-- 执行试用功能扩展脚本
source sql/trial_extension.sql;
```

### 2. 配置定时任务
在系统定时任务管理中添加以下任务：

```
# 每小时检查一次试用过期状态
任务名称: 检查试用过期状态
Cron表达式: 0 0 * * * ?
调用方法: trialStatusTask.checkTrialExpiredStatus

# 每天上午9点检查试用到期提醒
任务名称: 试用到期提醒
Cron表达式: 0 0 9 * * ?
调用方法: trialStatusTask.trialExpirationReminder
```

### 3. 运行单元测试
```bash
# 运行所有试用系统相关测试
mvn test -Dtest="*Trial*Test"

# 生成测试覆盖率报告
mvn clean test jacoco:report
```

### 4. 重启应用
重启应用以加载新的代码和配置。

## 使用示例

### 1. 在控制器中使用试用权限

```java
@RestController
@RequestMapping("/system/example")
public class ExampleController {
    
    // 读权限：试用期和过期后都可用
    @PreAuthorize("@trialPermission.hasTrialPermi('system:example:list')")
    @TrialPermission(value = "system:example:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/list")
    public TableDataInfo list() {
        // 查询逻辑
    }
    
    // 写权限：试用期可用，过期后不可用
    @PreAuthorize("@trialPermission.hasTrialPermi('system:example:add')")
    @TrialPermission(value = "system:example:add", type = 2, trialAvailable = true, expiredAvailable = false)
    @PostMapping
    public AjaxResult add(@RequestBody Example example) {
        // 新增逻辑
    }
}
```

### 2. 编程式权限检查

```java
@Autowired
private TrialPermissionService trialPermissionService;

public AjaxResult someMethod() {
    if (!trialPermissionService.hasTrialPermi("system:example:edit")) {
        return error("试用期已过期，写操作权限已被限制");
    }
    // 业务逻辑
}
```

### 3. 试用管理操作

```java
// 开始试用（7天）
trialPermissionService.startTrial("tenant001", 7);

// 租户转正
trialPermissionService.convertTenant("tenant001");

// 清除缓存
trialPermissionService.clearTenantTrialCache("tenant001");
```

## 权限配置

### 1. 默认权限配置

系统已预置常用权限配置：

**读权限（试用期和过期后都可用）：**
- system:user:list - 用户查询
- system:role:list - 角色查询
- system:menu:list - 菜单查询
- system:dept:list - 部门查询

**写权限（试用期可用，过期后不可用）：**
- system:user:add - 用户新增
- system:user:edit - 用户修改
- system:user:remove - 用户删除

### 2. 添加自定义权限配置

通过管理界面或API添加：

```java
SysTrialPermission permission = new SysTrialPermission();
permission.setPermissionCode("custom:feature:add");
permission.setPermissionName("自定义功能新增");
permission.setPermissionType(2); // 写权限
permission.setTrialAvailable(1); // 试用期可用
permission.setExpiredAvailable(0); // 过期后不可用
```

## 技术特性

### 1. 缓存机制
- 租户试用状态缓存：5分钟
- 权限配置缓存：30分钟
- 支持手动清除缓存

### 2. 权限分类
- **读权限（type=1）**：查询、导出等操作
- **写权限（type=2）**：新增、修改、删除等操作

### 3. 试用状态
- **0 - 未试用**：租户尚未开始试用
- **1 - 试用中**：租户正在试用期内
- **2 - 试用过期**：试用期已结束，写权限被限制
- **3 - 已转正**：租户已升级为正式用户

### 4. 自动化功能
- 自动检查试用过期状态
- 自动发送试用到期提醒
- 自动更新租户状态

### 5. 质量保障
- 完整的单元测试覆盖
- Service层测试覆盖率 ≥ 90%
- Controller层测试覆盖率 ≥ 85%
- 核心业务逻辑测试覆盖率 ≥ 95%

## 注意事项

1. **数据库字段**：确保已执行数据库扩展脚本
2. **缓存配置**：确保Redis正常运行
3. **定时任务**：确保定时任务正常执行
4. **权限配置**：根据业务需求配置相应的权限策略
5. **性能监控**：监控权限检查的性能影响
6. **测试覆盖**：确保新增功能有对应的单元测试

## 故障排查

### 1. 权限检查失败
- 检查租户试用状态
- 检查权限配置是否存在
- 检查缓存是否正常
- 运行相关单元测试验证逻辑

### 2. 试用状态未更新
- 检查定时任务是否运行
- 手动执行状态检查
- 清除相关缓存
- 检查定时任务单元测试

### 3. 性能问题
- 检查Redis缓存
- 优化权限检查逻辑
- 调整缓存过期时间
- 运行性能测试

### 4. 单元测试失败
- 检查测试环境配置
- 验证Mock对象设置
- 检查测试数据准备
- 查看测试覆盖率报告

## 总结

该试用系统完美解决了多租户系统的试用需求：

1. **试用期间**：用户拥有完整的读写权限
2. **试用过期后**：保留读权限，限制写权限
3. **灵活配置**：支持针对不同权限的个性化配置
4. **自动管理**：通过定时任务自动检查和更新试用状态
5. **性能优化**：使用缓存机制提高权限检查效率
6. **质量保障**：完整的单元测试确保系统稳定性

该方案在保持现有RBAC模型不变的基础上，通过扩展实现了试用期权限控制，满足了多租户系统的试用需求，并通过完整的测试体系确保了系统的可靠性和可维护性。 
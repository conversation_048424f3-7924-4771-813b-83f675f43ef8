package com.ruoyi.wendaologin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 店铺信息对象 wendao_shop_info
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class WendaoShopInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 店铺ID */
    private String shopId;

    /** 店铺名称 */
    @Excel(name = "店铺名称")
    private String shopName;

    /** 店铺logo */
    @Excel(name = "店铺logo")
    private String shopLogo;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String appId;

    /** 版本类型:0-试用版,1-标准版,2-专业版,3-旗舰版 */
    @Excel(name = "版本类型:0-试用版,1-标准版,2-专业版,3-旗舰版")
    private Integer versionType;

    /** 是否启用收藏:0-否,1-是 */
    @Excel(name = "是否启用收藏:0-否,1-是")
    private Integer useCollection;

    /** 店铺状态:0-正常,1-已关闭 */
    @Excel(name = "店铺状态:0-正常,1-已关闭")
    private Integer status;

    /** 是否已封停:0-否,1-是 */
    @Excel(name = "是否已封停:0-否,1-是")
    private Integer isSealed;

    /** 是否处于封停状态:0-否,1-是 */
    @Excel(name = "是否处于封停状态:0-否,1-是")
    private Integer isSeal;

    /** 是否等待封停:0-否,1-是 */
    @Excel(name = "是否等待封停:0-否,1-是")
    private Integer isWaitSeal;

    /** 封停申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "封停申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sealApplyTime;

    /** 预计封停时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预计封停时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date readySealAt;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireTime;

    /** 是否已过期:0-否,1-是 */
    @Excel(name = "是否已过期:0-否,1-是")
    private Integer hasExpired;

    /** 是否有激活订单:0-否,1-是 */
    @Excel(name = "是否有激活订单:0-否,1-是")
    private Integer hasActivateOrder;

    /** 是否最后登录:0-否,1-是 */
    @Excel(name = "是否最后登录:0-否,1-是")
    private Integer lastLogin;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String bUserId;

    public void setShopId(String shopId) 
    {
        this.shopId = shopId;
    }

    public String getShopId() 
    {
        return shopId;
    }
    public void setShopName(String shopName) 
    {
        this.shopName = shopName;
    }

    public String getShopName() 
    {
        return shopName;
    }
    public void setShopLogo(String shopLogo) 
    {
        this.shopLogo = shopLogo;
    }

    public String getShopLogo() 
    {
        return shopLogo;
    }
    public void setAppId(String appId) 
    {
        this.appId = appId;
    }

    public String getAppId() 
    {
        return appId;
    }
    public void setVersionType(Integer versionType) 
    {
        this.versionType = versionType;
    }

    public Integer getVersionType() 
    {
        return versionType;
    }
    public void setUseCollection(Integer useCollection) 
    {
        this.useCollection = useCollection;
    }

    public Integer getUseCollection() 
    {
        return useCollection;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setIsSealed(Integer isSealed) 
    {
        this.isSealed = isSealed;
    }

    public Integer getIsSealed() 
    {
        return isSealed;
    }
    public void setIsSeal(Integer isSeal) 
    {
        this.isSeal = isSeal;
    }

    public Integer getIsSeal() 
    {
        return isSeal;
    }
    public void setIsWaitSeal(Integer isWaitSeal) 
    {
        this.isWaitSeal = isWaitSeal;
    }

    public Integer getIsWaitSeal() 
    {
        return isWaitSeal;
    }
    public void setSealApplyTime(Date sealApplyTime) 
    {
        this.sealApplyTime = sealApplyTime;
    }

    public Date getSealApplyTime() 
    {
        return sealApplyTime;
    }
    public void setReadySealAt(Date readySealAt) 
    {
        this.readySealAt = readySealAt;
    }

    public Date getReadySealAt() 
    {
        return readySealAt;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setHasExpired(Integer hasExpired) 
    {
        this.hasExpired = hasExpired;
    }

    public Integer getHasExpired() 
    {
        return hasExpired;
    }
    public void setHasActivateOrder(Integer hasActivateOrder) 
    {
        this.hasActivateOrder = hasActivateOrder;
    }

    public Integer getHasActivateOrder() 
    {
        return hasActivateOrder;
    }
    public void setLastLogin(Integer lastLogin) 
    {
        this.lastLogin = lastLogin;
    }

    public Integer getLastLogin() 
    {
        return lastLogin;
    }
    public void setbUserId(String bUserId) 
    {
        this.bUserId = bUserId;
    }

    public String getbUserId() 
    {
        return bUserId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("shopId", getShopId())
            .append("shopName", getShopName())
            .append("shopLogo", getShopLogo())
            .append("appId", getAppId())
            .append("versionType", getVersionType())
            .append("useCollection", getUseCollection())
            .append("status", getStatus())
            .append("isSealed", getIsSealed())
            .append("isSeal", getIsSeal())
            .append("isWaitSeal", getIsWaitSeal())
            .append("sealApplyTime", getSealApplyTime())
            .append("readySealAt", getReadySealAt())
            .append("expireTime", getExpireTime())
            .append("hasExpired", getHasExpired())
            .append("hasActivateOrder", getHasActivateOrder())
            .append("lastLogin", getLastLogin())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("bUserId", getbUserId())
            .toString();
    }
}

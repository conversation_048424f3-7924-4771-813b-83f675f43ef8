package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysTrialPermission;

/**
 * 试用权限配置Service接口
 * 
 * <AUTHOR>
 */
public interface ISysTrialPermissionService 
{
    /**
     * 查询试用权限配置
     * 
     * @param id 试用权限配置主键
     * @return 试用权限配置
     */
    public SysTrialPermission selectSysTrialPermissionById(Long id);

    /**
     * 根据权限编码查询试用权限配置
     * 
     * @param permissionCode 权限编码
     * @return 试用权限配置
     */
    public SysTrialPermission selectByPermissionCode(String permissionCode);

    /**
     * 查询试用权限配置列表
     * 
     * @param sysTrialPermission 试用权限配置
     * @return 试用权限配置集合
     */
    public List<SysTrialPermission> selectSysTrialPermissionList(SysTrialPermission sysTrialPermission);

    /**
     * 新增试用权限配置
     * 
     * @param sysTrialPermission 试用权限配置
     * @return 结果
     */
    public int insertSysTrialPermission(SysTrialPermission sysTrialPermission);

    /**
     * 修改试用权限配置
     * 
     * @param sysTrialPermission 试用权限配置
     * @return 结果
     */
    public int updateSysTrialPermission(SysTrialPermission sysTrialPermission);

    /**
     * 批量删除试用权限配置
     * 
     * @param ids 需要删除的试用权限配置主键集合
     * @return 结果
     */
    public int deleteSysTrialPermissionByIds(Long[] ids);

    /**
     * 删除试用权限配置信息
     * 
     * @param id 试用权限配置主键
     * @return 结果
     */
    public int deleteSysTrialPermissionById(Long id);
} 
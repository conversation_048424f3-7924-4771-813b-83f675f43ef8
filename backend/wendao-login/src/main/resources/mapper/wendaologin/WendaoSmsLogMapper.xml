<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wendaologin.mapper.WendaoSmsLogMapper">
    
    <resultMap type="WendaoSmsLog" id="WendaoSmsLogResult">
        <result property="id"    column="id"    />
        <result property="uuid"    column="uuid"    />
        <result property="mobile"    column="mobile"    />
        <result property="code"    column="code"    />
        <result property="type"    column="type"    />
        <result property="ip"    column="ip"    />
        <result property="smsId"    column="sms_id"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="status"    column="status"    />
        <result property="sendTime"    column="send_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWendaoSmsLogVo">
        select id, uuid, mobile, code, type, ip, sms_id, error_msg, `status`, send_time, create_time, update_time from wendao_sms_log
    </sql>

    <select id="selectWendaoSmsLogList" parameterType="WendaoSmsLog" resultMap="WendaoSmsLogResult">
        <include refid="selectWendaoSmsLogVo"/>
        <where>  
            <if test="uuid != null  and uuid != ''"> and uuid = #{uuid}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="smsId != null  and smsId != ''"> and sms_id = #{smsId}</if>
            <if test="errorMsg != null  and errorMsg != ''"> and error_msg = #{errorMsg}</if>
            <if test="status != null "> and `status` = #{status}</if>
            <if test="sendTime != null "> and send_time = #{sendTime}</if>
        </where>
    </select>
    
    <select id="selectWendaoSmsLogById" parameterType="Long" resultMap="WendaoSmsLogResult">
        <include refid="selectWendaoSmsLogVo"/>
        where id = #{id}
    </select>
    <select id="selectByUuid" resultMap="WendaoSmsLogResult">
        <include refid="selectWendaoSmsLogVo"/>
        where uuid = #{uuid} limit 1
    </select>

    <insert id="insertWendaoSmsLog" parameterType="WendaoSmsLog" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_sms_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uuid != null and uuid != ''">uuid,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="type != null">type,</if>
            <if test="ip != null">ip,</if>
            <if test="smsId != null">sms_id,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="status != null">`status`,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uuid != null and uuid != ''">#{uuid},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="type != null">#{type},</if>
            <if test="ip != null">#{ip},</if>
            <if test="smsId != null">#{smsId},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="status != null">#{status},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWendaoSmsLog" parameterType="WendaoSmsLog">
        update wendao_sms_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="uuid != null and uuid != ''">uuid = #{uuid},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="type != null">type = #{type},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="smsId != null">sms_id = #{smsId},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateStatusBySmsId">
        UPDATE wendao_sms_log SET `status` = #{status}, error_msg = #{errorMsg}, update_time = now() WHERE sms_id = #{smsId}
    </update>

    <delete id="deleteWendaoSmsLogById" parameterType="Long">
        delete from wendao_sms_log where id = #{id}
    </delete>

    <delete id="deleteWendaoSmsLogByIds" parameterType="String">
        delete from wendao_sms_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
package com.ruoyi.web.controller.monitor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.TokenService;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.TrialPermission;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysUserOnline;
import com.ruoyi.system.service.ISysUserOnlineService;

import javax.servlet.http.HttpServletRequest;

/**
 * 在线用户监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/online")
public class SysUserOnlineController extends BaseController {
    @Autowired
    private ISysUserOnlineService userOnlineService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private TokenService tokenService;

    @PreAuthorize("@ss.hasPermi('monitor:online:list')")
    @TrialPermission(value = "monitor:online:list", type = 1, trialAvailable = true, expiredAvailable = true)
    @GetMapping("/list")
    public TableDataInfo list(String ipaddr, String userName, HttpServletRequest request) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //判断当前用户是否为管理员
        SysUser user1 = loginUser.getUser();
        List<SysRole> roles1 = user1.getRoles();
        boolean mySelfAdmin = false;
        if (roles1 != null) {
            for (SysRole role : roles1) {
                if (role.getRoleId() == 1L) {
                    mySelfAdmin = true;
                    break;
                }
            }
        }
        String batchKeyPrefix = CacheConstants.LOGIN_TOKEN_KEY + loginUser.getUser().getShopId() + ":";
        Collection<String> keys = redisCache.keys(batchKeyPrefix + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<>();
        for (String key : keys) {
            String tokenId = key.replace(batchKeyPrefix, "");
            boolean isMySelf = tokenService.itsMyself(request, loginUser.getUser().getShopId(), tokenId);
            LoginUser user = redisCache.getCacheObject(key);
            List<SysRole> roles = user.getUser().getRoles();
            boolean isAdmin = false;
            if (roles != null) {
                for (SysRole role : roles) {
                    if (role.getRoleId() == 1L && !mySelfAdmin) {
                        isAdmin = true;
                        break;
                    }
                }
            }
            if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName)) {
                SysUserOnline sysUserOnline = userOnlineService.selectOnlineByInfo(ipaddr, userName, user);
                sysUserOnline.setMySelf(isMySelf);
                sysUserOnline.setAdmin(isAdmin);
                userOnlineList.add(sysUserOnline);
            } else if (StringUtils.isNotEmpty(ipaddr)) {
                SysUserOnline sysUserOnline = userOnlineService.selectOnlineByIpaddr(ipaddr, user);
                sysUserOnline.setMySelf(isMySelf);
                sysUserOnline.setAdmin(isAdmin);
                userOnlineList.add(sysUserOnline);
            } else if (StringUtils.isNotEmpty(userName) && StringUtils.isNotNull(user.getUser())) {
                SysUserOnline sysUserOnline = userOnlineService.selectOnlineByUserName(userName, user);
                sysUserOnline.setMySelf(isMySelf);
                sysUserOnline.setAdmin(isAdmin);
                userOnlineList.add(sysUserOnline);
            } else {
                SysUserOnline sysUserOnline = userOnlineService.loginUserToUserOnline(user);
                sysUserOnline.setMySelf(isMySelf);
                sysUserOnline.setAdmin(isAdmin);
                userOnlineList.add(sysUserOnline);
            }
        }
        Collections.reverse(userOnlineList);
        userOnlineList.removeAll(Collections.singleton(null));
        return getDataTable(userOnlineList);
    }

    /**
     * 强退用户
     */
    @PreAuthorize("@ss.hasPermi('monitor:online:forceLogout')")
    @TrialPermission(value = "monitor:online:forceLogout", type = 2, trialAvailable = true, expiredAvailable = false)
    @Log(title = "在线用户", businessType = BusinessType.FORCE)
    @DeleteMapping("/{tokenId}")
    public AjaxResult forceLogout(@PathVariable String tokenId, HttpServletRequest request) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (tokenService.itsMyself(request, loginUser.getUser().getShopId(), tokenId)) {
            return AjaxResult.error("不能自己踢自己");
        }
        redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + loginUser.getUser().getShopId() + ":" + tokenId);
        return success();
    }
}

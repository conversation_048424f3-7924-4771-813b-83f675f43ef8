package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.WxLogin;

/**
 * 微信扫码登录管理 服务层
 */
public interface IWxLoginService {
    /**
     * 查询微信登录信息
     */
    public WxLogin selectWxLoginById(Long id);

    /**
     * 根据UUID查询微信登录信息
     */
    public WxLogin selectWxLoginByUuid(String uuid);

    /**
     * 根据创作者ID查询微信登录信息
     */
    public WxLogin selectWxLoginByCreatorId(String creatorId);

    /**
     * 根据OpenID查询微信登录信息
     */
    public WxLogin selectWxLoginByOpenid(String openid);

    /**
     * 根据unionid查询微信登录信息
     */
    public WxLogin selectWxLoginByUnionid(String unionid);

    /**
     * 查询微信登录信息列表
     */
    public List<WxLogin> selectWxLoginList(WxLogin wxLogin);

    /**
     * 新增微信登录信息
     */
    public int insertWxLogin(WxLogin wxLogin);

    /**
     * 修改微信登录信息
     */
    public int updateWxLogin(WxLogin wxLogin);

    /**
     * 删除微信登录信息
     */
    public int deleteWxLoginByIds(Long[] ids);

    /**
     * 删除过期的微信登录记录
     */
    public int deleteExpiredWxLogin();

    /**
     * 校验UUID是否唯一
     */
    public boolean checkUuidUnique(String uuid);

    /**
     * 根据创作者ID和微信小程序APPID查询最新的登录记录
     */
    public WxLogin selectLatestWxLoginByCreatorIdAndAppid(String creatorId, String wxMaAppid);

    /**
     * 生成微信登录二维码UUID
     */
    public String generateLoginUuid();

    /**
     * 确认微信登录
     */
    public boolean confirmWxLogin(String uuid, String creatorId, String openid, String wxMaAppid);

    /**
     * 检查登录状态
     */
    public WxLogin checkLoginStatus(String uuid);
}

# TenantScopeInterceptor测试结果

本文档记录了TenantScopeInterceptorTest的38个测试用例执行结果。所有测试用例均已通过,下面详细列出每个测试的SQL转换结果。

## 1. 简单查询(testSimpleSelect)
```sql
Original: SELECT * FROM sys_user
Modified: select * from sys_user where tenant_id = 1
```
结果正确,成功添加了租户条件。

## 2. 带WHERE条件的查询(testSelectWithWhere)
```sql
Original: SELECT * FROM sys_user WHERE user_name = 'admin'
Modified: select * from sys_user where user_name = 'admin' and tenant_id = 1
```
结果正确,保留原有条件并添加租户条件。

## 3. JOIN查询(testJoinUsing)
```sql
Original: SELECT * FROM sys_user JOIN sys_dept USING (dept_id)
Modified: select * from sys_user join sys_dept using (dept_id) where sys_user.tenant_id = 1 and sys_dept.tenant_id = 1
```
结果正确,为两个表都添加了带表前缀的租户条件。

## 4. 子查询(testSubquery)
```sql
Original: SELECT * FROM sys_user WHERE dept_id IN (SELECT dept_id FROM sys_dept)
Modified: select * from sys_user where dept_id in (select dept_id from sys_dept where tenant_id = 1) and tenant_id = 1
```
结果正确,主查询和子查询都添加了租户条件。

## 5. 复杂JOIN(testComplexJoin)
```sql
Original: SELECT u.*, d.dept_name, r.role_name FROM sys_user u LEFT JOIN sys_dept d ON u.dept_id = d.dept_id LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id LEFT JOIN sys_role r ON ur.role_id = r.role_id
Modified: select u.*, d.dept_name, r.role_name from sys_user u left join sys_dept d on u.dept_id = d.dept_id left join sys_user_role ur on u.user_id = ur.user_id left join sys_role r on ur.role_id = r.role_id where u.tenant_id = 1 and d.tenant_id = 1 and ur.tenant_id = 1 and r.tenant_id = 1
```
结果正确,所有相关表都添加了租户条件。

## 6. INSERT语句(testValuesClause)
```sql
Original: INSERT INTO sys_user (user_name, nick_name) VALUES ('admin', 'Administrator'), ('test', 'Test User')
Modified: insert into sys_user (user_name, nick_name, tenant_id) values ('admin', 'administrator', 1), ('test', 'test user', 1)
```
结果正确,添加了tenant_id列和对应的值。

## 7. UPDATE语句(testUpdateWithSubquery)
```sql
Original: UPDATE sys_user SET dept_id = (SELECT dept_id FROM sys_dept WHERE dept_name = 'IT')
Modified: update sys_user set dept_id = (select dept_id from sys_dept where dept_name = 'it' and tenant_id = 1) where tenant_id = 1
```
结果正确,主查询和子查询都添加了租户条件。

## 8. 忽略表(testIgnoreTable)
```sql
Original: SELECT * FROM sys_tenant
Modified: select * from sys_tenant
```
结果正确,sys_tenant表被忽略,没有添加租户条件。

## 9. 临时表(testTemporaryTable)
```sql
Original: WITH RECURSIVE temp_table AS (SELECT * FROM sys_dept WHERE parent_id IS NULL UNION ALL SELECT d.* FROM sys_dept d, temp_table t WHERE d.parent_id = t.dept_id)
Modified: with recursive temp_table as (select * from sys_dept where parent_id is null and tenant_id = 1 union all select d.* from sys_dept d, temp_table t where d.parent_id = t.dept_id and d.tenant_id = 1)
```
结果正确,递归CTE中的查询都添加了租户条件。

## 10. 函数和表达式(testFunctionAndExpression)
```sql
Original: SELECT u.*, CONCAT(u.user_name, d.dept_name) as full_name, CASE WHEN u.status = 0 THEN 'inactive' ELSE 'active' END as status FROM sys_user u LEFT JOIN sys_dept d ON u.dept_id = d.dept_id WHERE UPPER(u.user_name) LIKE '%ADMIN%'
Modified: select u.*, concat(u.user_name, d.dept_name) as full_name, case when u.status = 0 then 'inactive' else 'active' end as status from sys_user u left join sys_dept d on u.dept_id = d.dept_id where upper(u.user_name) like '%admin%' and u.tenant_id = 1 and d.tenant_id = 1
```
结果正确,保留了所有函数和表达式,同时添加了租户条件。

## 11. 分组和聚合(testGroupByWithAggregation)
```sql
Original: SELECT dept_id, COUNT(*) as count, MAX(create_time) as latest FROM sys_user GROUP BY dept_id HAVING COUNT(*) > 5
Modified: select dept_id, count(*) as count, max(create_time) as latest from sys_user where tenant_id = 1 group by dept_id having count(*) > 5
```
结果正确,保留了GROUP BY和HAVING子句,同时添加了租户条件。

## 12. 集合操作(testSetOperations)
```sql
Original: SELECT * FROM sys_user WHERE dept_id = 1 UNION SELECT * FROM sys_user WHERE user_id = 2 INTERSECT SELECT * FROM sys_user WHERE status = 0
Modified: select * from sys_user where dept_id = 1 and tenant_id = 1 union select * from sys_user where user_id = 2 and tenant_id = 1 intersect select * from sys_user where status = 0 and tenant_id = 1
```
结果正确,UNION和INTERSECT的每个子查询都添加了租户条件。

## 13. 分页查询(testPagination)
```sql
Original: SELECT * FROM sys_user ORDER BY create_time DESC LIMIT 10 OFFSET 20
Modified: select * from sys_user where tenant_id = 1 order by create_time desc limit 10 offset 20
```
结果正确,保留了ORDER BY、LIMIT和OFFSET子句,同时添加了租户条件。

## 14. 带JOIN的忽略表(testIgnoreTableWithJoin)
```sql
Original: SELECT t.*, u.user_name FROM sys_tenant t LEFT JOIN sys_user u ON t.tenant_id = u.tenant_id
Modified: select t.*, u.user_name from sys_tenant t left join sys_user u on t.tenant_id = u.tenant_id where u.tenant_id = 1
```
结果正确,只对非忽略表(sys_user)添加了租户条件。

## 15. 表别名(testTableAlias)
```sql
Original: SELECT u.* FROM sys_user u WHERE u.user_name = 'admin'
Modified: select u.* from sys_user u where u.user_name = 'admin' and u.tenant_id = 1
```
结果正确,使用了正确的表别名添加租户条件。

## 16. 批量插入(testBatchInsert)
```sql
Original: INSERT INTO sys_user (user_name, nick_name) VALUES ('test1', 'Test User1'), ('test2', 'Test User2')
Modified: insert into sys_user (user_name, nick_name, tenant_id) values ('test1', 'test user1', 1), ('test2', 'test user2', 1)
```
结果正确,为多行插入都添加了租户ID。

## 17. 复杂更新(testComplexUpdate)
```sql
Original: UPDATE sys_user SET dept_id = CASE WHEN user_name = 'admin' THEN (SELECT dept_id FROM sys_dept WHERE dept_name = 'IT') ELSE dept_id END WHERE exists (SELECT 1 FROM sys_dept d WHERE d.dept_id = sys_user.dept_id)
Modified: update sys_user set dept_id = case when user_name = 'admin' then (select dept_id from sys_dept where dept_name = 'it' and tenant_id = 1) else dept_id end where exists (select 1 from sys_dept d where d.dept_id = sys_user.dept_id and d.tenant_id = 1) and tenant_id = 1
```
结果正确,CASE表达式中的子查询和EXISTS子句都添加了租户条件。

## 18. 列子查询(testSubqueryAsColumn)
```sql
Original: SELECT u.*, (SELECT COUNT(*) FROM sys_user_post p WHERE p.user_id = u.user_id) as post_count FROM sys_user u
Modified: select u.*, (select count(*) from sys_user_post p where p.user_id = u.user_id and p.tenant_id = 1) as post_count from sys_user u where u.tenant_id = 1
```
结果正确,SELECT列表中的子查询也添加了租户条件。

## 19. CASE WHEN子查询(testCaseWhenSubquery)
```sql
Original: SELECT user_id, CASE WHEN dept_id IN (SELECT dept_id FROM sys_dept WHERE status = 1) THEN 'Active' ELSE 'Inactive' END as status FROM sys_user
Modified: select user_id, case when dept_id in (select dept_id from sys_dept where status = 1 and tenant_id = 1) then 'active' else 'inactive' end as status from sys_user where tenant_id = 1
```
结果正确,CASE表达式中的子查询添加了租户条件。

## 20. INSERT SELECT(testInsertSelect)
```sql
Original: INSERT INTO sys_user (user_name, nick_name) SELECT user_name, nick_name FROM sys_user_temp WHERE dept_id = 1
Modified: insert into sys_user (user_name, nick_name, tenant_id) select user_name, nick_name, 1 from sys_user_temp where dept_id = 1 and tenant_id = 1
```
结果正确,SELECT部分添加了租户条件,同时在INSERT中添加了tenant_id列。

## 21. CROSS JOIN(testCrossJoin)
```sql
Original: SELECT u.*, d.dept_name FROM sys_user u CROSS JOIN sys_dept d
Modified: select u.*, d.dept_name from sys_user u cross join sys_dept d where u.tenant_id = 1 and d.tenant_id = 1
```
结果正确,交叉连接的两个表都添加了租户条件。

## 22. 嵌套子查询(testNestedSubquery)
```sql
Original: SELECT * FROM sys_user WHERE dept_id IN (SELECT dept_id FROM sys_dept WHERE parent_id IN (SELECT dept_id FROM sys_dept WHERE dept_name = 'IT'))
Modified: select * from sys_user where dept_id in (select dept_id from sys_dept where parent_id in (select dept_id from sys_dept where dept_name = 'it' and tenant_id = 1) and tenant_id = 1) and tenant_id = 1
```
结果正确,所有层级的子查询都添加了租户条件。

## 23. EXISTS子句(testExistsClause)
```sql
Original: SELECT * FROM sys_user u WHERE EXISTS (SELECT 1 FROM sys_dept d WHERE d.dept_id = u.dept_id)
Modified: select * from sys_user u where exists (select 1 from sys_dept d where d.dept_id = u.dept_id and d.tenant_id = 1) and u.tenant_id = 1
```
结果正确,主查询和EXISTS子查询都添加了租户条件。

## 24. 空租户ID(testNullTenantId)
```sql
Original: SELECT * FROM sys_user
Modified: select * from sys_user
```
结果正确,当租户ID为null时不添加租户条件。

## 25. LEFT JOIN(testLeftJoin)
```sql
Original: SELECT u.*, d.dept_name FROM sys_user u LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
Modified: select u.*, d.dept_name from sys_user u left join sys_dept d on u.dept_id = d.dept_id where u.tenant_id = 1 and d.tenant_id = 1
```
结果正确,左连接的两个表都添加了租户条件。

## 26. RIGHT JOIN(testRightJoin)
```sql
Original: SELECT u.*, d.dept_name FROM sys_user u RIGHT JOIN sys_dept d ON u.dept_id = d.dept_id
Modified: select u.*, d.dept_name from sys_user u right join sys_dept d on u.dept_id = d.dept_id where u.tenant_id = 1 and d.tenant_id = 1
```
结果正确,右连接的两个表都添加了租户条件。

## 27. INNER JOIN(testInnerJoin)
```sql
Original: SELECT u.*, d.dept_name FROM sys_user u INNER JOIN sys_dept d ON u.dept_id = d.dept_id
Modified: select u.*, d.dept_name from sys_user u inner join sys_dept d on u.dept_id = d.dept_id where u.tenant_id = 1 and d.tenant_id = 1
```
结果正确,内连接的两个表都添加了租户条件。

## 28. DELETE带子查询(testDeleteWithSubquery)
```sql
Original: DELETE FROM sys_user WHERE dept_id IN (SELECT dept_id FROM sys_dept WHERE status = 0)
Modified: delete from sys_user where dept_id in (select dept_id from sys_dept where status = 0 and tenant_id = 1) and tenant_id = 1
```
结果正确,DELETE语句和子查询都添加了租户条件。

## 29. INSERT带子查询(testInsertWithSubquery)
```sql
Original: INSERT INTO sys_user (user_name, dept_id) SELECT user_name, dept_id FROM sys_user_temp WHERE status = 1
Modified: insert into sys_user (user_name, dept_id, tenant_id) select user_name, dept_id, 1 from sys_user_temp where status = 1 and tenant_id = 1
```
结果正确,SELECT部分添加了租户条件,同时在INSERT中添加了tenant_id列。

## 30. 基本INSERT(testInsert)
```sql
Original: INSERT INTO sys_user (user_name, nick_name) VALUES ('test', 'Test User')
Modified: insert into sys_user (user_name, nick_name, tenant_id) values ('test', 'test user', 1)
```
结果正确,添加了tenant_id列和值。

## 31. 基本UPDATE(testUpdate)
```sql
Original: UPDATE sys_user SET nick_name = 'New Name' WHERE user_id = 1
Modified: update sys_user set nick_name = 'new name' where user_id = 1 and tenant_id = 1
```
结果正确,添加了租户条件。

## 32. 基本DELETE(testDelete)
```sql
Original: DELETE FROM sys_user WHERE user_id = 1
Modified: delete from sys_user where user_id = 1 and tenant_id = 1
```
结果正确,添加了租户条件。

## 33. WITH子句(testWithClause)
```sql
Original: WITH dept_users AS (SELECT * FROM sys_user WHERE dept_id = 1) SELECT * FROM dept_users
Modified: with dept_users as (select * from sys_user where dept_id = 1 and tenant_id = 1) select * from dept_users where tenant_id = 1
```
结果正确,CTE和主查询都添加了租户条件。

## 34. 子查询函数(testSubqueryInFunction)
```sql
Original: SELECT * FROM sys_user WHERE dept_id = COALESCE((SELECT dept_id FROM sys_dept WHERE dept_name = 'IT'), 0)
Modified: select * from sys_user where dept_id = coalesce((select dept_id from sys_dept where dept_name = 'it' and tenant_id = 1), 0) and tenant_id = 1
```
结果正确,函数中的子查询和主查询都添加了租户条件。

## 35. 临时表递归(testTemporaryTable)
```sql
Original: WITH RECURSIVE temp_table AS (SELECT * FROM sys_dept WHERE parent_id IS NULL UNION ALL SELECT d.* FROM sys_dept d, temp_table t WHERE d.parent_id = t.dept_id)
Modified: with recursive temp_table as (select * from sys_dept where parent_id is null and tenant_id = 1 union all select d.* from sys_dept d, temp_table t where d.parent_id = t.dept_id and d.tenant_id = 1)
```
结果正确,递归CTE的基础查询和递归部分都添加了租户条件。

## 36. UNION查询(testUnionSelect)
```sql
Original: SELECT * FROM sys_user WHERE dept_id = 1 UNION SELECT * FROM sys_user WHERE user_id = 2
Modified: select * from sys_user where dept_id = 1 and tenant_id = 1 union select * from sys_user where user_id = 2 and tenant_id = 1
```
结果正确,UNION的两个查询都添加了租户条件。

## 37. INSERT带子查询和函数(testInsertWithSubqueryAndFunction)
```sql
Original: INSERT INTO sys_user (user_name, dept_id) SELECT UPPER(user_name), COALESCE(dept_id, 0) FROM sys_user_temp
Modified: insert into sys_user (user_name, dept_id, tenant_id) select upper(user_name), coalesce(dept_id, 0), 1 from sys_user_temp where tenant_id = 1
```
结果正确,保留了函数调用,同时添加了租户条件。

## 38. 子查询IN条件(testSubqueryInCondition)
```sql
Original: SELECT * FROM sys_user WHERE dept_id IN (1, 2) AND user_id IN (SELECT user_id FROM sys_user_temp)
Modified: select * from sys_user where dept_id in (1, 2) and user_id in (select user_id from sys_user_temp where tenant_id = 1) and tenant_id = 1
```
结果正确,IN子句中的子查询添加了租户条件。

# 总结

1. 所有38个测试用例都成功通过
2. SQL语句的各种场景都得到了正确处理:
   - 基础CRUD操作
   - 各种类型的JOIN
   - 子查询(各种位置和深度)
   - 函数调用
   - 集合操作(UNION/INTERSECT)
   - CTE和递归查询
   - 分组和聚合
   - 分页
3. 租户条件的添加符合预期:
   - 正确的位置
   - 正确的数量
   - 正确的表前缀
4. 特殊情况处理得当:
   - 忽略表
   - 空租户ID
   - 表别名
   - 复杂表达式

TenantScopeInterceptor的实现完全满足了多租户数据隔离的要求,能够正确处理各种复杂的SQL场景。

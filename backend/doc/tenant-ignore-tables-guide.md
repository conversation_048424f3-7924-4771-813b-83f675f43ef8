# 租户忽略表配置使用指南

## 概述

将 `IGNORE_TABLES` 配置从代码中移到数据库表中，实现动态配置多租户过滤规则，无需重启应用即可调整忽略的表。

## 设计方案

### 1. 数据库表结构

```sql
CREATE TABLE `sys_tenant_ignore_tables` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_name` varchar(64) NOT NULL COMMENT '表名',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_name` (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户忽略表配置';
```

### 2. 系统架构

#### 2.1 缓存机制
- **Redis 缓存**: 使用 Redis 缓存有效的忽略表名集合，提高查询性能
- **缓存键**: `sys_tenant_ignore_tables`
- **自动刷新**: 增删改操作会自动刷新缓存
- **启动加载**: 应用启动时自动加载配置到缓存

#### 2.2 数据层架构
```
Controller (SysTenantIgnoreTableController)
    ↓
Service (ISysTenantIgnoreTableService)
    ↓
Mapper (SysTenantIgnoreTableMapper)
    ↓
Database (sys_tenant_ignore_tables)
```

## 使用方法

### 1. 添加忽略表

#### 方法一：通过 API 接口
```http
POST /system/tenantIgnoreTable
Content-Type: application/json

{
    "tableName": "sys_log",
    "description": "系统日志表",
    "status": "0",
    "remark": "日志表不需要多租户隔离"
}
```

#### 方法二：直接操作数据库
```sql
INSERT INTO sys_tenant_ignore_tables (table_name, description, status, create_by, create_time) 
VALUES ('sys_log', '系统日志表', '0', 'admin', NOW());
```

### 2. 查询忽略表列表

```http
GET /system/tenantIgnoreTable/list?tableName=sys&status=0
```

### 3. 修改忽略表配置

```http
PUT /system/tenantIgnoreTable
Content-Type: application/json

{
    "id": 1,
    "tableName": "sys_log",
    "description": "系统操作日志表",
    "status": "0"
}
```

### 4. 删除忽略表配置

```http
DELETE /system/tenantIgnoreTable/1,2,3
```

### 5. 刷新缓存

```http
DELETE /system/tenantIgnoreTable/refreshCache
```

## 核心特性

### 1. 动态配置
- 无需重启应用
- 实时生效
- 支持状态启用/停用

### 2. 高性能
- Redis 缓存机制
- 减少数据库查询
- 应用启动时预加载

### 3. 数据安全
- 表名唯一性约束
- 参数验证
- 操作日志记录

### 4. 管理便捷
- RESTful API 接口
- 支持批量操作
- 导出功能支持

## 配置示例

### 默认忽略表配置

```sql
INSERT INTO sys_tenant_ignore_tables (table_name, description, status, create_by, create_time) VALUES
('sys_tenant', '租户表', '0', 'admin', NOW()),
('gen_table', '代码生成表', '0', 'admin', NOW()),
('gen_table_column', '代码生成字段表', '0', 'admin', NOW()),
('information_schema.tables', '系统表信息', '0', 'admin', NOW()),
('information_schema.columns', '系统列信息', '0', 'admin', NOW()),
('wendao_shop_info', '商店信息表', '0', 'admin', NOW()),
('wendao_sms_log', '短信日志表', '0', 'admin', NOW()),
('wendao_sys_login_log', '系统登录日志表', '0', 'admin', NOW()),
('wendao_sys_user', '系统用户表', '0', 'admin', NOW()),
('wendao_wx_qrcode_record', '微信二维码记录表', '0', 'admin', NOW()),
('sys_trial_permission', '试用权限表', '0', 'admin', NOW()),
('sys_tenant_ignore_tables', '租户忽略表配置表', '0', 'admin', NOW());
```

## 权限配置

### 菜单权限
需要在系统菜单中配置相应的权限标识：

- `system:tenantIgnoreTable:list` - 查询列表
- `system:tenantIgnoreTable:query` - 查询详情
- `system:tenantIgnoreTable:add` - 新增配置
- `system:tenantIgnoreTable:edit` - 编辑配置
- `system:tenantIgnoreTable:remove` - 删除配置
- `system:tenantIgnoreTable:export` - 导出数据

## 最佳实践

### 1. 配置管理
- 定期备份配置数据
- 为每个忽略表添加清晰的描述
- 合理使用状态字段控制

### 2. 性能优化
- 避免频繁修改配置
- 批量操作优于单个操作
- 合理使用缓存刷新功能

### 3. 安全考虑
- 严格控制权限分配
- 记录所有配置变更
- 定期审查忽略表配置

## 迁移步骤

### 1. 执行数据库脚本
运行 `sql/tenant_ignore_tables.sql` 脚本创建表和初始数据。

### 2. 部署新代码
包含以下新增的文件：
- `SysTenantIgnoreTable.java` (实体类)
- `SysTenantIgnoreTableMapper.java` (Mapper 接口)
- `SysTenantIgnoreTableMapper.xml` (Mapper XML)
- `ISysTenantIgnoreTableService.java` (Service 接口)
- `SysTenantIgnoreTableServiceImpl.java` (Service 实现)
- `SysTenantIgnoreTableController.java` (Controller)

### 3. 修改的文件
- `TenantScopeInterceptor.java` - 移除硬编码配置，使用数据库配置
- `CacheConstants.java` - 添加缓存键常量

### 4. 验证功能
- 启动应用，检查缓存是否正确加载
- 测试增删改查功能
- 验证多租户过滤是否正常工作

## 常见问题

### Q: 如何确认配置是否生效？
A: 可以通过查看 Redis 缓存内容或调用刷新缓存接口来确认。

### Q: 可以动态添加新的忽略表吗？
A: 是的，通过 API 接口添加后会立即生效，无需重启应用。

### Q: 如果缓存出现问题怎么办？
A: 可以调用刷新缓存接口 `/system/tenantIgnoreTable/refreshCache` 重新加载配置。

### Q: 配置表本身需要多租户隔离吗？
A: 不需要，配置表 `sys_tenant_ignore_tables` 本身已经被加入忽略列表。 
package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.mapper.WendaoShopMapper;
import com.ruoyi.system.domain.WendaoShop;
import com.ruoyi.system.service.IWendaoShopService;

/**
 * 普通店铺信息管理 服务实现
 */
@Service
public class WendaoShopServiceImpl implements IWendaoShopService {
    @Autowired
    private WendaoShopMapper wendaoShopMapper;

    /**
     * 查询普通店铺信息
     */
    @Override
    public WendaoShop selectWendaoShopById(Long id) {
        return wendaoShopMapper.selectWendaoShopById(id);
    }

    /**
     * 根据店铺ID查询普通店铺信息
     */
    @Override
    public WendaoShop selectWendaoShopByShopId(String shopId) {
        return wendaoShopMapper.selectWendaoShopByShopId(shopId);
    }

    /**
     * 根据应用ID查询普通店铺信息
     */
    @Override
    public WendaoShop selectWendaoShopByAppId(String appId) {
        return wendaoShopMapper.selectWendaoShopByAppId(appId);
    }

    /**
     * 根据店主ID查询普通店铺信息
     */
    @Override
    public WendaoShop selectWendaoShopByCreatorId(String creatorId) {
        return wendaoShopMapper.selectWendaoShopByCreatorId(creatorId);
    }

    /**
     * 根据店主ID查询普通店铺信息列表
     */
    @Override
    public List<WendaoShop> selectWendaoShopListByCreatorId(String creatorId) {
        return wendaoShopMapper.selectWendaoShopListByCreatorId(creatorId);
    }

    /**
     * 查询普通店铺信息列表
     */
    @Override
    public List<WendaoShop> selectWendaoShopList(WendaoShop wendaoShop) {
        return wendaoShopMapper.selectWendaoShopList(wendaoShop);
    }

    /**
     * 新增普通店铺信息
     */
    @Override
    public int insertWendaoShop(WendaoShop wendaoShop) {
        // 设置创建时间
        if (wendaoShop.getCreatedAt() == null) {
            wendaoShop.setCreatedAt(new Date());
        }
        if (wendaoShop.getCreatedTime() == null) {
            wendaoShop.setCreatedTime(new Date());
        }
        return wendaoShopMapper.insertWendaoShop(wendaoShop);
    }

    /**
     * 修改普通店铺信息
     */
    @Override
    public int updateWendaoShop(WendaoShop wendaoShop) {
        // 设置更新时间
        wendaoShop.setUpdatedTime(new Date());
        return wendaoShopMapper.updateWendaoShop(wendaoShop);
    }

    /**
     * 删除普通店铺信息
     */
    @Override
    public int deleteWendaoShopByIds(Long[] ids) {
        return wendaoShopMapper.deleteWendaoShopByIds(ids);
    }

    /**
     * 删除普通店铺信息
     */
    @Override
    public int deleteWendaoShopById(Long id) {
        return wendaoShopMapper.deleteWendaoShopById(id);
    }

    /**
     * 校验店铺ID是否唯一
     */
    @Override
    public boolean checkShopIdUnique(String shopId) {
        WendaoShop wendaoShop = wendaoShopMapper.checkShopIdUnique(shopId);
        return StringUtils.isNull(wendaoShop);
    }

    /**
     * 校验应用ID是否唯一
     */
    @Override
    public boolean checkAppIdUnique(String appId) {
        WendaoShop wendaoShop = wendaoShopMapper.checkAppIdUnique(appId);
        return StringUtils.isNull(wendaoShop);
    }

    /**
     * 封禁店铺
     */
    @Override
    public int sealShop(Long id) {
        WendaoShop wendaoShop = new WendaoShop();
        wendaoShop.setId(id);
        wendaoShop.setIsSealed(1);
        wendaoShop.setUpdatedTime(new Date());
        return wendaoShopMapper.updateWendaoShop(wendaoShop);
    }

    /**
     * 解封店铺
     */
    @Override
    public int unsealShop(Long id) {
        WendaoShop wendaoShop = new WendaoShop();
        wendaoShop.setId(id);
        wendaoShop.setIsSealed(0);
        wendaoShop.setIsWaitSeal(0);
        wendaoShop.setSealApplyTime(null);
        wendaoShop.setReadySealAt(null);
        wendaoShop.setUpdatedTime(new Date());
        return wendaoShopMapper.updateWendaoShop(wendaoShop);
    }

    /**
     * 关闭店铺
     */
    @Override
    public int closeShop(Long id) {
        WendaoShop wendaoShop = new WendaoShop();
        wendaoShop.setId(id);
        wendaoShop.setStatus(1);
        wendaoShop.setUpdatedTime(new Date());
        return wendaoShopMapper.updateWendaoShop(wendaoShop);
    }

    /**
     * 开启店铺
     */
    @Override
    public int openShop(Long id) {
        WendaoShop wendaoShop = new WendaoShop();
        wendaoShop.setId(id);
        wendaoShop.setStatus(0);
        wendaoShop.setUpdatedTime(new Date());
        return wendaoShopMapper.updateWendaoShop(wendaoShop);
    }

    /**
     * 注销店铺
     */
    @Override
    public int dropShop(Long id) {
        WendaoShop wendaoShop = new WendaoShop();
        wendaoShop.setId(id);
        wendaoShop.setIsDrop(1);
        wendaoShop.setUpdatedTime(new Date());
        return wendaoShopMapper.updateWendaoShop(wendaoShop);
    }

    /**
     * 恢复店铺
     */
    @Override
    public int restoreShop(Long id) {
        WendaoShop wendaoShop = new WendaoShop();
        wendaoShop.setId(id);
        wendaoShop.setIsDrop(0);
        wendaoShop.setUpdatedTime(new Date());
        return wendaoShopMapper.updateWendaoShop(wendaoShop);
    }

    /**
     * 更新店铺过期状态
     */
    @Override
    public int updateExpiredStatus() {
        return wendaoShopMapper.updateExpiredStatus();
    }

    /**
     * 查询即将过期的店铺列表
     */
    @Override
    public List<WendaoShop> selectExpiringSoonShops(int days) {
        return wendaoShopMapper.selectExpiringSoonShops(days);
    }

    /**
     * 查询已过期的店铺列表
     */
    @Override
    public List<WendaoShop> selectExpiredShops() {
        return wendaoShopMapper.selectExpiredShops();
    }

    /**
     * 设置最后登录店铺
     */
    @Override
    public int setLastLoginShop(String creatorId, String shopId) {
        // 先将该创作者的所有店铺的最后登录状态设为0
        wendaoShopMapper.clearLastLoginByCreatorId(creatorId);
        // 再将指定店铺的最后登录状态设为1
        return wendaoShopMapper.setLastLoginShop(shopId);
    }
}

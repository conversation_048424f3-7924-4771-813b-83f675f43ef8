package com.ruoyi.web.controller.system;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.framework.web.jwt.CreatorJwtUtil;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.service.ICreatorInfoService;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.IWxLoginService;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;

/**
 * 创作者店铺controller
 */
@Slf4j
@RestController
@RequestMapping("/creator_shop")
public class CreatorShopController extends BaseController {
    @Autowired
    private ISysTenantService sysTenantService;
    @Autowired
    private CreatorJwtUtil creatorJwtUtil;


    //先查询店铺是否已经超过了数量,总数只能是3个,如果为3个时不能创建
    //创建店铺,并设置店铺id
    @Anonymous
    @PostMapping("/createShop")
    public AjaxResult createShop(@RequestBody CreateShopDTO createShopDTO) {
        //获取creatorId
        String creatorId = creatorJwtUtil.getCreatorId();
        //查询店铺数量,tenant_code=creatorId
        //sysTenantService
        SysTenant tenant = new SysTenant();
        tenant.setTenantCode(creatorId);
        List<SysTenant> sysTenants = sysTenantService.selectTenantList(tenant);
        if (sysTenants.size() >= 3) {
            return error("店铺数量已经超过了3个,不能再创建");
        }
        //创建店铺
        SysTenant sysTenant = new SysTenant();
        String shopId = UUID.randomUUID().toString().replace("-", "");
        sysTenant.setShopId(shopId);
        sysTenant.setTenantCode(creatorId);
        sysTenant.setShopName(createShopDTO.getShopName());
        sysTenantService.insertTenant(sysTenant);
        //sysTenant.setTenantName(createShopDTO.getShopName());
        return AjaxResult.success();
    }


    // DTO定义
    @Data
    @Builder
    public static class CreateShopDTO {
        private String shopName;
    }
}
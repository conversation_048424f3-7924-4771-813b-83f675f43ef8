package com.ruoyi.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 试用权限验证注解
 * 
 * <AUTHOR>
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TrialPermission
{
    /**
     * 权限字符串
     */
    String value() default "";
    
    /**
     * 权限类型：1-读权限，2-写权限
     */
    int type(); //default 1;
    
    /**
     * 试用期是否可用
     */
    boolean trialAvailable();// default true;
    
    /**
     * 试用过期后是否可用
     */
    boolean expiredAvailable();// default false;
} 
package com.ruoyi.web.controller.system;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.framework.web.service.TrialPermissionService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.domain.SysTrialPermission;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.ISysTrialPermissionService;

/**
 * 试用管理Controller单元测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
public class SysTrialControllerTest {

    @Mock
    private ISysTenantService tenantService;

    @Mock
    private ISysTrialPermissionService trialPermissionService;

    @Mock
    private TrialPermissionService trialService;

    @InjectMocks
    private SysTrialController sysTrialController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    private SysTrialPermission testPermission;
    private SysTenant testTenant;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(sysTrialController).build();
        objectMapper = new ObjectMapper();

        // 初始化测试数据
        testPermission = new SysTrialPermission();
        testPermission.setId(1L);
        testPermission.setPermissionCode("system:user:list");
        testPermission.setPermissionName("用户查询");
        testPermission.setPermissionType(1);
        testPermission.setTrialAvailable(1);
        testPermission.setExpiredAvailable(1);
        testPermission.setCreateTime(new Date());

        testTenant = new SysTenant();
        testTenant.setShopId("tenant001");
        testTenant.setTenantName("测试租户");
        testTenant.setTrialStatus(1);
        testTenant.setTrialDays(7);
        testTenant.setTrialStartTime(new Date());
        testTenant.setTrialEndTime(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));
    }

    @Test
    public void testPermissionList() throws Exception {
        // Given
        List<SysTrialPermission> permissions = Arrays.asList(testPermission);
        when(trialPermissionService.selectSysTrialPermissionList(any())).thenReturn(permissions);

        // When & Then
        mockMvc.perform(get("/system/trial/permission/list")
                .param("permissionType", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.rows[0].id").value(1))
                .andExpect(jsonPath("$.rows[0].permissionCode").value("system:user:list"));

        verify(trialPermissionService).selectSysTrialPermissionList(any());
    }

    @Test
    public void testGetPermissionInfo() throws Exception {
        // Given
        when(trialPermissionService.selectSysTrialPermissionById(1L)).thenReturn(testPermission);

        // When & Then
        mockMvc.perform(get("/system/trial/permission/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.permissionCode").value("system:user:list"));

        verify(trialPermissionService).selectSysTrialPermissionById(1L);
    }

    @Test
    public void testAddPermission() throws Exception {
        // Given
        SysTrialPermission newPermission = new SysTrialPermission();
        newPermission.setPermissionCode("system:user:add");
        newPermission.setPermissionName("用户新增");
        newPermission.setPermissionType(2);
        newPermission.setTrialAvailable(1);
        newPermission.setExpiredAvailable(0);

        when(trialPermissionService.insertSysTrialPermission(any())).thenReturn(1);

        // When & Then
        mockMvc.perform(post("/system/trial/permission")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newPermission)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));

        verify(trialPermissionService).insertSysTrialPermission(any());
    }

    @Test
    public void testEditPermission() throws Exception {
        // Given
        testPermission.setPermissionName("用户查询-修改");
        when(trialPermissionService.updateSysTrialPermission(any())).thenReturn(1);

        // When & Then
        mockMvc.perform(put("/system/trial/permission")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testPermission)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(trialPermissionService).updateSysTrialPermission(any());
    }

    @Test
    public void testRemovePermission() throws Exception {
        // Given
        when(trialPermissionService.deleteSysTrialPermissionByIds(any())).thenReturn(2);

        // When & Then
        mockMvc.perform(delete("/system/trial/permission/1,2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(trialPermissionService).deleteSysTrialPermissionByIds(any());
    }

    @Test
    public void testTenantList() throws Exception {
        // Given
        List<SysTenant> tenants = Arrays.asList(testTenant);
        when(tenantService.selectTenantList(any())).thenReturn(tenants);

        // When & Then
        mockMvc.perform(get("/system/trial/tenant/list")
                .param("trialStatus", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.rows[0].shopId").value("tenant001"));

        verify(tenantService).selectTenantList(any());
    }

    @Test
    public void testStartTrial_Success() throws Exception {
        // Given
        when(trialService.startTrial("tenant001", 7)).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/system/trial/start")
                .param("tenantId", "tenant001")
                .param("trialDays", "7"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("试用开始成功"));

        verify(trialService).startTrial("tenant001", 7);
    }

    @Test
    public void testStartTrial_Failed() throws Exception {
        // Given
        when(trialService.startTrial("tenant001", 7)).thenReturn(false);

        // When & Then
        mockMvc.perform(post("/system/trial/start")
                .param("tenantId", "tenant001")
                .param("trialDays", "7"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("试用开始失败"));

        verify(trialService).startTrial("tenant001", 7);
    }

    @Test
    public void testConvertTenant_Success() throws Exception {
        // Given
        when(trialService.convertTenant("tenant001")).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/system/trial/convert")
                .param("tenantId", "tenant001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("转正成功"));

        verify(trialService).convertTenant("tenant001");
    }

    @Test
    public void testConvertTenant_Failed() throws Exception {
        // Given
        when(trialService.convertTenant("tenant001")).thenReturn(false);

        // When & Then
        mockMvc.perform(post("/system/trial/convert")
                .param("tenantId", "tenant001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("转正失败"));

        verify(trialService).convertTenant("tenant001");
    }

    @Test
    public void testGetTenantTrialStatus_Success() throws Exception {
        // Given
        when(tenantService.selectTenantById("tenant001")).thenReturn(testTenant);

        // When & Then
        mockMvc.perform(get("/system/trial/tenant/tenant001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.shopId").value("tenant001"));

        verify(tenantService).selectTenantById("tenant001");
    }

    @Test
    public void testGetTenantTrialStatus_NotFound() throws Exception {
        // Given
        when(tenantService.selectTenantById("nonexistent")).thenReturn(null);

        // When & Then
        mockMvc.perform(get("/system/trial/tenant/nonexistent"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("租户不存在"));

        verify(tenantService).selectTenantById("nonexistent");
    }

    @Test
    public void testClearTrialCache() throws Exception {
        // Given
        doNothing().when(trialService).clearTenantTrialCache("tenant001");

        // When & Then
        mockMvc.perform(delete("/system/trial/cache/tenant001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("缓存清除成功"));

        verify(trialService).clearTenantTrialCache("tenant001");
    }

    @Test
    public void testPermissionList_EmptyResult() throws Exception {
        // Given
        when(trialPermissionService.selectSysTrialPermissionList(any())).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(get("/system/trial/permission/list"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.rows").isEmpty());

        verify(trialPermissionService).selectSysTrialPermissionList(any());
    }

    @Test
    public void testAddPermission_ValidationError() throws Exception {
        // Given - 创建一个无效的权限对象（缺少必要字段）
        SysTrialPermission invalidPermission = new SysTrialPermission();
        // 不设置必要字段，这将触发@NotBlank和@NotNull验证

        // When & Then
        mockMvc.perform(post("/system/trial/permission")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidPermission)))
                .andExpect(status().isBadRequest()); // 在测试环境中，验证失败直接返回400状态码
    }

    @Test
    public void testStartTrial_DefaultTrialDays() throws Exception {
        // Given
        when(trialService.startTrial("tenant001", 7)).thenReturn(true);

        // When & Then - 不传trialDays参数，应该使用默认值7
        mockMvc.perform(post("/system/trial/start")
                .param("tenantId", "tenant001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        verify(trialService).startTrial("tenant001", 7);
    }
} 
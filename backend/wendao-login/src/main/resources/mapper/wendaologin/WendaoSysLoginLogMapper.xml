<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wendaologin.mapper.WendaoSysLoginLogMapper">
    
    <resultMap type="WendaoSysLoginLog" id="WendaoSysLoginLogResult">
        <result property="id"    column="id"    />
        <result property="bUserId"    column="b_user_id"    />
        <result property="loginType"    column="login_type"    />
        <result property="ip"    column="ip"    />
        <result property="deviceInfo"    column="device_info"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWendaoSysLoginLogVo">
        select id, b_user_id, login_type, ip, device_info, create_time, update_time from wendao_sys_login_log
    </sql>

    <select id="selectWendaoSysLoginLogList" parameterType="WendaoSysLoginLog" resultMap="WendaoSysLoginLogResult">
        <include refid="selectWendaoSysLoginLogVo"/>
        <where>  
            <if test="bUserId != null  and bUserId != ''"> and b_user_id = #{bUserId}</if>
            <if test="loginType != null "> and login_type = #{loginType}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="deviceInfo != null  and deviceInfo != ''"> and device_info = #{deviceInfo}</if>
        </where>
    </select>
    
    <select id="selectWendaoSysLoginLogById" parameterType="Long" resultMap="WendaoSysLoginLogResult">
        <include refid="selectWendaoSysLoginLogVo"/>
        where id = #{id}
    </select>

    <insert id="insertWendaoSysLoginLog" parameterType="WendaoSysLoginLog" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_sys_login_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bUserId != null">b_user_id,</if>
            <if test="loginType != null">login_type,</if>
            <if test="ip != null">ip,</if>
            <if test="deviceInfo != null">device_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bUserId != null">#{bUserId},</if>
            <if test="loginType != null">#{loginType},</if>
            <if test="ip != null">#{ip},</if>
            <if test="deviceInfo != null">#{deviceInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWendaoSysLoginLog" parameterType="WendaoSysLoginLog">
        update wendao_sys_login_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="bUserId != null">b_user_id = #{bUserId},</if>
            <if test="loginType != null">login_type = #{loginType},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="deviceInfo != null">device_info = #{deviceInfo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWendaoSysLoginLogById" parameterType="Long">
        delete from wendao_sys_login_log where id = #{id}
    </delete>

    <delete id="deleteWendaoSysLoginLogByIds" parameterType="String">
        delete from wendao_sys_login_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
package com.ruoyi.wendaologin.service.impl;

import com.ruoyi.wendaologin.config.TencentCaptchaConfig;
import com.ruoyi.wendaologin.dto.CaptchaVerifyDTO;
import com.ruoyi.wendaologin.service.CaptchaService;
import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultResponse;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TencentCaptchaServiceImpl implements CaptchaService {
    
    @Autowired
    private TencentCaptchaConfig captchaConfig;
    
    @Override
    public boolean verify(CaptchaVerifyDTO verifyDTO) {
        try {
            // 实例化一个认证对象
            Credential cred = new Credential(captchaConfig.getSecretId(), captchaConfig.getSecretKey());
            
            // 实例化一个HTTP选项
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("captcha.tencentcloudapi.com");
            
            // 实例化一个client选项
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            
            // 实例化验证码客户端对象
            CaptchaClient client = new CaptchaClient(cred, "", clientProfile);
            
            // 实例化一个请求对象
            DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
            req.setCaptchaType(9L);  // 验证码类型，9：滑块验证码
            req.setTicket(verifyDTO.getTicket());
            req.setUserIp(verifyDTO.getUserIp());
            req.setRandstr(verifyDTO.getRandstr());
            req.setCaptchaAppId(Long.parseLong(captchaConfig.getCaptchaAppId()));
            req.setAppSecretKey(captchaConfig.getAppSecretKey());
            
            // 发送请求，返回响应
            DescribeCaptchaResultResponse resp = client.DescribeCaptchaResult(req);
            
            // 记录验证结果
            log.info("验证码验证结果：CaptchaCode={}, CaptchaMsg={}, userIp={}", 
                    resp.getCaptchaCode(), resp.getCaptchaMsg(), verifyDTO.getUserIp());
            
            // 返回验证结果（1：验证成功，其他：验证失败）
            return resp.getCaptchaCode() == 1L;
        } catch (TencentCloudSDKException e) {
            log.error("验证码验证失败", e);
            return false;
        }
    }
} 
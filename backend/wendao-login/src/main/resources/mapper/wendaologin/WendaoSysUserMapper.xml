<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wendaologin.mapper.WendaoSysUserMapper">
    
    <resultMap type="WendaoSysUser" id="WendaoSysUserResult">
        <result property="bUserId"    column="b_user_id"    />
        <result property="wxOpenId"    column="wx_open_id"    />
        <result property="wxUnionId"    column="wx_union_id"    />
        <result property="mobile"    column="mobile"    />
        <result property="nationCode"    column="nation_code"    />
        <result property="password"    column="password"    />
        <result property="nickname"    column="nickname"    />
        <result property="headImgUrl"    column="head_img_url"    />
        <result property="wxHeadImgUrl"    column="wx_head_img_url"    />
        <result property="wxNickName"    column="wx_nick_name"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="gender"    column="gender"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWendaoSysUserVo">
        select b_user_id, wx_open_id, wx_union_id, mobile, nation_code, password, nickname, head_img_url, wx_head_img_url, wx_nick_name, avatar_url, gender, status, create_time, update_time from wendao_sys_user
    </sql>

    <select id="selectWendaoSysUserList" parameterType="WendaoSysUser" resultMap="WendaoSysUserResult">
        <include refid="selectWendaoSysUserVo"/>
        <where>  
            <if test="wxOpenId != null  and wxOpenId != ''"> and wx_open_id = #{wxOpenId}</if>
            <if test="wxUnionId != null  and wxUnionId != ''"> and wx_union_id = #{wxUnionId}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="nationCode != null  and nationCode != ''"> and nation_code = #{nationCode}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="headImgUrl != null  and headImgUrl != ''"> and head_img_url = #{headImgUrl}</if>
            <if test="wxHeadImgUrl != null  and wxHeadImgUrl != ''"> and wx_head_img_url = #{wxHeadImgUrl}</if>
            <if test="wxNickName != null  and wxNickName != ''"> and wx_nick_name like concat('%', #{wxNickName}, '%')</if>
            <if test="avatarUrl != null  and avatarUrl != ''"> and avatar_url = #{avatarUrl}</if>
            <if test="gender != null "> and gender = #{gender}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectWendaoSysUserByBUserId" parameterType="String" resultMap="WendaoSysUserResult">
        <include refid="selectWendaoSysUserVo"/>
        where b_user_id = #{bUserId}
    </select>
    <select id="selectByMobile" resultMap="WendaoSysUserResult">
        <include refid="selectWendaoSysUserVo"/>
        where mobile = #{mobile} limit 1
    </select>
    <select id="selectByWxOpenId" resultMap="WendaoSysUserResult">
        <include refid="selectWendaoSysUserVo"/>
        where wx_open_id = #{wxOpenId} limit 1
    </select>

    <insert id="insertWendaoSysUser" parameterType="WendaoSysUser">
        insert into wendao_sys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bUserId != null">b_user_id,</if>
            <if test="wxOpenId != null">wx_open_id,</if>
            <if test="wxUnionId != null">wx_union_id,</if>
            <if test="mobile != null">mobile,</if>
            <if test="nationCode != null">nation_code,</if>
            <if test="password != null">password,</if>
            <if test="nickname != null">nickname,</if>
            <if test="headImgUrl != null">head_img_url,</if>
            <if test="wxHeadImgUrl != null">wx_head_img_url,</if>
            <if test="wxNickName != null">wx_nick_name,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="gender != null">gender,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bUserId != null">#{bUserId},</if>
            <if test="wxOpenId != null">#{wxOpenId},</if>
            <if test="wxUnionId != null">#{wxUnionId},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="nationCode != null">#{nationCode},</if>
            <if test="password != null">#{password},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="headImgUrl != null">#{headImgUrl},</if>
            <if test="wxHeadImgUrl != null">#{wxHeadImgUrl},</if>
            <if test="wxNickName != null">#{wxNickName},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="gender != null">#{gender},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWendaoSysUser" parameterType="WendaoSysUser">
        update wendao_sys_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="wxOpenId != null">wx_open_id = #{wxOpenId},</if>
            <if test="wxUnionId != null">wx_union_id = #{wxUnionId},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="nationCode != null">nation_code = #{nationCode},</if>
            <if test="password != null">password = #{password},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="headImgUrl != null">head_img_url = #{headImgUrl},</if>
            <if test="wxHeadImgUrl != null">wx_head_img_url = #{wxHeadImgUrl},</if>
            <if test="wxNickName != null">wx_nick_name = #{wxNickName},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where b_user_id = #{bUserId}
    </update>

    <delete id="deleteWendaoSysUserByBUserId" parameterType="String">
        delete from wendao_sys_user where b_user_id = #{bUserId}
    </delete>

    <delete id="deleteWendaoSysUserByBUserIds" parameterType="String">
        delete from wendao_sys_user where b_user_id in 
        <foreach item="bUserId" collection="array" open="(" separator="," close=")">
            #{bUserId}
        </foreach>
    </delete>
</mapper>
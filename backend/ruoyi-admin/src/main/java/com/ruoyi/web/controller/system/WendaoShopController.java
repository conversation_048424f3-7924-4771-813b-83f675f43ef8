package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.WendaoShop;
import com.ruoyi.system.service.IWendaoShopService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 普通店铺信息Controller
 */
@RestController
@RequestMapping("/system/wendaoShop")
public class WendaoShopController extends BaseController {
    @Autowired
    private IWendaoShopService wendaoShopService;

    /**
     * 查询普通店铺信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:list')")
    @GetMapping("/list")
    public TableDataInfo list(WendaoShop wendaoShop) {
        startPage();
        List<WendaoShop> list = wendaoShopService.selectWendaoShopList(wendaoShop);
        return getDataTable(list);
    }

    /**
     * 导出普通店铺信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:export')")
    @Log(title = "普通店铺信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WendaoShop wendaoShop) {
        List<WendaoShop> list = wendaoShopService.selectWendaoShopList(wendaoShop);
        ExcelUtil<WendaoShop> util = new ExcelUtil<WendaoShop>(WendaoShop.class);
        util.exportExcel(response, list, "普通店铺信息数据");
    }

    /**
     * 获取普通店铺信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(wendaoShopService.selectWendaoShopById(id));
    }

    /**
     * 根据店铺ID获取普通店铺信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:query')")
    @GetMapping(value = "/shopId/{shopId}")
    public AjaxResult getInfoByShopId(@PathVariable("shopId") String shopId) {
        return success(wendaoShopService.selectWendaoShopByShopId(shopId));
    }

    /**
     * 根据应用ID获取普通店铺信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:query')")
    @GetMapping(value = "/appId/{appId}")
    public AjaxResult getInfoByAppId(@PathVariable("appId") String appId) {
        return success(wendaoShopService.selectWendaoShopByAppId(appId));
    }

    /**
     * 根据店主ID获取普通店铺信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:query')")
    @GetMapping(value = "/creatorId/{creatorId}")
    public AjaxResult getInfoByCreatorId(@PathVariable("creatorId") String creatorId) {
        return success(wendaoShopService.selectWendaoShopByCreatorId(creatorId));
    }

    /**
     * 根据店主ID获取普通店铺信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:query')")
    @GetMapping(value = "/listByCreatorId/{creatorId}")
    public AjaxResult getListByCreatorId(@PathVariable("creatorId") String creatorId) {
        return success(wendaoShopService.selectWendaoShopListByCreatorId(creatorId));
    }

    /**
     * 新增普通店铺信息
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:add')")
    @Log(title = "普通店铺信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WendaoShop wendaoShop) {
        // 校验店铺ID是否唯一
        if (!wendaoShopService.checkShopIdUnique(wendaoShop.getShopId())) {
            return error("新增店铺'" + wendaoShop.getShopName() + "'失败，店铺ID已存在");
        }
        // 校验应用ID是否唯一
        if (!wendaoShopService.checkAppIdUnique(wendaoShop.getAppId())) {
            return error("新增店铺'" + wendaoShop.getShopName() + "'失败，应用ID已存在");
        }
        return toAjax(wendaoShopService.insertWendaoShop(wendaoShop));
    }

    /**
     * 修改普通店铺信息
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:edit')")
    @Log(title = "普通店铺信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WendaoShop wendaoShop) {
        // 获取原有店铺信息
        WendaoShop originalShop = wendaoShopService.selectWendaoShopById(wendaoShop.getId());
        if (originalShop == null) {
            return error("店铺不存在");
        }
        
        // 如果店铺ID发生变化，校验新的店铺ID是否唯一
        if (!originalShop.getShopId().equals(wendaoShop.getShopId())) {
            if (!wendaoShopService.checkShopIdUnique(wendaoShop.getShopId())) {
                return error("修改店铺'" + wendaoShop.getShopName() + "'失败，店铺ID已存在");
            }
        }
        
        // 如果应用ID发生变化，校验新的应用ID是否唯一
        if (!originalShop.getAppId().equals(wendaoShop.getAppId())) {
            if (!wendaoShopService.checkAppIdUnique(wendaoShop.getAppId())) {
                return error("修改店铺'" + wendaoShop.getShopName() + "'失败，应用ID已存在");
            }
        }
        
        return toAjax(wendaoShopService.updateWendaoShop(wendaoShop));
    }

    /**
     * 删除普通店铺信息
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:remove')")
    @Log(title = "普通店铺信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(wendaoShopService.deleteWendaoShopByIds(ids));
    }

    /**
     * 封禁店铺
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:seal')")
    @Log(title = "封禁店铺", businessType = BusinessType.UPDATE)
    @PutMapping("/seal/{id}")
    public AjaxResult sealShop(@PathVariable Long id) {
        return toAjax(wendaoShopService.sealShop(id));
    }

    /**
     * 解封店铺
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:unseal')")
    @Log(title = "解封店铺", businessType = BusinessType.UPDATE)
    @PutMapping("/unseal/{id}")
    public AjaxResult unsealShop(@PathVariable Long id) {
        return toAjax(wendaoShopService.unsealShop(id));
    }

    /**
     * 关闭店铺
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:close')")
    @Log(title = "关闭店铺", businessType = BusinessType.UPDATE)
    @PutMapping("/close/{id}")
    public AjaxResult closeShop(@PathVariable Long id) {
        return toAjax(wendaoShopService.closeShop(id));
    }

    /**
     * 开启店铺
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:open')")
    @Log(title = "开启店铺", businessType = BusinessType.UPDATE)
    @PutMapping("/open/{id}")
    public AjaxResult openShop(@PathVariable Long id) {
        return toAjax(wendaoShopService.openShop(id));
    }

    /**
     * 注销店铺
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:drop')")
    @Log(title = "注销店铺", businessType = BusinessType.UPDATE)
    @PutMapping("/drop/{id}")
    public AjaxResult dropShop(@PathVariable Long id) {
        return toAjax(wendaoShopService.dropShop(id));
    }

    /**
     * 恢复店铺
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:restore')")
    @Log(title = "恢复店铺", businessType = BusinessType.UPDATE)
    @PutMapping("/restore/{id}")
    public AjaxResult restoreShop(@PathVariable Long id) {
        return toAjax(wendaoShopService.restoreShop(id));
    }

    /**
     * 更新店铺过期状态
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:updateExpired')")
    @Log(title = "更新店铺过期状态", businessType = BusinessType.UPDATE)
    @PutMapping("/updateExpiredStatus")
    public AjaxResult updateExpiredStatus() {
        int count = wendaoShopService.updateExpiredStatus();
        return success("成功更新 " + count + " 个店铺的过期状态");
    }

    /**
     * 查询即将过期的店铺列表
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:query')")
    @GetMapping("/expiringSoon/{days}")
    public AjaxResult getExpiringSoonShops(@PathVariable int days) {
        List<WendaoShop> list = wendaoShopService.selectExpiringSoonShops(days);
        return success(list);
    }

    /**
     * 查询已过期的店铺列表
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:query')")
    @GetMapping("/expired")
    public AjaxResult getExpiredShops() {
        List<WendaoShop> list = wendaoShopService.selectExpiredShops();
        return success(list);
    }

    /**
     * 设置最后登录店铺
     */
    @PreAuthorize("@ss.hasPermi('system:wendaoShop:setLastLogin')")
    @Log(title = "设置最后登录店铺", businessType = BusinessType.UPDATE)
    @PutMapping("/setLastLogin/{creatorId}/{shopId}")
    public AjaxResult setLastLoginShop(@PathVariable String creatorId, @PathVariable String shopId) {
        return toAjax(wendaoShopService.setLastLoginShop(creatorId, shopId));
    }
}

package com.ruoyi.framework.aspectj;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ruoyi.common.annotation.TrialPermission;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.framework.web.service.TrialPermissionService;

/**
 * 试用权限验证处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class TrialPermissionAspect
{
    @Autowired
    private TrialPermissionService trialPermissionService;

    @Around("@annotation(trialPermission)")
    public Object around(ProceedingJoinPoint point, TrialPermission trialPermission) throws Throwable
    {
        String permission = trialPermission.value();
        
        if (!trialPermissionService.hasTrialPermi(permission))
        {
            String message = getPermissionDeniedMessage(trialPermission);
            throw new ServiceException(message);
        }
        
        return point.proceed();
    }
    
    /**
     * 获取权限拒绝消息
     */
    private String getPermissionDeniedMessage(TrialPermission trialPermission)
    {
        if (trialPermission.type() == 2) // 写权限
        {
            return "试用期已过期，写操作权限已被限制。请联系管理员升级账户。";
        }
        else
        {
            return "权限不足，无法访问该功能。";
        }
    }
} 
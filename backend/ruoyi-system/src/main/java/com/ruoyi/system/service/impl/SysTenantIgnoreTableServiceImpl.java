package com.ruoyi.system.service.impl;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysTenantIgnoreTable;
import com.ruoyi.system.mapper.SysTenantIgnoreTableMapper;
import com.ruoyi.system.service.ISysTenantIgnoreTableService;

/**
 * 租户忽略表配置 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class SysTenantIgnoreTableServiceImpl implements ISysTenantIgnoreTableService
{
    @Autowired
    private SysTenantIgnoreTableMapper tenantIgnoreTableMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 项目启动时，初始化忽略表到缓存
     */
    @PostConstruct
    public void init()
    {
        loadingIgnoreTableCache();
    }

    /**
     * 查询租户忽略表配置信息
     * 
     * @param id 主键ID
     * @return 租户忽略表配置信息
     */
    @Override
    public SysTenantIgnoreTable selectTenantIgnoreTableById(Long id)
    {
        return tenantIgnoreTableMapper.selectTenantIgnoreTableById(id);
    }

    /**
     * 查询租户忽略表配置列表
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 租户忽略表配置集合
     */
    @Override
    public List<SysTenantIgnoreTable> selectTenantIgnoreTableList(SysTenantIgnoreTable sysTenantIgnoreTable)
    {
        return tenantIgnoreTableMapper.selectTenantIgnoreTableList(sysTenantIgnoreTable);
    }

    /**
     * 获取所有有效的忽略表名集合
     * 
     * @return 表名集合
     */
    @Override
    public Set<String> getActiveIgnoreTableNames()
    {
        Set<String> ignoreTableNames = redisCache.getCacheObject(CacheConstants.SYS_TENANT_IGNORE_TABLES_KEY);
        if (StringUtils.isNotNull(ignoreTableNames))
        {
            return ignoreTableNames;
        }
        return new HashSet<>();
    }

    /**
     * 新增租户忽略表配置
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 结果
     */
    @Override
    public int insertTenantIgnoreTable(SysTenantIgnoreTable sysTenantIgnoreTable)
    {
        int row = tenantIgnoreTableMapper.insertTenantIgnoreTable(sysTenantIgnoreTable);
        if (row > 0)
        {
            resetIgnoreTableCache();
        }
        return row;
    }

    /**
     * 修改租户忽略表配置
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 结果
     */
    @Override
    public int updateTenantIgnoreTable(SysTenantIgnoreTable sysTenantIgnoreTable)
    {
        int row = tenantIgnoreTableMapper.updateTenantIgnoreTable(sysTenantIgnoreTable);
        if (row > 0)
        {
            resetIgnoreTableCache();
        }
        return row;
    }

    /**
     * 批量删除租户忽略表配置
     * 
     * @param ids 需要删除的主键ID
     */
    @Override
    public void deleteTenantIgnoreTableByIds(Long[] ids)
    {
        for (Long id : ids)
        {
            SysTenantIgnoreTable ignoreTable = selectTenantIgnoreTableById(id);
            if (StringUtils.isNotNull(ignoreTable))
            {
                tenantIgnoreTableMapper.deleteTenantIgnoreTableById(id);
            }
        }
        resetIgnoreTableCache();
    }

    /**
     * 校验表名是否唯一
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 结果
     */
    @Override
    public boolean checkTableNameUnique(SysTenantIgnoreTable sysTenantIgnoreTable)
    {
        Long id = StringUtils.isNull(sysTenantIgnoreTable.getId()) ? -1L : sysTenantIgnoreTable.getId();
        SysTenantIgnoreTable info = tenantIgnoreTableMapper.checkTableNameUnique(sysTenantIgnoreTable.getTableName());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 加载忽略表缓存数据
     */
    @Override
    public void loadingIgnoreTableCache()
    {
        List<String> tableNames = tenantIgnoreTableMapper.selectActiveTableNames();
        Set<String> ignoreTableNames = new HashSet<>(tableNames);
        redisCache.setCacheObject(CacheConstants.SYS_TENANT_IGNORE_TABLES_KEY, ignoreTableNames);
    }

    /**
     * 清空忽略表缓存数据
     */
    @Override
    public void clearIgnoreTableCache()
    {
        redisCache.deleteObject(CacheConstants.SYS_TENANT_IGNORE_TABLES_KEY);
    }

    /**
     * 重置忽略表缓存数据
     */
    @Override
    public void resetIgnoreTableCache()
    {
        clearIgnoreTableCache();
        loadingIgnoreTableCache();
    }
} 
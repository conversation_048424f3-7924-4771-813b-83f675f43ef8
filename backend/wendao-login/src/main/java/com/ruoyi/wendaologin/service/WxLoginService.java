package com.ruoyi.wendaologin.service;


import com.ruoyi.wendaologin.dto.LoginInfoDTO;
import com.ruoyi.wendaologin.dto.WxCheckSessionDTO;
import com.ruoyi.wendaologin.dto.WxPhoneDTO;

import java.util.Map;

public interface WxLoginService {
    /**
     * 生成小程序码
     * @param businessType 业务类型
     * @return 小程序码信息
     */
    Map<String, Object> generateWxaCode(Integer businessType);
    
    /**
     * 处理扫码登录
     * @param scene 场景值
     * @param code 微信登录code
     * @return 登录结果
     */
    Map<String, Object> handleScan(String scene, String code);
    
    /**
     * 解密手机号并处理登录
     * @param phoneDTO 手机号加密信息
     * @return 登录结果
     */
    LoginInfoDTO decryptPhone(WxPhoneDTO phoneDTO);
    
    /**
     * 检查扫码状态
     * @param scene 场景值
     * @return 扫码状态
     */
    LoginInfoDTO checkScanStatus(String scene);

    /**
     * 检查session是否有效
     * @param dto 请求参数
     * @return 是否有效
     */
    Boolean checkSession(WxCheckSessionDTO dto);
} 
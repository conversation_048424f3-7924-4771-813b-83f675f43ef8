<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysTenantMapper">
    
    <resultMap type="SysTenant" id="SysTenantResult">
        <id     property="shopId"         column="shop_id"         />
        <result property="shopName"     column="shop_name"     />
        <result property="tenantName"     column="tenant_name"     />
        <result property="tenantCode"     column="tenant_code"     />
        <result property="status"         column="status"          />
        <result property="bUserId"        column="b_user_id"       />
        <result property="trialStartTime" column="trial_start_time"/>
        <result property="trialEndTime"   column="trial_end_time"  />
        <result property="trialStatus"    column="trial_status"    />
        <result property="trialDays"      column="trial_days"      />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
    </resultMap>
    
    <sql id="selectTenantVo">
        select shop_id, shop_name, tenant_name, tenant_code, `status`, b_user_id,
               trial_start_time, trial_end_time, trial_status, trial_days,
               create_by, create_time, update_by, update_time, remark
        from sys_tenant
    </sql>
    
    <select id="selectTenantList" parameterType="SysTenant" resultMap="SysTenantResult">
        <include refid="selectTenantVo"/>
        <where>
            <if test="tenantName != null and tenantName != ''">
                AND tenant_name like concat('%', #{tenantName}, '%')
            </if>
            <if test="shopId != null and shopId != ''">
                AND shop_id = #{shopId}
            </if>
            <if test="shopName != null and shopName != ''">
                AND shop_name like concat('%', #{shopName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND `status` = #{status}
            </if>
            <if test="bUserId != null and bUserId != ''">
                AND b_user_id = #{bUserId}
            </if>
            <if test="trialStatus != null">
                AND trial_status = #{trialStatus}
            </if>
        </where>
    </select>
    
    <select id="selectTenantById" parameterType="String" resultMap="SysTenantResult">
        <include refid="selectTenantVo"/>
        where shop_id = #{shopId}
    </select>
    
    <select id="selectNormalTenantByShopId" resultMap="SysTenantResult">
        <include refid="selectTenantVo"/>
        where shop_id = #{shopId} and `status` = 0
    </select>

    <insert id="insertTenant" parameterType="SysTenant">
        insert into sys_tenant(
            <if test="shopId != null and shopId != ''">shop_id,</if>
            <if test="shopName != null and shopName != ''">shop_name,</if>
            <if test="tenantName != null and tenantName != ''">tenant_name,</if>
            <if test="tenantCode != null and tenantCode != ''">tenant_code,</if>
            <if test="status != null">`status`,</if>
            <if test="bUserId != null">b_user_id,</if>
            <if test="trialStartTime != null">trial_start_time,</if>
            <if test="trialEndTime != null">trial_end_time,</if>
            <if test="trialStatus != null">trial_status,</if>
            <if test="trialDays != null">trial_days,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            create_time
        )values(
            <if test="shopId != null and shopId != ''">#{shopId},</if>
            <if test="shopName != null and shopName != ''">#{shopName},</if>
            <if test="tenantName != null and tenantName != ''">#{tenantName},</if>
            <if test="tenantCode != null and tenantCode != ''">#{tenantCode},</if>
            <if test="status != null">#{status},</if>
            <if test="bUserId != null">#{bUserId},</if>
            <if test="trialStartTime != null">#{trialStartTime},</if>
            <if test="trialEndTime != null">#{trialEndTime},</if>
            <if test="trialStatus != null">#{trialStatus},</if>
            <if test="trialDays != null">#{trialDays},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
        )
    </insert>
    
    <update id="updateTenant" parameterType="SysTenant">
        update sys_tenant
        <set>
            <if test="tenantName != null and tenantName != ''">tenant_name = #{tenantName},</if>
            <if test="shopName != null and shopName != ''">shop_name = #{shopName},</if>
            <if test="tenantCode != null and tenantCode != ''">tenant_code = #{tenantCode},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="bUserId != null">b_user_id = #{bUserId},</if>
            <if test="trialStartTime != null">trial_start_time = #{trialStartTime},</if>
            <if test="trialEndTime != null">trial_end_time = #{trialEndTime},</if>
            <if test="trialStatus != null">trial_status = #{trialStatus},</if>
            <if test="trialDays != null">trial_days = #{trialDays},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where shop_id = #{shopId}
    </update>
    
    <delete id="deleteTenantById" parameterType="String">
        delete from sys_tenant where shop_id = #{shopId}
    </delete>
    
    <delete id="deleteTenantByIds" parameterType="String">
        delete from sys_tenant where shop_id in
        <foreach collection="array" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
    </delete>
</mapper> 
/*
 Navicat Premium Data Transfer

 Source Server         : aliyun-rds-test
 Source Server Type    : MySQL
 Source Server Version : 50743
 Source Host           : rm-cn-x0r3gorex000erbo.rwlb.rds.aliyuncs.com:3306
 Source Schema         : ruoyi-ttt

 Target Server Type    : MySQL
 Target Server Version : 50743
 File Encoding         : 65001

 Date: 23/05/2025 11:00:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table
-- ----------------------------

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint(20) NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(5) NOT NULL COMMENT '参数主键',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '10000', '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (1, '20000', '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '10000', '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (2, '20000', '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '10000', '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (3, '20000', '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '10000', '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (4, '20000', '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '10000', '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '20000', '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '10000', '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` VALUES (6, '20000', '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2024-12-31 16:03:19', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(20) NOT NULL COMMENT '部门id',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, '10000', 0, '0', '问到科技', 0, '问到', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', 'admin', '2025-01-20 14:48:38');
INSERT INTO `sys_dept` VALUES (100, '20000', 0, '0', '若依科技', 0, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (101, '10000', 100, '0,100', '杭州总公司', 1, '问到', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', 'admin', '2025-01-20 14:48:58');
INSERT INTO `sys_dept` VALUES (101, '20000', 100, '0,100', '深圳总公司', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (102, '10000', 100, '0,100', '安徽分公司', 2, 'anhuiwendan', '15888888887', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', 'admin', '2025-01-20 14:49:35');
INSERT INTO `sys_dept` VALUES (102, '20000', 100, '0,100', '长沙分公司', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (103, '10000', 101, '0,100,101', '研发部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (103, '20000', 101, '0,100,101', '研发部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (104, '10000', 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (104, '20000', 101, '0,100,101', '市场部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (105, '10000', 101, '0,100,101', '测试部门', 3, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (105, '20000', 101, '0,100,101', '测试部门', 3, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (106, '10000', 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (106, '20000', 101, '0,100,101', '财务部门', 4, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (107, '10000', 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (107, '20000', 101, '0,100,101', '运维部门', 5, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (108, '10000', 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (108, '20000', 102, '0,100,102', '市场部门', 1, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (109, '10000', 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);
INSERT INTO `sys_dept` VALUES (109, '20000', 102, '0,100,102', '财务部门', 2, '若依', '15888888888', '<EMAIL>', '0', '0', 'admin', '2024-12-31 16:03:18', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(20) NOT NULL COMMENT '字典编码',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `dict_sort` int(4) NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, '10000', 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (1, '20000', 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, '10000', 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (2, '20000', 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, '10000', 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (3, '20000', 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, '10000', 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (4, '20000', 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, '10000', 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (5, '20000', 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, '10000', 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (6, '20000', 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, '10000', 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (7, '20000', 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, '10000', 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (8, '20000', 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, '10000', 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (9, '20000', 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, '10000', 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (10, '20000', 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, '10000', 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (11, '20000', 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, '10000', 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (12, '20000', 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, '10000', 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (13, '20000', 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, '10000', 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (14, '20000', 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, '10000', 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (15, '20000', 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, '10000', 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (16, '20000', 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, '10000', 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (17, '20000', 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, '10000', 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (18, '20000', 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, '10000', 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (19, '20000', 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, '10000', 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (20, '20000', 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, '10000', 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (21, '20000', 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, '10000', 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (22, '20000', 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, '10000', 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (23, '20000', 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, '10000', 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (24, '20000', 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, '10000', 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (25, '20000', 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, '10000', 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (26, '20000', 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, '10000', 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (27, '20000', 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, '10000', 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (28, '20000', 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, '10000', 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (29, '20000', 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '停用状态');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(20) NOT NULL COMMENT '字典主键',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`, `shop_id`) USING BTREE,
  UNIQUE INDEX `uni_shop_id_dict_type`(`shop_id`, `dict_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '10000', '用户性别', 'sys_user_sex', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (1, '20000', '用户性别', 'sys_user_sex', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '10000', '菜单状态', 'sys_show_hide', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (2, '20000', '菜单状态', 'sys_show_hide', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '10000', '系统开关', 'sys_normal_disable', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (3, '20000', '系统开关', 'sys_normal_disable', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '10000', '任务状态', 'sys_job_status', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (4, '20000', '任务状态', 'sys_job_status', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '10000', '任务分组', 'sys_job_group', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (5, '20000', '任务分组', 'sys_job_group', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '10000', '系统是否', 'sys_yes_no', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (6, '20000', '系统是否', 'sys_yes_no', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '10000', '通知类型', 'sys_notice_type', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (7, '20000', '通知类型', 'sys_notice_type', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '10000', '通知状态', 'sys_notice_status', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (8, '20000', '通知状态', 'sys_notice_status', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '10000', '操作类型', 'sys_oper_type', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (9, '20000', '操作类型', 'sys_oper_type', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '10000', '系统状态', 'sys_common_status', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (10, '20000', '系统状态', 'sys_common_status', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '登录状态列表');

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint(20) NOT NULL COMMENT '任务ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `shop_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '10000', '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_job` VALUES (1, '20000', '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '10000', '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '20000', '系统默认（有参）2', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2024-12-31 16:03:19', 'admin', '2025-01-04 16:09:51', '');
INSERT INTO `sys_job` VALUES (3, '10000', '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_job` VALUES (3, '20000', '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2024-12-31 16:03:19', '', NULL, '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint(20) NOT NULL COMMENT '任务日志ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint(20) NOT NULL COMMENT '访问ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`, `shop_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (2, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 14:47:06');
INSERT INTO `sys_logininfor` VALUES (2, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 14:47:06');
INSERT INTO `sys_logininfor` VALUES (3, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 14:48:27');
INSERT INTO `sys_logininfor` VALUES (3, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 14:48:27');
INSERT INTO `sys_logininfor` VALUES (4, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 14:48:59');
INSERT INTO `sys_logininfor` VALUES (4, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 14:48:59');
INSERT INTO `sys_logininfor` VALUES (5, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:30:56');
INSERT INTO `sys_logininfor` VALUES (5, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:30:56');
INSERT INTO `sys_logininfor` VALUES (6, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:32:06');
INSERT INTO `sys_logininfor` VALUES (6, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:32:06');
INSERT INTO `sys_logininfor` VALUES (7, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:34:56');
INSERT INTO `sys_logininfor` VALUES (7, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:34:56');
INSERT INTO `sys_logininfor` VALUES (8, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:39:12');
INSERT INTO `sys_logininfor` VALUES (8, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:39:12');
INSERT INTO `sys_logininfor` VALUES (9, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:39:37');
INSERT INTO `sys_logininfor` VALUES (9, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:39:37');
INSERT INTO `sys_logininfor` VALUES (10, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:40:36');
INSERT INTO `sys_logininfor` VALUES (10, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:40:36');
INSERT INTO `sys_logininfor` VALUES (11, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:47:13');
INSERT INTO `sys_logininfor` VALUES (11, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:47:13');
INSERT INTO `sys_logininfor` VALUES (12, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:49:56');
INSERT INTO `sys_logininfor` VALUES (12, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:49:56');
INSERT INTO `sys_logininfor` VALUES (13, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 15:50:20');
INSERT INTO `sys_logininfor` VALUES (13, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 15:50:20');
INSERT INTO `sys_logininfor` VALUES (14, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:50:51');
INSERT INTO `sys_logininfor` VALUES (14, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:50:51');
INSERT INTO `sys_logininfor` VALUES (15, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 15:58:53');
INSERT INTO `sys_logininfor` VALUES (15, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 15:58:53');
INSERT INTO `sys_logininfor` VALUES (16, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:58:59');
INSERT INTO `sys_logininfor` VALUES (16, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 15:58:59');
INSERT INTO `sys_logininfor` VALUES (17, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:07:51');
INSERT INTO `sys_logininfor` VALUES (17, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:08:33');
INSERT INTO `sys_logininfor` VALUES (18, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:08:41');
INSERT INTO `sys_logininfor` VALUES (18, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:10:56');
INSERT INTO `sys_logininfor` VALUES (19, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:10:34');
INSERT INTO `sys_logininfor` VALUES (19, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:11:06');
INSERT INTO `sys_logininfor` VALUES (20, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:10:51');
INSERT INTO `sys_logininfor` VALUES (20, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:11:12');
INSERT INTO `sys_logininfor` VALUES (21, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:11:16');
INSERT INTO `sys_logininfor` VALUES (21, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:12:24');
INSERT INTO `sys_logininfor` VALUES (22, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:11:24');
INSERT INTO `sys_logininfor` VALUES (22, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:27:39');
INSERT INTO `sys_logininfor` VALUES (23, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:11:48');
INSERT INTO `sys_logininfor` VALUES (23, '20000', 'ruoyi', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-04 16:27:53');
INSERT INTO `sys_logininfor` VALUES (24, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:11:58');
INSERT INTO `sys_logininfor` VALUES (24, '20000', 'ruoyi', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-04 16:28:21');
INSERT INTO `sys_logininfor` VALUES (25, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 21:23:42');
INSERT INTO `sys_logininfor` VALUES (25, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:28:49');
INSERT INTO `sys_logininfor` VALUES (26, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 21:25:12');
INSERT INTO `sys_logininfor` VALUES (26, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:29:33');
INSERT INTO `sys_logininfor` VALUES (27, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-04 21:25:27');
INSERT INTO `sys_logininfor` VALUES (27, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-04 16:29:46');
INSERT INTO `sys_logininfor` VALUES (28, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 21:51:53');
INSERT INTO `sys_logininfor` VALUES (28, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-04 16:30:43');
INSERT INTO `sys_logininfor` VALUES (29, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-09 11:41:43');
INSERT INTO `sys_logininfor` VALUES (29, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-04 16:32:34');
INSERT INTO `sys_logininfor` VALUES (30, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-09 11:42:34');
INSERT INTO `sys_logininfor` VALUES (30, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-04 16:32:40');
INSERT INTO `sys_logininfor` VALUES (31, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-09 11:44:43');
INSERT INTO `sys_logininfor` VALUES (31, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:33:25');
INSERT INTO `sys_logininfor` VALUES (32, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-09 14:15:38');
INSERT INTO `sys_logininfor` VALUES (32, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:34:28');
INSERT INTO `sys_logininfor` VALUES (33, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-09 16:56:50');
INSERT INTO `sys_logininfor` VALUES (33, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:34:47');
INSERT INTO `sys_logininfor` VALUES (34, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-10 12:33:29');
INSERT INTO `sys_logininfor` VALUES (34, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:37:39');
INSERT INTO `sys_logininfor` VALUES (35, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-10 15:30:51');
INSERT INTO `sys_logininfor` VALUES (35, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:37:49');
INSERT INTO `sys_logininfor` VALUES (36, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-10 16:23:26');
INSERT INTO `sys_logininfor` VALUES (36, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 16:43:59');
INSERT INTO `sys_logininfor` VALUES (37, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-10 16:23:36');
INSERT INTO `sys_logininfor` VALUES (37, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 16:44:09');
INSERT INTO `sys_logininfor` VALUES (38, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-10 17:29:03');
INSERT INTO `sys_logininfor` VALUES (38, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 18:00:28');
INSERT INTO `sys_logininfor` VALUES (39, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:09:36');
INSERT INTO `sys_logininfor` VALUES (39, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 21:25:36');
INSERT INTO `sys_logininfor` VALUES (40, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:32:01');
INSERT INTO `sys_logininfor` VALUES (40, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 21:28:08');
INSERT INTO `sys_logininfor` VALUES (41, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:45:33');
INSERT INTO `sys_logininfor` VALUES (41, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 21:28:16');
INSERT INTO `sys_logininfor` VALUES (42, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:46:45');
INSERT INTO `sys_logininfor` VALUES (42, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 21:43:08');
INSERT INTO `sys_logininfor` VALUES (43, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:48:57');
INSERT INTO `sys_logininfor` VALUES (43, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-04 21:50:32');
INSERT INTO `sys_logininfor` VALUES (44, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 11:51:17');
INSERT INTO `sys_logininfor` VALUES (44, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-04 21:51:39');
INSERT INTO `sys_logininfor` VALUES (45, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:51:28');
INSERT INTO `sys_logininfor` VALUES (45, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-06 16:40:35');
INSERT INTO `sys_logininfor` VALUES (46, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 11:52:03');
INSERT INTO `sys_logininfor` VALUES (46, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-06 16:40:52');
INSERT INTO `sys_logininfor` VALUES (47, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:52:15');
INSERT INTO `sys_logininfor` VALUES (47, '20000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-06 16:41:13');
INSERT INTO `sys_logininfor` VALUES (48, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 11:52:49');
INSERT INTO `sys_logininfor` VALUES (48, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-06 16:41:34');
INSERT INTO `sys_logininfor` VALUES (49, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:53:56');
INSERT INTO `sys_logininfor` VALUES (49, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-06 16:44:25');
INSERT INTO `sys_logininfor` VALUES (50, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 12:06:01');
INSERT INTO `sys_logininfor` VALUES (50, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-09 11:41:22');
INSERT INTO `sys_logininfor` VALUES (51, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 12:17:20');
INSERT INTO `sys_logininfor` VALUES (51, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-09 11:41:32');
INSERT INTO `sys_logininfor` VALUES (52, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 14:32:04');
INSERT INTO `sys_logininfor` VALUES (52, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-09 11:42:45');
INSERT INTO `sys_logininfor` VALUES (53, '10000', 'test1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 14:32:12');
INSERT INTO `sys_logininfor` VALUES (53, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-09 11:44:32');
INSERT INTO `sys_logininfor` VALUES (54, '10000', 'test1', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 14:32:23');
INSERT INTO `sys_logininfor` VALUES (54, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 12:06:18');
INSERT INTO `sys_logininfor` VALUES (55, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 14:32:30');
INSERT INTO `sys_logininfor` VALUES (55, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 12:17:29');
INSERT INTO `sys_logininfor` VALUES (56, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 14:46:32');
INSERT INTO `sys_logininfor` VALUES (56, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 13:50:07');
INSERT INTO `sys_logininfor` VALUES (57, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 14:46:41');
INSERT INTO `sys_logininfor` VALUES (57, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 13:50:21');
INSERT INTO `sys_logininfor` VALUES (58, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-20 15:18:04');
INSERT INTO `sys_logininfor` VALUES (58, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 15:17:50');
INSERT INTO `sys_logininfor` VALUES (59, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:18:24');
INSERT INTO `sys_logininfor` VALUES (59, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-05-19 11:01:53');
INSERT INTO `sys_logininfor` VALUES (60, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 15:19:20');
INSERT INTO `sys_logininfor` VALUES (60, '20000', 'ry', '127.0.0.1', '内网IP', 'Safari', 'Mac OS X', '1', '用户不存在/密码错误', '2025-05-19 11:03:22');
INSERT INTO `sys_logininfor` VALUES (61, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:19:41');
INSERT INTO `sys_logininfor` VALUES (61, '20000', 'ry', '127.0.0.1', '内网IP', 'Safari', 'Mac OS X', '1', '用户不存在/密码错误', '2025-05-19 11:03:30');
INSERT INTO `sys_logininfor` VALUES (62, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:21:47');
INSERT INTO `sys_logininfor` VALUES (62, '20000', 'ry', '127.0.0.1', '内网IP', 'Safari', 'Mac OS X', '0', '登录成功', '2025-05-19 11:03:35');
INSERT INTO `sys_logininfor` VALUES (63, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:23:54');
INSERT INTO `sys_logininfor` VALUES (63, '20000', 'ry', '127.0.0.1', '内网IP', 'Safari', 'Mac OS X', '0', '登录成功', '2025-05-19 11:03:57');
INSERT INTO `sys_logininfor` VALUES (64, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 15:26:21');
INSERT INTO `sys_logininfor` VALUES (64, '20000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-05-19 15:11:41');
INSERT INTO `sys_logininfor` VALUES (65, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:26:27');
INSERT INTO `sys_logininfor` VALUES (66, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 15:28:37');
INSERT INTO `sys_logininfor` VALUES (67, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:28:41');
INSERT INTO `sys_logininfor` VALUES (68, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 15:28:46');
INSERT INTO `sys_logininfor` VALUES (69, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:39:47');
INSERT INTO `sys_logininfor` VALUES (70, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 15:42:20');
INSERT INTO `sys_logininfor` VALUES (71, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:42:21');
INSERT INTO `sys_logininfor` VALUES (72, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 15:42:35');
INSERT INTO `sys_logininfor` VALUES (73, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:42:43');
INSERT INTO `sys_logininfor` VALUES (74, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:49:32');
INSERT INTO `sys_logininfor` VALUES (75, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 16:04:28');
INSERT INTO `sys_logininfor` VALUES (76, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 16:04:31');
INSERT INTO `sys_logininfor` VALUES (77, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-20 16:23:14');
INSERT INTO `sys_logininfor` VALUES (78, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 16:23:21');
INSERT INTO `sys_logininfor` VALUES (79, '10000', 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Android 4.x', '0', '登录成功', '2025-01-20 16:24:48');
INSERT INTO `sys_logininfor` VALUES (80, '10000', 'admin', '127.0.0.1', '内网IP', 'Mobile Safari', 'Android 4.x', '0', '登录成功', '2025-01-20 16:31:11');
INSERT INTO `sys_logininfor` VALUES (81, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 16:31:26');
INSERT INTO `sys_logininfor` VALUES (82, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-21 09:35:17');
INSERT INTO `sys_logininfor` VALUES (83, '10000', 'ry', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-21 15:51:49');
INSERT INTO `sys_logininfor` VALUES (84, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-23 14:57:30');
INSERT INTO `sys_logininfor` VALUES (85, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '退出成功', '2025-01-23 14:57:38');
INSERT INTO `sys_logininfor` VALUES (86, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '登录成功', '2025-05-19 11:01:19');
INSERT INTO `sys_logininfor` VALUES (87, '10000', 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Mac OS X', '0', '退出成功', '2025-05-19 11:01:50');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int(1) NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '10000', '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2024-12-31 16:03:19', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (1, '20000', '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2024-12-31 16:03:19', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '10000', '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2024-12-31 16:03:19', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (2, '20000', '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2024-12-31 16:03:19', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '20000', '系统工具', 0, 3, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2024-12-31 16:03:19', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` VALUES (100, '10000', '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2024-12-31 16:03:19', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (100, '20000', '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2024-12-31 16:03:19', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '10000', '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2024-12-31 16:03:19', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (101, '20000', '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2024-12-31 16:03:19', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '10000', '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2024-12-31 16:03:19', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (102, '20000', '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2024-12-31 16:03:19', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '10000', '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2024-12-31 16:03:19', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` VALUES (103, '20000', '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2024-12-31 16:03:19', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '10000', '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2024-12-31 16:03:19', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (104, '20000', '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2024-12-31 16:03:19', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '10000', '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2024-12-31 16:03:19', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (105, '20000', '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2024-12-31 16:03:19', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '20000', '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2024-12-31 16:03:19', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '10000', '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2024-12-31 16:03:19', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (107, '20000', '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2024-12-31 16:03:19', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '10000', '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2024-12-31 16:03:19', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (108, '20000', '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2024-12-31 16:03:19', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '10000', '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2024-12-31 16:03:19', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (109, '20000', '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2024-12-31 16:03:19', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '20000', '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2024-12-31 16:03:19', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, '20000', '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2024-12-31 16:03:19', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` VALUES (112, '20000', '服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2024-12-31 16:03:19', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` VALUES (113, '20000', '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2024-12-31 16:03:19', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` VALUES (114, '20000', '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2024-12-31 16:03:19', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` VALUES (115, '20000', '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2024-12-31 16:03:19', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (116, '20000', '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2024-12-31 16:03:19', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` VALUES (117, '20000', '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2024-12-31 16:03:19', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '10000', '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2024-12-31 16:03:19', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (500, '20000', '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2024-12-31 16:03:19', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '10000', '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2024-12-31 16:03:19', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (501, '20000', '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2024-12-31 16:03:19', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '10000', '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1000, '20000', '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '10000', '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '20000', '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '10000', '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '20000', '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '10000', '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '20000', '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '10000', '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '20000', '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '10000', '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '20000', '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '10000', '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '20000', '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '10000', '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '20000', '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '10000', '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '20000', '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '10000', '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '20000', '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '10000', '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '20000', '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '10000', '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '20000', '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '10000', '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '20000', '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '10000', '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '20000', '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '10000', '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '20000', '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '10000', '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '20000', '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '10000', '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '20000', '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '10000', '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '20000', '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '10000', '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '20000', '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '10000', '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '20000', '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '10000', '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '20000', '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '10000', '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '20000', '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '10000', '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '20000', '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '10000', '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '20000', '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '10000', '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '20000', '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '10000', '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '20000', '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '10000', '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '20000', '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '10000', '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '20000', '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '10000', '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '20000', '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '10000', '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '20000', '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '20000', '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '20000', '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '20000', '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '20000', '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '20000', '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '10000', '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '20000', '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '10000', '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '20000', '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '10000', '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '20000', '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '10000', '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '20000', '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '10000', '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '20000', '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '10000', '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '20000', '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '10000', '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '20000', '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '10000', '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '20000', '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '10000', '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '20000', '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '10000', '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '20000', '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '10000', '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '20000', '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '10000', '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '20000', '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '10000', '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '20000', '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '10000', '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '20000', '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '20000', '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '20000', '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '20000', '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '20000', '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '20000', '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '20000', '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '20000', '生成查询', 116, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '20000', '生成修改', 116, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '20000', '生成删除', 116, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '20000', '导入代码', 116, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '20000', '预览代码', 116, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '20000', '生成代码', 116, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2024-12-31 16:03:19', '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int(4) NOT NULL COMMENT '公告ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '10000', '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2024-12-31 16:03:19', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (1, '20000', '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2024-12-31 16:03:19', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '10000', '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2024-12-31 16:03:19', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '20000', '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2024-12-31 16:03:19', '', NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(20) NOT NULL COMMENT '日志主键',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int(2) NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int(1) NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int(1) NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(20) NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`, `shop_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type`) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status`) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (1, '10000', '通知公告', 1, 'com.ruoyi.web.controller.system.SysNoticeController.add()', 'POST', 1, 'admin', '研发部门', '/system/notice', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"noticeContent\":\"<p>这是一个测试公共这是一个测试公共这是一个测试公共这是一个测试公共</p>\",\"noticeId\":3,\"noticeTitle\":\"这是一个测试公告\",\"noticeType\":\"1\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 15:56:25', 71);
INSERT INTO `sys_oper_log` VALUES (1, '20000', '通知公告', 1, 'com.ruoyi.web.controller.system.SysNoticeController.add()', 'POST', 1, 'admin', '研发部门', '/system/notice', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"noticeContent\":\"<p>这是一个测试公共这是一个测试公共这是一个测试公共这是一个测试公共</p>\",\"noticeId\":3,\"noticeTitle\":\"这是一个测试公告\",\"noticeType\":\"1\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 15:56:25', 71);
INSERT INTO `sys_oper_log` VALUES (2, '10000', '通知公告', 3, 'com.ruoyi.web.controller.system.SysNoticeController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/notice/3', '127.0.0.1', '内网IP', '[3]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 15:56:53', 32);
INSERT INTO `sys_oper_log` VALUES (2, '20000', '通知公告', 3, 'com.ruoyi.web.controller.system.SysNoticeController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/notice/3', '127.0.0.1', '内网IP', '[3]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 15:56:53', 32);
INSERT INTO `sys_oper_log` VALUES (3, '10000', '通知公告', 1, 'com.ruoyi.web.controller.system.SysNoticeController.add()', 'POST', 1, 'admin', '研发部门', '/system/notice', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"noticeContent\":\"<p>二二</p>\",\"noticeId\":4,\"noticeTitle\":\"122441\",\"noticeType\":\"1\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 15:57:34', 29);
INSERT INTO `sys_oper_log` VALUES (3, '20000', '通知公告', 1, 'com.ruoyi.web.controller.system.SysNoticeController.add()', 'POST', 1, 'admin', '研发部门', '/system/notice', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"noticeContent\":\"<p>二二</p>\",\"noticeId\":4,\"noticeTitle\":\"122441\",\"noticeType\":\"1\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 15:57:34', 29);
INSERT INTO `sys_oper_log` VALUES (4, '10000', '通知公告', 2, 'com.ruoyi.web.controller.system.SysNoticeController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/notice', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2025-01-04 15:57:34\",\"noticeContent\":\"<p>二二</p>\",\"noticeId\":4,\"noticeTitle\":\"12244125566\",\"noticeType\":\"1\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 16:09:16', 20);
INSERT INTO `sys_oper_log` VALUES (4, '20000', '通知公告', 3, 'com.ruoyi.web.controller.system.SysNoticeController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/notice/4', '127.0.0.1', '内网IP', '[4]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 16:09:00', 20);
INSERT INTO `sys_oper_log` VALUES (5, '10000', '个人信息', 2, 'com.ruoyi.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 16:10:29', 204);
INSERT INTO `sys_oper_log` VALUES (5, '20000', '定时任务', 2, 'com.ruoyi.quartz.controller.SysJobController.edit()', 'PUT', 1, 'admin', '研发部门', '/monitor/job', '127.0.0.1', '内网IP', '{\"concurrent\":\"1\",\"createBy\":\"admin\",\"createTime\":\"2024-12-31 16:03:19\",\"cronExpression\":\"0/15 * * * * ?\",\"invokeTarget\":\"ryTask.ryParams(\'ry\')\",\"jobGroup\":\"DEFAULT\",\"jobId\":2,\"jobName\":\"系统默认（有参）2\",\"misfirePolicy\":\"3\",\"nextValidTime\":\"2025-01-04 16:10:00\",\"params\":{},\"remark\":\"\",\"status\":\"1\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 16:09:51', 62);
INSERT INTO `sys_oper_log` VALUES (6, '10000', '个人信息', 2, 'com.ruoyi.web.controller.system.SysProfileController.updatePwd()', 'PUT', 1, 'admin', '研发部门', '/system/user/profile/updatePwd', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 16:11:45', 207);
INSERT INTO `sys_oper_log` VALUES (6, '20000', '用户管理', 2, 'com.ruoyi.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', '研发部门', '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateBy\":\"admin\",\"userId\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 16:29:28', 79);
INSERT INTO `sys_oper_log` VALUES (7, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-01-09 11:44:59', 27);
INSERT INTO `sys_oper_log` VALUES (7, '20000', '用户管理', 2, 'com.ruoyi.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', '研发部门', '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateBy\":\"admin\",\"userId\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-04 16:33:44', 83);
INSERT INTO `sys_oper_log` VALUES (8, '10000', '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-31 16:03:19\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-09 11:45:36', 137);
INSERT INTO `sys_oper_log` VALUES (8, '20000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-01-09 11:43:19', 23);
INSERT INTO `sys_oper_log` VALUES (9, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-09 11:45:53', 52);
INSERT INTO `sys_oper_log` VALUES (9, '20000', '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-31 16:03:19\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-09 11:43:36', 102);
INSERT INTO `sys_oper_log` VALUES (10, '10000', '通知公告', 3, 'com.ruoyi.web.controller.system.SysNoticeController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/notice/4', '127.0.0.1', '内网IP', '[4]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-09 11:46:19', 33);
INSERT INTO `sys_oper_log` VALUES (10, '20000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/4', '127.0.0.1', '内网IP', '4', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-09 11:44:08', 31);
INSERT INTO `sys_oper_log` VALUES (11, '10000', '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"wendao_shop_info,wendao_wx_qrcode_record,wendao_sys_login_log,wendao_sms_log,wendao_sys_user\"}', NULL, 1, 'nested exception is org.apache.ibatis.exceptions.PersistenceException: \r\n### Error querying database.  Cause: java.lang.reflect.UndeclaredThrowableException\r\n### The error may exist in file [D:\\wendao-new-java\\RuoYi-Vue-v3.8.9\\backend\\ruoyi-generator\\target\\classes\\mapper\\generator\\GenTableMapper.xml]\r\n### The error may involve com.ruoyi.generator.mapper.GenTableMapper.selectDbTableListByNames\r\n### The error occurred while executing a query\r\n### Cause: java.lang.reflect.UndeclaredThrowableException', '2025-01-09 17:19:32', 15);
INSERT INTO `sys_oper_log` VALUES (11, '20000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/2306e431-2ab0-41f9-801b-d9798da87267', '127.0.0.1', '内网IP', '\"2306e431-2ab0-41f9-801b-d9798da87267\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 12:06:37', 5);
INSERT INTO `sys_oper_log` VALUES (12, '10000', '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"wendao_shop_info,wendao_wx_qrcode_record,wendao_sys_login_log,wendao_sms_log,wendao_sys_user\"}', NULL, 1, 'nested exception is org.apache.ibatis.exceptions.PersistenceException: \r\n### Error querying database.  Cause: java.lang.reflect.UndeclaredThrowableException\r\n### The error may exist in file [D:\\wendao-new-java\\RuoYi-Vue-v3.8.9\\backend\\ruoyi-generator\\target\\classes\\mapper\\generator\\GenTableMapper.xml]\r\n### The error may involve com.ruoyi.generator.mapper.GenTableMapper.selectDbTableListByNames\r\n### The error occurred while executing a query\r\n### Cause: java.lang.reflect.UndeclaredThrowableException', '2025-01-09 17:26:36', 62);
INSERT INTO `sys_oper_log` VALUES (12, '20000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/c70e0491-7402-4275-be1c-6600ea97e827', '127.0.0.1', '内网IP', '\"c70e0491-7402-4275-be1c-6600ea97e827\"', '{\"msg\":\"不能自己踢自己\",\"code\":500}', 0, NULL, '2025-01-20 12:17:47', 1);
INSERT INTO `sys_oper_log` VALUES (13, '10000', '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"wendao_shop_info,wendao_wx_qrcode_record,wendao_sys_login_log,wendao_sms_log,wendao_sys_user\"}', NULL, 1, '导入失败：nested exception is org.apache.ibatis.exceptions.PersistenceException: \r\n### Error updating database.  Cause: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [SELECT COALESCE(MAX(id), 0) FROM gen_table WHERE tenant_id = ?]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'id\' in \'field list\'\r\n### Cause: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [SELECT COALESCE(MAX(id), 0) FROM gen_table WHERE tenant_id = ?]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'id\' in \'field list\'', '2025-01-09 17:33:48', 158);
INSERT INTO `sys_oper_log` VALUES (13, '20000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/c70e0491-7402-4275-be1c-6600ea97e827', '127.0.0.1', '内网IP', '\"c70e0491-7402-4275-be1c-6600ea97e827\"', '{\"msg\":\"不能自己踢自己\",\"code\":500}', 0, NULL, '2025-01-20 12:17:55', 1);
INSERT INTO `sys_oper_log` VALUES (14, '10000', '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"wendao_shop_info,wendao_wx_qrcode_record,wendao_sys_login_log,wendao_sms_log,wendao_sys_user\"}', NULL, 1, '导入失败：nested exception is org.apache.ibatis.exceptions.PersistenceException: \r\n### Error updating database.  Cause: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [SELECT COALESCE(MAX(id), 0) FROM gen_table WHERE tenant_id = ?]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'id\' in \'field list\'\r\n### Cause: org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [SELECT COALESCE(MAX(id), 0) FROM gen_table WHERE tenant_id = ?]; nested exception is java.sql.SQLSyntaxErrorException: Unknown column \'id\' in \'field list\'', '2025-01-09 17:40:14', 4680);
INSERT INTO `sys_oper_log` VALUES (14, '20000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/c70e0491-7402-4275-be1c-6600ea97e827', '127.0.0.1', '内网IP', '\"c70e0491-7402-4275-be1c-6600ea97e827\"', '{\"msg\":\"不能自己踢自己\",\"code\":500}', 0, NULL, '2025-01-20 13:36:43', 9);
INSERT INTO `sys_oper_log` VALUES (15, '10000', '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"wendao_shop_info,wendao_wx_qrcode_record,wendao_sys_login_log,wendao_sms_log,wendao_sys_user\"}', NULL, 1, '导入失败：nested exception is org.apache.ibatis.exceptions.PersistenceException: \r\n### Error querying database.  Cause: java.lang.reflect.UndeclaredThrowableException\r\n### The error may exist in file [D:\\wendao-new-java\\RuoYi-Vue-v3.8.9\\backend\\ruoyi-generator\\target\\classes\\mapper\\generator\\GenTableColumnMapper.xml]\r\n### The error may involve com.ruoyi.generator.mapper.GenTableColumnMapper.selectDbTableColumnsByName\r\n### The error occurred while executing a query\r\n### Cause: java.lang.reflect.UndeclaredThrowableException', '2025-01-09 17:51:53', 174);
INSERT INTO `sys_oper_log` VALUES (15, '20000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/a9792000-e5fb-4347-a3f1-135412dab4e3', '127.0.0.1', '内网IP', '\"a9792000-e5fb-4347-a3f1-135412dab4e3\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-19 11:03:48', 9);
INSERT INTO `sys_oper_log` VALUES (16, '10000', '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"wendao_shop_info,wendao_wx_qrcode_record,wendao_sys_login_log,wendao_sms_log,wendao_sys_user\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-09 17:54:43', 1274);
INSERT INTO `sys_oper_log` VALUES (17, '10000', '代码生成', 3, 'com.ruoyi.generator.controller.GenController.remove()', 'DELETE', 1, 'admin', '研发部门', '/tool/gen/2,3,4,5,6', '127.0.0.1', '内网IP', '[2,3,4,5,6]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-09 17:57:54', 76);
INSERT INTO `sys_oper_log` VALUES (18, '10000', '代码生成', 6, 'com.ruoyi.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', '研发部门', '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"wendao_shop_info,wendao_wx_qrcode_record,wendao_sys_login_log,wendao_sms_log,wendao_sys_user\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-09 17:58:07', 1130);
INSERT INTO `sys_oper_log` VALUES (19, '10000', '代码生成', 8, 'com.ruoyi.generator.controller.GenController.batchGenCode()', 'GET', 1, 'admin', '研发部门', '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"wendao_shop_info,wendao_sms_log,wendao_sys_login_log,wendao_sys_user,wendao_wx_qrcode_record\"}', NULL, 0, NULL, '2025-01-09 17:58:37', 167);
INSERT INTO `sys_oper_log` VALUES (20, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/114', '127.0.0.1', '内网IP', '114', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2025-01-20 11:18:56', 31);
INSERT INTO `sys_oper_log` VALUES (21, '10000', '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-31 16:03:19\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,3,115,116,1055,1056,1057,1058,1059,1060,117],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:19:26', 415);
INSERT INTO `sys_oper_log` VALUES (22, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/114', '127.0.0.1', '内网IP', '114', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:20:00', 35);
INSERT INTO `sys_oper_log` VALUES (23, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/113', '127.0.0.1', '内网IP', '113', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:20:03', 31);
INSERT INTO `sys_oper_log` VALUES (24, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/112', '127.0.0.1', '内网IP', '112', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:20:06', 29);
INSERT INTO `sys_oper_log` VALUES (25, '10000', '代码生成', 3, 'com.ruoyi.generator.controller.GenController.remove()', 'DELETE', 1, 'admin', '研发部门', '/tool/gen/7,8,9,10,11', '127.0.0.1', '内网IP', '[7,8,9,10,11]', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:20:36', 42);
INSERT INTO `sys_oper_log` VALUES (26, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/a8eb7a26-efc8-43af-8464-7137a0e151e9', '127.0.0.1', '内网IP', '\"a8eb7a26-efc8-43af-8464-7137a0e151e9\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:31:52', 9);
INSERT INTO `sys_oper_log` VALUES (27, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/91be14f6-cb4c-4cad-b0d8-07e16e3e2de8', '127.0.0.1', '内网IP', '\"91be14f6-cb4c-4cad-b0d8-07e16e3e2de8\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:46:20', 4);
INSERT INTO `sys_oper_log` VALUES (28, '10000', '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-31 16:03:19\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,3,115,116,1055,1056,1057,1058,1059,1060,117],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:47:38', 130);
INSERT INTO `sys_oper_log` VALUES (29, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/111', '127.0.0.1', '内网IP', '111', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 11:47:53', 427);
INSERT INTO `sys_oper_log` VALUES (30, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/1754500a-fb90-44b6-9a65-8473ee643ec4', '127.0.0.1', '内网IP', '\"1754500a-fb90-44b6-9a65-8473ee643ec4\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 12:06:44', 1);
INSERT INTO `sys_oper_log` VALUES (31, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/5d983671-4b05-410f-b318-8a88fe66cdcf', '127.0.0.1', '内网IP', '\"5d983671-4b05-410f-b318-8a88fe66cdcf\"', '{\"msg\":\"不能自己踢自己\",\"code\":500}', 0, NULL, '2025-01-20 12:17:37', 4);
INSERT INTO `sys_oper_log` VALUES (32, '10000', '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-31 16:03:19\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[3,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,115,116,1055,1056,1057,1058,1059,1060],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 13:47:45', 188);
INSERT INTO `sys_oper_log` VALUES (33, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/117', '127.0.0.1', '内网IP', '117', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 13:47:53', 56);
INSERT INTO `sys_oper_log` VALUES (34, '10000', '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-31 16:03:19\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,109,1046,1047,1048,3,115,116,1055,1056,1057,1058,1059,1060],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:04:56', 170);
INSERT INTO `sys_oper_log` VALUES (35, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/110', '127.0.0.1', '内网IP', '110', '{\"msg\":\"存在子菜单,不允许删除\",\"code\":601}', 0, NULL, '2025-01-20 14:05:11', 17);
INSERT INTO `sys_oper_log` VALUES (36, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1054', '127.0.0.1', '内网IP', '1054', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:05:24', 54);
INSERT INTO `sys_oper_log` VALUES (37, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1053', '127.0.0.1', '内网IP', '1053', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:05:27', 497);
INSERT INTO `sys_oper_log` VALUES (38, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1052', '127.0.0.1', '内网IP', '1052', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:05:30', 52);
INSERT INTO `sys_oper_log` VALUES (39, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1051', '127.0.0.1', '内网IP', '1051', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:05:33', 59);
INSERT INTO `sys_oper_log` VALUES (40, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1050', '127.0.0.1', '内网IP', '1050', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:05:35', 278);
INSERT INTO `sys_oper_log` VALUES (41, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1049', '127.0.0.1', '内网IP', '1049', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:05:37', 59);
INSERT INTO `sys_oper_log` VALUES (42, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/110', '127.0.0.1', '内网IP', '110', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:05:41', 58);
INSERT INTO `sys_oper_log` VALUES (43, '10000', '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-31 16:03:19\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:09', 118);
INSERT INTO `sys_oper_log` VALUES (44, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1060', '127.0.0.1', '内网IP', '1060', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:21', 33);
INSERT INTO `sys_oper_log` VALUES (45, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1059', '127.0.0.1', '内网IP', '1059', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:24', 38);
INSERT INTO `sys_oper_log` VALUES (46, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1058', '127.0.0.1', '内网IP', '1058', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:26', 28);
INSERT INTO `sys_oper_log` VALUES (47, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1057', '127.0.0.1', '内网IP', '1057', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:28', 32);
INSERT INTO `sys_oper_log` VALUES (48, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1056', '127.0.0.1', '内网IP', '1056', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:30', 32);
INSERT INTO `sys_oper_log` VALUES (49, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1055', '127.0.0.1', '内网IP', '1055', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:33', 36);
INSERT INTO `sys_oper_log` VALUES (50, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/116', '127.0.0.1', '内网IP', '116', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:35', 72);
INSERT INTO `sys_oper_log` VALUES (51, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/115', '127.0.0.1', '内网IP', '115', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:38', 37);
INSERT INTO `sys_oper_log` VALUES (52, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/3', '127.0.0.1', '内网IP', '3', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:16:41', 27);
INSERT INTO `sys_oper_log` VALUES (53, '10000', '用户管理', 1, 'com.ruoyi.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', '研发部门', '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createBy\":\"admin\",\"deptId\":100,\"nickName\":\"鬼地方个\",\"params\":{},\"phonenumber\":\"15845585475\",\"postIds\":[3],\"remark\":\"多个风格\",\"roleIds\":[2],\"sex\":\"0\",\"status\":\"0\",\"userId\":3,\"userName\":\"test1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:31:51', 194);
INSERT INTO `sys_oper_log` VALUES (54, '10000', '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2024-12-31 16:03:19\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:33:26', 138);
INSERT INTO `sys_oper_log` VALUES (55, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1034', '127.0.0.1', '内网IP', '1034', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:33:41', 49);
INSERT INTO `sys_oper_log` VALUES (56, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1033', '127.0.0.1', '内网IP', '1033', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:33:45', 62);
INSERT INTO `sys_oper_log` VALUES (57, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1032', '127.0.0.1', '内网IP', '1032', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:33:48', 50);
INSERT INTO `sys_oper_log` VALUES (58, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1031', '127.0.0.1', '内网IP', '1031', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:33:51', 51);
INSERT INTO `sys_oper_log` VALUES (59, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/1030', '127.0.0.1', '内网IP', '1030', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:33:55', 49);
INSERT INTO `sys_oper_log` VALUES (60, '10000', '菜单管理', 3, 'com.ruoyi.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', '研发部门', '/system/menu/106', '127.0.0.1', '内网IP', '106', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:33:59', 50);
INSERT INTO `sys_oper_log` VALUES (61, '10000', '部门管理', 2, 'com.ruoyi.web.controller.system.SysDeptController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0\",\"children\":[],\"deptId\":100,\"deptName\":\"问到科技\",\"email\":\"<EMAIL>\",\"leader\":\"问到\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:48:38', 63);
INSERT INTO `sys_oper_log` VALUES (62, '10000', '部门管理', 2, 'com.ruoyi.web.controller.system.SysDeptController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"deptId\":101,\"deptName\":\"杭州总公司\",\"email\":\"<EMAIL>\",\"leader\":\"问到\",\"orderNum\":1,\"params\":{},\"parentId\":100,\"parentName\":\"问到科技\",\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:48:58', 115);
INSERT INTO `sys_oper_log` VALUES (63, '10000', '部门管理', 2, 'com.ruoyi.web.controller.system.SysDeptController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"deptId\":102,\"deptName\":\"安徽分公司\",\"email\":\"<EMAIL>\",\"leader\":\"anhuiwendan\",\"orderNum\":2,\"params\":{},\"parentId\":100,\"parentName\":\"问到科技\",\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:49:27', 105);
INSERT INTO `sys_oper_log` VALUES (64, '10000', '部门管理', 2, 'com.ruoyi.web.controller.system.SysDeptController.edit()', 'PUT', 1, 'admin', '研发部门', '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"deptId\":102,\"deptName\":\"安徽分公司\",\"email\":\"<EMAIL>\",\"leader\":\"anhuiwendan\",\"orderNum\":2,\"params\":{},\"parentId\":100,\"parentName\":\"问到科技\",\"phone\":\"15888888887\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 14:49:35', 103);
INSERT INTO `sys_oper_log` VALUES (65, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/2828270f-f95a-457c-a331-c617773942a8', '127.0.0.1', '内网IP', '\"2828270f-f95a-457c-a331-c617773942a8\"', '{\"msg\":\"不能自己踢自己\",\"code\":500}', 0, NULL, '2025-01-20 15:01:26', 0);
INSERT INTO `sys_oper_log` VALUES (66, '10000', '用户管理', 2, 'com.ruoyi.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', '研发部门', '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateBy\":\"admin\",\"userId\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 15:18:16', 119);
INSERT INTO `sys_oper_log` VALUES (67, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/2562b459-eec4-4b01-ae5d-63501a823cc3', '127.0.0.1', '内网IP', '\"2562b459-eec4-4b01-ae5d-63501a823cc3\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 15:19:52', 2);
INSERT INTO `sys_oper_log` VALUES (68, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'ry', '测试部门', '/monitor/online/2828270f-f95a-457c-a331-c617773942a8', '127.0.0.1', '内网IP', '\"2828270f-f95a-457c-a331-c617773942a8\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 15:22:10', 2);
INSERT INTO `sys_oper_log` VALUES (69, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/03885739-2bdb-4d92-aaf6-d7c9079148ec', '127.0.0.1', '内网IP', '\"03885739-2bdb-4d92-aaf6-d7c9079148ec\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 15:43:01', 5);
INSERT INTO `sys_oper_log` VALUES (70, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/072101b0-bd06-4d20-a820-045717118c15', '127.0.0.1', '内网IP', '\"072101b0-bd06-4d20-a820-045717118c15\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 16:26:33', 4);
INSERT INTO `sys_oper_log` VALUES (71, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/dfc4c55e-7dee-4ab6-86b8-4f3af440d1bd', '127.0.0.1', '内网IP', '\"dfc4c55e-7dee-4ab6-86b8-4f3af440d1bd\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 16:26:43', 5);
INSERT INTO `sys_oper_log` VALUES (72, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/6f3af2ce-c54d-490f-b82a-91700429c552', '127.0.0.1', '内网IP', '\"6f3af2ce-c54d-490f-b82a-91700429c552\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 16:31:19', 1);
INSERT INTO `sys_oper_log` VALUES (73, '10000', '在线用户', 7, 'com.ruoyi.web.controller.monitor.SysUserOnlineController.forceLogout()', 'DELETE', 1, 'admin', '研发部门', '/monitor/online/cba2d84b-a2a0-4d02-9648-10545b7876fc', '127.0.0.1', '内网IP', '\"cba2d84b-a2a0-4d02-9648-10545b7876fc\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:34:17', 17);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, '10000', 'ceo', '董事长', 1, '0', 'admin', '2024-12-31 16:03:18', '', NULL, '');
INSERT INTO `sys_post` VALUES (1, '20000', 'ceo', '董事长', 1, '0', 'admin', '2024-12-31 16:03:18', '', NULL, '');
INSERT INTO `sys_post` VALUES (2, '10000', 'se', '项目经理', 2, '0', 'admin', '2024-12-31 16:03:18', '', NULL, '');
INSERT INTO `sys_post` VALUES (2, '20000', 'se', '项目经理', 2, '0', 'admin', '2024-12-31 16:03:18', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, '10000', 'hr', '人力资源', 3, '0', 'admin', '2024-12-31 16:03:18', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, '20000', 'hr', '人力资源', 3, '0', 'admin', '2024-12-31 16:03:18', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, '10000', 'user', '普通员工', 4, '0', 'admin', '2024-12-31 16:03:18', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, '20000', 'user', '普通员工', 4, '0', 'admin', '2024-12-31 16:03:18', '', NULL, '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '10000', '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (1, '20000', '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2024-12-31 16:03:19', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '10000', '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2024-12-31 16:03:19', 'admin', '2025-01-20 14:33:26', '普通角色');
INSERT INTO `sys_role` VALUES (2, '20000', '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2024-12-31 16:03:19', 'admin', '2025-01-09 11:43:36', '普通角色');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (2, '10000', 100);
INSERT INTO `sys_role_dept` VALUES (2, '20000', 100);
INSERT INTO `sys_role_dept` VALUES (2, '10000', 101);
INSERT INTO `sys_role_dept` VALUES (2, '20000', 101);
INSERT INTO `sys_role_dept` VALUES (2, '10000', 105);
INSERT INTO `sys_role_dept` VALUES (2, '20000', 105);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  PRIMARY KEY (`role_id`, `menu_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (2, 1, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 2, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 2, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 3, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 100, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 100, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 101, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 101, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 102, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 102, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 103, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 103, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 104, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 104, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 105, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 105, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 106, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 107, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 107, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 108, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 108, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 109, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 109, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 110, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 111, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 112, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 113, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 114, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 115, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 116, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 117, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 500, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 500, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 501, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 501, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1000, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1000, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1001, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1001, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1002, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1002, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1003, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1003, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1004, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1004, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1005, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1005, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1006, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1006, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1007, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1007, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1008, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1008, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1009, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1009, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1010, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1010, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1011, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1011, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1012, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1012, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1013, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1013, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1014, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1014, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1015, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1015, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1016, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1016, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1017, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1017, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1018, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1018, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1019, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1019, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1020, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1020, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1021, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1021, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1022, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1022, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1023, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1023, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1024, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1024, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1025, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1025, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1026, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1026, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1027, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1027, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1028, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1028, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1029, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1029, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1030, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1031, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1032, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1033, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1034, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1035, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1035, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1036, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1036, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1037, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1037, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1038, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1038, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1039, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1039, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1040, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1040, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1041, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1041, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1042, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1042, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1043, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1043, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1044, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1044, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1045, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1045, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1046, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1046, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1047, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1047, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1048, '10000');
INSERT INTO `sys_role_menu` VALUES (2, 1048, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1049, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1050, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1051, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1052, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1053, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1054, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1055, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1056, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1057, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1058, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1059, '20000');
INSERT INTO `sys_role_menu` VALUES (2, 1060, '20000');

-- ----------------------------
-- Table structure for sys_tenant
-- ----------------------------
DROP TABLE IF EXISTS `sys_tenant`;
CREATE TABLE `sys_tenant`  (
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `tenant_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户名称',
  `tenant_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户编码',
  `status` tinyint(4) NULL DEFAULT 0 COMMENT '状态（0正常 1停用）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `b_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问到用户id',
  PRIMARY KEY (`shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_tenant
-- ----------------------------
INSERT INTO `sys_tenant` VALUES ('10000', '问到科技', 'wendao', 0, '2025-01-03 22:23:02', 'admin', '2025-01-03 22:23:10', 'admin', '官方系统', NULL);
INSERT INTO `sys_tenant` VALUES ('20000', '成美科技', 'chengmei', 0, '2025-01-03 22:23:02', 'admin', '2025-01-03 22:23:10', 'admin', '官方系统2', NULL);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, '10000', 103, 'admin', '问到管理员', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$0v9SLo.qhzEMpenYkW9iP.8GSBzIYq4K1h9iMPefvv3zDFhaD1X9m', '0', '0', '127.0.0.1', '2025-05-19 11:01:19', 'admin', '2024-12-31 16:03:18', '', '2025-05-19 11:01:19', '管理员');
INSERT INTO `sys_user` VALUES (1, '20000', 103, 'admin', '问到管理员1', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2025-05-19 15:11:42', 'admin', '2024-12-31 16:03:18', '', '2025-05-19 15:11:41', '管理员');
INSERT INTO `sys_user` VALUES (2, '10000', 105, 'ry', '测试', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$7wu2wZdy.tBwycz7qo78GuMyIX8jCbJVcMys4ujC3BEWCeFePE52.', '0', '0', '127.0.0.1', '2025-01-21 09:35:19', 'admin', '2024-12-31 16:03:18', 'admin', '2025-01-21 09:35:17', '测试员');
INSERT INTO `sys_user` VALUES (2, '20000', 105, 'ry', '测试1', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$Em8stuVVaQC5DGOSqhSiDuwd/9T2NfOvCY5/zao.DOFf810mj3ErG', '0', '0', '127.0.0.1', '2025-05-19 11:03:57', 'admin', '2024-12-31 16:03:18', 'admin', '2025-05-19 11:03:57', '测试员');
INSERT INTO `sys_user` VALUES (3, '10000', 100, 'test1', '鬼地方个', '00', '', '15845585475', '0', '', '$2a$10$zmRym15x0Z7RqpyuBxrf3.oxHo.Nu3Re/UBRHA7z82OebAwQaFr.S', '0', '0', '127.0.0.1', '2025-01-20 14:32:13', 'admin', '2025-01-20 14:31:51', '', '2025-01-20 14:32:12', '多个风格');

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  PRIMARY KEY (`user_id`, `post_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1, '10000');
INSERT INTO `sys_user_post` VALUES (1, 1, '20000');
INSERT INTO `sys_user_post` VALUES (2, 2, '10000');
INSERT INTO `sys_user_post` VALUES (2, 2, '20000');
INSERT INTO `sys_user_post` VALUES (3, 3, '10000');

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  PRIMARY KEY (`user_id`, `role_id`, `shop_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1, '10000');
INSERT INTO `sys_user_role` VALUES (1, 1, '20000');
INSERT INTO `sys_user_role` VALUES (2, 2, '10000');
INSERT INTO `sys_user_role` VALUES (2, 2, '20000');
INSERT INTO `sys_user_role` VALUES (3, 2, '10000');

-- ----------------------------
-- Table structure for wendao_shop_info
-- ----------------------------
DROP TABLE IF EXISTS `wendao_shop_info`;
CREATE TABLE `wendao_shop_info`  (
  `shop_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺ID',
  `shop_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '店铺名称',
  `shop_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '店铺logo',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用ID',
  `version_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '版本类型:0-试用版,1-标准版,2-专业版,3-旗舰版',
  `use_collection` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否启用收藏:0-否,1-是',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '店铺状态:0-正常,1-已关闭',
  `is_sealed` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否已封停:0-否,1-是',
  `is_seal` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否处于封停状态:0-否,1-是',
  `is_wait_seal` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否等待封停:0-否,1-是',
  `seal_apply_time` datetime NULL DEFAULT NULL COMMENT '封停申请时间',
  `ready_seal_at` datetime NULL DEFAULT NULL COMMENT '预计封停时间',
  `expire_time` date NULL DEFAULT NULL COMMENT '过期时间',
  `has_expired` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否已过期:0-否,1-是',
  `has_activate_order` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否有激活订单:0-否,1-是',
  `last_login` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否最后登录:0-否,1-是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `b_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  PRIMARY KEY (`shop_id`) USING BTREE,
  INDEX `idx_app_id`(`app_id`) USING BTREE,
  INDEX `idx_b_user_id`(`b_user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '店铺信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of wendao_shop_info
-- ----------------------------

-- ----------------------------
-- Table structure for wendao_sms_log
-- ----------------------------
DROP TABLE IF EXISTS `wendao_sms_log`;
CREATE TABLE `wendao_sms_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uuid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一标识',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `code` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '验证码',
  `type` tinyint(4) NOT NULL COMMENT '业务类型：1-登录，2-注册，3-重置密码',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送IP',
  `sms_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短信平台返回的消息ID',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误信息',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '发送状态：0-失败，1-成功，2-未回执',
  `send_time` datetime NULL DEFAULT NULL COMMENT '发送时间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_uuid`(`uuid`) USING BTREE,
  INDEX `idx_mobile_create_time`(`mobile`, `create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信发送日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of wendao_sms_log
-- ----------------------------
INSERT INTO `wendao_sms_log` VALUES (1, '9008b568ca5642d78201fe122a5dae5e', '18667016502', '253780', 1, '127.0.0.1', '80749464306', NULL, 1, '2025-01-10 12:39:18', '2025-01-10 12:39:18', '2025-01-10 12:39:18');
INSERT INTO `wendao_sms_log` VALUES (2, '6889809da3084714a439ec30833e6c5a', '18667016502', '476015', 1, '127.0.0.1', '80749499456', NULL, 1, '2025-01-10 12:47:25', '2025-01-10 12:47:25', '2025-01-10 12:47:25');
INSERT INTO `wendao_sms_log` VALUES (3, '591531f9da2b4e7a90405cc4e514694a', '18667016502', '501765', 1, '127.0.0.1', '80749701866', NULL, 1, '2025-01-10 13:17:21', '2025-01-10 13:17:21', '2025-01-10 13:17:21');
INSERT INTO `wendao_sms_log` VALUES (4, 'e987d4b77bb24e2690f12a49624ef4cf', '18667016502', '306393', 1, '127.0.0.1', '80749749702', NULL, 1, '2025-01-10 13:27:39', '2025-01-10 13:27:39', '2025-01-10 13:27:39');
INSERT INTO `wendao_sms_log` VALUES (5, '44832eb285304fa1817f1a6f32369692', '18667016502', '982316', 1, '127.0.0.1', '80749777222', NULL, 1, '2025-01-10 13:33:55', '2025-01-10 13:33:55', '2025-01-10 13:33:55');
INSERT INTO `wendao_sms_log` VALUES (6, '668b8fa9c5fe4060a2aeff75660f1ab5', '18667016502', '205069', 1, '127.0.0.1', '80749808068', NULL, 1, '2025-01-10 13:40:31', '2025-01-10 13:40:31', '2025-01-10 13:40:31');
INSERT INTO `wendao_sms_log` VALUES (7, '3f16507da8ff4479afc4e5ef398bbe92', '18667016502', '898473', 1, '127.0.0.1', '80749812294', NULL, 1, '2025-01-10 13:41:26', '2025-01-10 13:41:26', '2025-01-10 13:41:26');
INSERT INTO `wendao_sms_log` VALUES (8, '6955deb98cc44c85aa481c760b3b851c', '18658823687', '831581', 1, '127.0.0.1', '80749819050', NULL, 1, '2025-01-10 13:42:56', '2025-01-10 13:42:56', '2025-01-10 13:42:56');
INSERT INTO `wendao_sms_log` VALUES (9, 'c915f391368545d48b7b8636d017e3c9', '18658823687', '465075', 1, '127.0.0.1', '80749859178', NULL, 1, '2025-01-10 13:50:49', '2025-01-10 13:50:49', '2025-01-10 13:50:49');
INSERT INTO `wendao_sms_log` VALUES (10, '5cd804e3e22442eab9fc8fa12380e9bc', '18658823687', '181009', 1, '127.0.0.1', '80749866730', NULL, 1, '2025-01-10 13:52:00', '2025-01-10 13:52:00', '2025-01-10 13:52:00');
INSERT INTO `wendao_sms_log` VALUES (11, '97fa565e943a4e7e89f6b1a24def981f', '18658823687', '412194', 1, '127.0.0.1', '80749954822', NULL, 1, '2025-01-10 14:06:15', '2025-01-10 14:06:15', '2025-01-10 14:06:15');
INSERT INTO `wendao_sms_log` VALUES (12, '4189f9981a9040cba01d459b810d7072', '18658823687', '863368', 1, '127.0.0.1', '80750431664', NULL, 1, '2025-01-10 15:26:56', '2025-01-10 15:26:56', '2025-01-10 15:26:56');
INSERT INTO `wendao_sms_log` VALUES (13, '8e31c716d6ad4415858b14bbe13865ee', '18667016502', '033592', 1, '127.0.0.1', '80845631312', NULL, 1, '2025-01-23 16:08:37', '2025-01-23 16:08:37', '2025-01-23 16:08:37');
INSERT INTO `wendao_sms_log` VALUES (14, '2c3cae0819974a8a8155008ef5148017', '18667016502', '515674', 1, '127.0.0.1', '80845730684', NULL, 1, '2025-01-23 16:23:42', '2025-01-23 16:23:42', '2025-01-23 16:23:42');
INSERT INTO `wendao_sms_log` VALUES (15, '55b3bd53db7a4917aea387a796bc4ba2', '18667016502', '463561', 1, '127.0.0.1', '80845775510', NULL, 1, '2025-01-23 16:31:54', '2025-01-23 16:31:54', '2025-01-23 16:31:54');
INSERT INTO `wendao_sms_log` VALUES (16, '26d2c3cd950e402ca8b56bba4777d2d1', '18667016502', '817031', 1, '127.0.0.1', '80845824772', NULL, 1, '2025-01-23 16:41:22', '2025-01-23 16:41:22', '2025-01-23 16:41:22');

-- ----------------------------
-- Table structure for wendao_sys_login_log
-- ----------------------------
DROP TABLE IF EXISTS `wendao_sys_login_log`;
CREATE TABLE `wendao_sys_login_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `b_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户ID',
  `login_type` tinyint(4) NULL DEFAULT NULL COMMENT '登录方式:1微信扫码,2手机号',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录IP',
  `device_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备信息',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_b_user_id`(`b_user_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '登录日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wendao_sys_login_log
-- ----------------------------
INSERT INTO `wendao_sys_login_log` VALUES (1, 'b_u_cphpnt0cav3hxar3an7u6', 1, '************', '小程序', '2025-01-25 15:57:23', '2025-01-25 15:57:22');
INSERT INTO `wendao_sys_login_log` VALUES (2, 'b_u_cphpnt0cav3hxar3an7u6', 1, '************', '小程序', '2025-01-25 15:59:41', '2025-01-25 15:59:40');
INSERT INTO `wendao_sys_login_log` VALUES (3, 'b_u_cphpnt0cav3hxar3an7u6', 1, '************', '小程序', '2025-01-25 16:02:13', '2025-01-25 16:02:12');
INSERT INTO `wendao_sys_login_log` VALUES (4, 'b_u_cphpnt0cav3hxar3an7u6', 1, '************', '小程序', '2025-01-25 16:13:08', '2025-01-25 16:13:07');
INSERT INTO `wendao_sys_login_log` VALUES (5, 'b_u_mmdkhx0up1fc15n7wn9is', 1, '************', '小程序', '2025-01-25 16:13:46', '2025-01-25 16:13:46');

-- ----------------------------
-- Table structure for wendao_sys_user
-- ----------------------------
DROP TABLE IF EXISTS `wendao_sys_user`;
CREATE TABLE `wendao_sys_user`  (
  `b_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `wx_open_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信openid',
  `wx_union_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信unionid',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `nation_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '86' COMMENT '手机号国家代码',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '加密后的密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `head_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义头像URL',
  `wx_head_img_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信头像URL',
  `wx_nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信昵称',
  `avatar_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint(4) NULL DEFAULT 0 COMMENT '性别:0未知,1男,2女',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态:0禁用,1启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`b_user_id`) USING BTREE,
  UNIQUE INDEX `uk_wx_open_id`(`wx_open_id`) USING BTREE,
  INDEX `idx_wx_union_id`(`wx_union_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问到老师用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of wendao_sys_user
-- ----------------------------
INSERT INTO `wendao_sys_user` VALUES ('b_u_mmdkhx0up1fc15n7wn9is', 'o4kys4lyCCJPMYlD1O410X4GB1HA', 'o670b6i2se4jAjT8Hd8ce_VzZDn0', '18667016502', '86', NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, '2025-01-25 16:13:46', '2025-01-25 16:13:46');
INSERT INTO `wendao_sys_user` VALUES ('b_u_pmx81c3ohecnja365jq6f', NULL, NULL, '18667018888', '86', '$2a$10$m00f8IZO.YYYXuudR7EpLu3yxLfAN3Q5jqMWWRg33mGqwFteSmrg6', NULL, NULL, NULL, NULL, NULL, 0, 1, '2025-01-23 16:08:53', '2025-01-25 15:55:49');

-- ----------------------------
-- Table structure for wendao_wx_qrcode_record
-- ----------------------------
DROP TABLE IF EXISTS `wendao_wx_qrcode_record`;
CREATE TABLE `wendao_wx_qrcode_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scene` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '场景值',
  `page_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '页面路径',
  `business_type` tinyint(4) NULL DEFAULT NULL COMMENT '业务类型:1登录,2注册',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态:0已失效,1有效',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_scene`(`scene`) USING BTREE,
  INDEX `idx_expire_time`(`expire_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序码记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wendao_wx_qrcode_record
-- ----------------------------

-- ----------------------------
-- Table structure for worker_node
-- ----------------------------
DROP TABLE IF EXISTS `worker_node`;
CREATE TABLE `worker_node`  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'auto increment id',
  `HOST_NAME` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'host name',
  `PORT` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'port',
  `TYPE` int(11) NOT NULL COMMENT 'node type: ACTUAL or CONTAINER',
  `LAUNCH_DATE` date NOT NULL COMMENT 'launch date',
  `MODIFIED` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'modified time',
  `CREATED` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'created time',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DB WorkerID Assigner for UID Generator' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_node
-- ----------------------------
INSERT INTO `worker_node` VALUES (1, '*************', '1747723284206-61620', 2, '2025-05-20', '2025-05-20 14:41:23', '2025-05-20 14:41:23');
INSERT INTO `worker_node` VALUES (2, '*************', '1747723683016-80046', 2, '2025-05-20', '2025-05-20 14:48:02', '2025-05-20 14:48:02');
INSERT INTO `worker_node` VALUES (3, '*************', '1747723960373-85798', 2, '2025-05-20', '2025-05-20 14:52:39', '2025-05-20 14:52:39');
INSERT INTO `worker_node` VALUES (4, '*************', '1747723982220-3956', 2, '2025-05-20', '2025-05-20 14:53:01', '2025-05-20 14:53:01');

SET FOREIGN_KEY_CHECKS = 1;

package com.ruoyi.wendaologin.enums;

import lombok.Getter;

import java.math.BigDecimal;

@Getter
public enum ShopVersionPrice {
    STANDARD(ShopVersionType.STANDARD, new BigDecimal("6800")),
    PROFESSIONAL(ShopVersionType.PROFESSIONAL, new BigDecimal("12800")),
    FLAGSHIP(ShopVersionType.FLAGSHIP, new BigDecimal("25800"));

    private final ShopVersionType versionType;
    private final BigDecimal price;

    ShopVersionPrice(ShopVersionType versionType, BigDecimal price) {
        this.versionType = versionType;
        this.price = price;
    }

    public static BigDecimal getPriceByVersion(ShopVersionType versionType) {
        for (ShopVersionPrice price : values()) {
            if (price.getVersionType().equals(versionType)) {
                return price.getPrice();
            }
        }
        throw new RuntimeException("无效的版本类型");
    }
} 
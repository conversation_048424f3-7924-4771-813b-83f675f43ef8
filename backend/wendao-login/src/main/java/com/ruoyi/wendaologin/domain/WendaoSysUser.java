package com.ruoyi.wendaologin.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 问到老师用户对象 wendao_sys_user
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class WendaoSysUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private String bUserId;

    /** 微信openid */
    @Excel(name = "微信openid")
    private String wxOpenId;

    /** 微信unionid */
    @Excel(name = "微信unionid")
    private String wxUnionId;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobile;

    /** 手机号国家代码 */
    @Excel(name = "手机号国家代码")
    private String nationCode;

    /** 加密后的密码 */
    @Excel(name = "加密后的密码")
    private String password;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 自定义头像URL */
    @Excel(name = "自定义头像URL")
    private String headImgUrl;

    /** 微信头像URL */
    @Excel(name = "微信头像URL")
    private String wxHeadImgUrl;

    /** 微信昵称 */
    @Excel(name = "微信昵称")
    private String wxNickName;

    /** 头像URL */
    @Excel(name = "头像URL")
    private String avatarUrl;

    /** 性别:0未知,1男,2女 */
    @Excel(name = "性别:0未知,1男,2女")
    private Integer gender;

    /** 状态:0禁用,1启用 */
    @Excel(name = "状态:0禁用,1启用")
    private Integer status;

    public void setbUserId(String bUserId) 
    {
        this.bUserId = bUserId;
    }

    public String getbUserId() 
    {
        return bUserId;
    }
    public void setWxOpenId(String wxOpenId) 
    {
        this.wxOpenId = wxOpenId;
    }

    public String getWxOpenId() 
    {
        return wxOpenId;
    }
    public void setWxUnionId(String wxUnionId) 
    {
        this.wxUnionId = wxUnionId;
    }

    public String getWxUnionId() 
    {
        return wxUnionId;
    }
    public void setMobile(String mobile) 
    {
        this.mobile = mobile;
    }

    public String getMobile() 
    {
        return mobile;
    }
    public void setNationCode(String nationCode) 
    {
        this.nationCode = nationCode;
    }

    public String getNationCode() 
    {
        return nationCode;
    }
    public void setPassword(String password) 
    {
        this.password = password;
    }

    public String getPassword() 
    {
        return password;
    }
    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }
    public void setHeadImgUrl(String headImgUrl) 
    {
        this.headImgUrl = headImgUrl;
    }

    public String getHeadImgUrl() 
    {
        return headImgUrl;
    }
    public void setWxHeadImgUrl(String wxHeadImgUrl) 
    {
        this.wxHeadImgUrl = wxHeadImgUrl;
    }

    public String getWxHeadImgUrl() 
    {
        return wxHeadImgUrl;
    }
    public void setWxNickName(String wxNickName) 
    {
        this.wxNickName = wxNickName;
    }

    public String getWxNickName() 
    {
        return wxNickName;
    }
    public void setAvatarUrl(String avatarUrl) 
    {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarUrl() 
    {
        return avatarUrl;
    }
    public void setGender(Integer gender) 
    {
        this.gender = gender;
    }

    public Integer getGender() 
    {
        return gender;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("bUserId", getbUserId())
            .append("wxOpenId", getWxOpenId())
            .append("wxUnionId", getWxUnionId())
            .append("mobile", getMobile())
            .append("nationCode", getNationCode())
            .append("password", getPassword())
            .append("nickname", getNickname())
            .append("headImgUrl", getHeadImgUrl())
            .append("wxHeadImgUrl", getWxHeadImgUrl())
            .append("wxNickName", getWxNickName())
            .append("avatarUrl", getAvatarUrl())
            .append("gender", getGender())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}

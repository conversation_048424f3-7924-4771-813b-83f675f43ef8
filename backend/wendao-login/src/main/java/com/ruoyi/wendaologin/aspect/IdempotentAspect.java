package com.ruoyi.wendaologin.aspect;

import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.wendaologin.annotation.Idempotent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

@Slf4j
@Aspect
@Component
public class IdempotentAspect {
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    @Around("@annotation(com.ruoyi.wendaologin.annotation.Idempotent)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 获取当前请求的HttpServletRequest
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new RuntimeException("获取请求上下文失败");
        }
        HttpServletRequest request = attributes.getRequest();
        
        // 获取注解信息
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Idempotent idempotent = method.getAnnotation(Idempotent.class);
        
        // 构建Redis键
        String key = buildKey(request, point);
        
        // 尝试获取锁
        boolean isLocked = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(key, "1", idempotent.expire(), TimeUnit.SECONDS));
        if (!isLocked) {
            log.warn("重复请求，key={}", key);
            throw new RuntimeException(idempotent.message());
        }
        
        try {
            // 执行目标方法
            return point.proceed();
        } finally {
            if (idempotent.immediateDelete()) {
                try {
                    stringRedisTemplate.delete(key);
                } catch (Exception e) {
                    log.error("删除幂等性键失败，key={}", key, e);
                }
            }
            // 如果 immediateDelete 为 false，则让 key 自然过期
        }
    }
    
    /**
     * 构建Redis键
     * 格式：wendao_idempotent:ip:uri:method:params
     */
    private String buildKey(HttpServletRequest request, ProceedingJoinPoint point) {
        StringBuilder sb = new StringBuilder("wendao_idempotent:");
        // 添加IP
        sb.append(IpUtils.getIpAddr(request)).append(":");
        // 添加URI
        sb.append(request.getRequestURI()).append(":");
        // 添加方法名
        sb.append(point.getSignature().getName()).append(":");
        // 添加参数
        Object[] args = point.getArgs();
        if (args != null) {
            for (Object arg : args) {
                if (arg != null) {
                    sb.append(arg.toString());
                }
            }
        }
        return sb.toString();
    }
} 
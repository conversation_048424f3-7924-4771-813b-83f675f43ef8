package com.ruoyi.wendaologin.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class WxPhoneDTO {
    /**
     * 微信加密数据
     */
    @NotBlank(message = "加密数据不能为空")
    private String encryptedData;
    
    /**
     * 加密算法的初始向量
     */
    @NotBlank(message = "加密向量不能为空")
    private String iv;
    
    /**
     * 用户登录时返回的code，当session失效时需要传入
     */
    private String code;
    
    /**
     * 用户的openid，当session有效时传入
     */
    private String openid;

    /**
     * 用户的unionid，当session有效时传入
     */
    private String unionid;
    
    /**
     * 场景值
     */
    @NotBlank(message = "场景值不能为空")
    private String scene;
} 
{"info": {"_postman_id": "wx-creator-info-controller-api", "name": "WxCreatorInfoController API", "description": "创作者小程序扫码登录接口相关API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "更新扫码状态", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "uuid", "value": "{{uuid}}", "description": "二维码UUID"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/updateScanStatus", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "updateScanStatus"]}, "description": "小程序端扫码进入页面即请求,更新扫码状态,并在pc端登录页面显示扫码成功"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "uuid", "value": "test-uuid"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/updateScanStatus", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "updateScanStatus"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"更新扫码状态成功!\"\n}"}, {"name": "失败响应", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "uuid", "value": "invalid-uuid"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/updateScanStatus", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "updateScanStatus"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 500,\n    \"msg\": \"非法进入,请扫码进入!\"\n}"}]}, {"name": "取消登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "uuid", "value": "{{uuid}}", "description": "二维码UUID"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/cancelLogin", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "cancelLogin"]}, "description": "小程序扫码进入后,用户选择取消登录接口!"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "uuid", "value": "test-uuid"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/cancelLogin", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "cancelLogin"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"取消登录成功!\"\n}"}]}, {"name": "同意并继续", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "uuid", "value": "{{uuid}}", "description": "二维码UUID"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/agreeAndContinue", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "agreeAndContinue"]}, "description": "小程序扫码进入后,用户看到显示的手机号(原来就有,或者新授权手机号),点击\"同意并继续\",然后修改登录记录状态为登录成功."}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "uuid", "value": "test-uuid"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/agreeAndContinue", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "agreeAndContinue"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"登录成功!\"\n}"}]}, {"name": "微信登录获取openid和unionid", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "code", "value": "{{wxCode}}", "description": "wx.login接口返回的code"}, {"key": "uuid", "value": "{{uuid}}", "description": "二维码UUID"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/code2session", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "code2session"]}, "description": "微信登录,获取openid和unionid,并返回给前端"}, "response": [{"name": "成功响应-已有手机号", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "code", "value": "test-wx-code"}, {"key": "uuid", "value": "test-uuid"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/code2session", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "code2session"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"id\": 1,\n        \"creatorId\": \"creator123\",\n        \"avatarUrl\": \"https://example.com/avatar.jpg\",\n        \"openid\": \"wx_openid\",\n        \"unionid\": \"wx_unionid\",\n        \"wxAccount\": \"***********\",\n        \"phoneNumber\": \"+86***********\",\n        \"purePhoneNumber\": \"***********\",\n        \"countryCode\": \"+86\",\n        \"wxMaAppid\": \"wx_appid\"\n    }\n}"}, {"name": "成功响应-需要授权手机号", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "code", "value": "test-wx-code"}, {"key": "uuid", "value": "test-uuid"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/code2session", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "code2session"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"openid\": \"wx_openid\",\n        \"unionid\": \"wx_unionid\"\n    }\n}"}]}, {"name": "获取用户手机号", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "code", "value": "{{phoneCode}}", "description": "getPhoneNumber方法返回的code"}, {"key": "uuid", "value": "{{uuid}}", "description": "二维码UUID"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/getUserPhoneNumber", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "getUserPhoneNumber"]}, "description": "/code2session这个接口请求后返回的数据中如果不包含phoneNumber,则请求此接口让用户进行微信授权获取手机号\n此时根据用户unionid查询用户信息,如果有且手机号码为空则更新手机号码,如果没有则创建创作者账号并将unionid,openid和手机号码信息保存."}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "code", "value": "test-phone-code"}, {"key": "uuid", "value": "test-uuid"}]}, "url": {"raw": "{{baseUrl}}/creator_wx_login/getUserPhoneNumber", "host": ["{{baseUrl}}"], "path": ["creator_wx_login", "getUserPhoneNumber"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"code\": 200,\n    \"msg\": \"操作成功\",\n    \"data\": {\n        \"id\": 1,\n        \"creatorId\": \"creator123\",\n        \"avatarUrl\": \"https://example.com/avatar.jpg\",\n        \"openid\": \"wx_openid\",\n        \"unionid\": \"wx_unionid\",\n        \"wxAccount\": \"***********\",\n        \"phoneNumber\": \"+86***********\",\n        \"purePhoneNumber\": \"***********\",\n        \"countryCode\": \"+86\",\n        \"wxMaAppid\": \"wx_appid\"\n    }\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string", "description": "API基础URL"}, {"key": "uuid", "value": "test-uuid-123456", "type": "string", "description": "测试用UUID"}, {"key": "wxCode", "value": "test-wx-code", "type": "string", "description": "微信登录code"}, {"key": "phoneCode", "value": "test-phone-code", "type": "string", "description": "获取手机号的code"}]}
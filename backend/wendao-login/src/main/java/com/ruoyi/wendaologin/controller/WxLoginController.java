package com.ruoyi.wendaologin.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.scheme.WxMaGenerateSchemeRequest;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.wendaologin.annotation.Idempotent;
import com.ruoyi.wendaologin.dto.WxCheckSessionDTO;
import com.ruoyi.wendaologin.dto.WxPhoneDTO;
import com.ruoyi.wendaologin.dto.WxScanDTO;
import com.ruoyi.wendaologin.service.WxLoginService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Anonymous
@RestController
@RequestMapping("/wx/login")
public class WxLoginController {

    @Autowired
    private WxLoginService wxLoginService;

    @Autowired
    private WxMaService wxMaService;

    /**
     * 生成小程序码
     */
    @GetMapping("/qrcode")
    public AjaxResult generateQrcode(@RequestParam Integer businessType) {
        return AjaxResult.success(wxLoginService.generateWxaCode(businessType));
    }

    /**
     * 处理扫码
     */
    @PostMapping("/scan")
    @Idempotent(expire = 5, message = "请勿重复扫码")
    public AjaxResult handleScan(@Valid @RequestBody WxScanDTO wxScanDTO) {
        return AjaxResult.success(wxLoginService.handleScan(wxScanDTO.getScene(), wxScanDTO.getCode()));
    }

    /**
     * 处理手机号登录
     */
    @PostMapping("/phone")
    @Idempotent(expire = 5, message = "请勿重复登录")
    public AjaxResult handlePhoneLogin(@Valid @RequestBody WxPhoneDTO phoneDTO) {
        return AjaxResult.success(wxLoginService.decryptPhone(phoneDTO));
    }

    /**
     * 检查扫码状态
     */
    @GetMapping("/scan/status")
    public AjaxResult checkScanStatus(@RequestParam String scene) {
        return AjaxResult.success(wxLoginService.checkScanStatus(scene));
    }

    /**
     * 检查session是否有效
     */
    @PostMapping("/check/session")
    public AjaxResult checkSession(@Valid @RequestBody WxCheckSessionDTO dto) {
        return AjaxResult.success(wxLoginService.checkSession(dto));
    }
} 
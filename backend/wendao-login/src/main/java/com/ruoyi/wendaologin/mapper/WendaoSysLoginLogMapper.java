package com.ruoyi.wendaologin.mapper;

import java.util.List;
import com.ruoyi.wendaologin.domain.WendaoSysLoginLog;

/**
 * 登录日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface WendaoSysLoginLogMapper 
{
    /**
     * 查询登录日志
     * 
     * @param id 登录日志主键
     * @return 登录日志
     */
    public WendaoSysLoginLog selectWendaoSysLoginLogById(Long id);

    /**
     * 查询登录日志列表
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 登录日志集合
     */
    public List<WendaoSysLoginLog> selectWendaoSysLoginLogList(WendaoSysLoginLog wendaoSysLoginLog);

    /**
     * 新增登录日志
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 结果
     */
    public int insertWendaoSysLoginLog(WendaoSysLoginLog wendaoSysLoginLog);

    /**
     * 修改登录日志
     * 
     * @param wendaoSysLoginLog 登录日志
     * @return 结果
     */
    public int updateWendaoSysLoginLog(WendaoSysLoginLog wendaoSysLoginLog);

    /**
     * 删除登录日志
     * 
     * @param id 登录日志主键
     * @return 结果
     */
    public int deleteWendaoSysLoginLogById(Long id);

    /**
     * 批量删除登录日志
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoSysLoginLogByIds(Long[] ids);
}

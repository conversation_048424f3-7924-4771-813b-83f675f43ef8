package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 试用权限配置对象 sys_trial_permission
 * 
 * <AUTHOR>
 */
public class SysTrialPermission extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 权限编码 */
    @Excel(name = "权限编码")
    @NotBlank(message = "权限编码不能为空")
    private String permissionCode;

    /** 权限名称 */
    @Excel(name = "权限名称")
    @NotBlank(message = "权限名称不能为空")
    private String permissionName;

    /** 权限类型：1-读权限，2-写权限 */
    @Excel(name = "权限类型", readConverterExp = "1=读权限,2=写权限")
    @NotNull(message = "权限类型不能为空")
    private Integer permissionType;

    /** 试用期是否可用：0-不可用，1-可用 */
    @Excel(name = "试用期可用", readConverterExp = "0=不可用,1=可用")
    @NotNull(message = "试用期可用状态不能为空")
    private Integer trialAvailable;

    /** 试用过期后是否可用：0-不可用，1-可用 */
    @Excel(name = "过期后可用", readConverterExp = "0=不可用,1=可用")
    @NotNull(message = "过期后可用状态不能为空")
    private Integer expiredAvailable;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setPermissionCode(String permissionCode) 
    {
        this.permissionCode = permissionCode;
    }

    public String getPermissionCode() 
    {
        return permissionCode;
    }

    public void setPermissionName(String permissionName) 
    {
        this.permissionName = permissionName;
    }

    public String getPermissionName() 
    {
        return permissionName;
    }

    public void setPermissionType(Integer permissionType) 
    {
        this.permissionType = permissionType;
    }

    public Integer getPermissionType() 
    {
        return permissionType;
    }

    public void setTrialAvailable(Integer trialAvailable) 
    {
        this.trialAvailable = trialAvailable;
    }

    public Integer getTrialAvailable() 
    {
        return trialAvailable;
    }

    public void setExpiredAvailable(Integer expiredAvailable) 
    {
        this.expiredAvailable = expiredAvailable;
    }

    public Integer getExpiredAvailable() 
    {
        return expiredAvailable;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("permissionCode", getPermissionCode())
            .append("permissionName", getPermissionName())
            .append("permissionType", getPermissionType())
            .append("trialAvailable", getTrialAvailable())
            .append("expiredAvailable", getExpiredAvailable())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
} 
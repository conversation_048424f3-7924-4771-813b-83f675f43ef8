package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.system.service.IFetchWxKtMiniAppTokenService;
import org.springframework.stereotype.Service;

@Service
public class FetchWxKtMiniAppTokenServiceImpl implements IFetchWxKtMiniAppTokenService {
    private static final String key = "87er8wefsd5f4e8wr8ew78rew";
    @Override
    public String fetchToken() {
        String url  = "https://goodminiapp.wendao101.com/kt_access_token_controller/getKtWxToken?requestKey="+key;
        return HttpUtils.sendGet(url);
    }
}

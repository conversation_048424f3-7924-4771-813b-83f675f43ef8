package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysTenantMapper;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.service.ISysTenantService;

/**
 * 租户管理 服务实现
 */
@Service
public class SysTenantServiceImpl implements ISysTenantService {
    @Autowired
    private SysTenantMapper tenantMapper;

    /**
     * 查询租户信息
     */
    @Override
    public SysTenant selectTenantById(String shopId) {
        return tenantMapper.selectTenantById(shopId);
    }

    /**
     * 查询租户列表
     */
    @Override
    public List<SysTenant> selectTenantList(SysTenant tenant) {
        return tenantMapper.selectTenantList(tenant);
    }

    /**
     * 新增租户
     */
    @Override
    public int insertTenant(SysTenant tenant) {
        return tenantMapper.insertTenant(tenant);
    }

    /**
     * 修改租户
     */
    @Override
    public int updateTenant(SysTenant tenant) {
        return tenantMapper.updateTenant(tenant);
    }

    /**
     * 删除租户对象
     */
    @Override
    public int deleteTenantByIds(String[] shopIds) {
        return tenantMapper.deleteTenantByIds(shopIds);
    }

    @Override
    public SysTenant selectNormalTenantByShopId(String shopId) {
        return tenantMapper.selectNormalTenantByShopId(shopId);
    }
} 
package com.ruoyi.wendaologin.service.impl;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.wendaologin.enums.ShopVersionType;
import com.ruoyi.wendaologin.util.ShopIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.wendaologin.mapper.WendaoShopInfoMapper;
import com.ruoyi.wendaologin.domain.WendaoShopInfo;
import com.ruoyi.wendaologin.service.IWendaoShopInfoService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 店铺信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@Service
public class WendaoShopInfoServiceImpl implements IWendaoShopInfoService 
{
    @Autowired
    private WendaoShopInfoMapper wendaoShopInfoMapper;

    /**
     * 查询店铺信息
     * 
     * @param shopId 店铺信息主键
     * @return 店铺信息
     */
    @Override
    public WendaoShopInfo selectWendaoShopInfoByShopId(String shopId)
    {
        return wendaoShopInfoMapper.selectWendaoShopInfoByShopId(shopId);
    }

    /**
     * 查询店铺信息列表
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 店铺信息
     */
    @Override
    public List<WendaoShopInfo> selectWendaoShopInfoList(WendaoShopInfo wendaoShopInfo)
    {
        return wendaoShopInfoMapper.selectWendaoShopInfoList(wendaoShopInfo);
    }

    /**
     * 新增店铺信息
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 结果
     */
    @Override
    public int insertWendaoShopInfo(WendaoShopInfo wendaoShopInfo)
    {
        wendaoShopInfo.setCreateTime(DateUtils.getNowDate());
        return wendaoShopInfoMapper.insertWendaoShopInfo(wendaoShopInfo);
    }

    /**
     * 修改店铺信息
     * 
     * @param wendaoShopInfo 店铺信息
     * @return 结果
     */
    @Override
    public int updateWendaoShopInfo(WendaoShopInfo wendaoShopInfo)
    {
        wendaoShopInfo.setUpdateTime(DateUtils.getNowDate());
        return wendaoShopInfoMapper.updateWendaoShopInfo(wendaoShopInfo);
    }

    /**
     * 批量删除店铺信息
     * 
     * @param shopIds 需要删除的店铺信息主键
     * @return 结果
     */
    @Override
    public int deleteWendaoShopInfoByShopIds(String[] shopIds)
    {
        return wendaoShopInfoMapper.deleteWendaoShopInfoByShopIds(shopIds);
    }

    /**
     * 删除店铺信息信息
     * 
     * @param shopId 店铺信息主键
     * @return 结果
     */
    @Override
    public int deleteWendaoShopInfoByShopId(String shopId)
    {
        return wendaoShopInfoMapper.deleteWendaoShopInfoByShopId(shopId);
    }

    @Override
    public WendaoShopInfo createShop(WendaoShopInfo shopInfo) {
        // 检查店铺名称是否已存在
        if (wendaoShopInfoMapper.checkShopNameExists(shopInfo.getShopName()) > 0) {
            throw new ServiceException("店铺名称已存在");
        }

        // 生成店铺ID
        String shopId = ShopIdGenerator.generateShopId();
        shopInfo.setShopId(shopId);
        shopInfo.setAppId(shopId);  // 应用ID与店铺ID相同

        // 设置默认值
        shopInfo.setVersionType(ShopVersionType.TRIAL.getCode());  // 默认试用版
        shopInfo.setUseCollection(1);// 默认启用收藏
        shopInfo.setStatus(0);       // 默认正常状态
        shopInfo.setIsSealed(0);
        shopInfo.setIsSeal(0);
        shopInfo.setIsWaitSeal(0);
        shopInfo.setHasExpired(0);
        shopInfo.setHasActivateOrder(0);
        shopInfo.setLastLogin(0);

        // 设置创建时间和过期时间
        Date now = new Date();
        shopInfo.setCreateTime(now);

        // 设置过期时间为7天后(只保留日期部分)
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        // 清除时分秒
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        shopInfo.setExpireTime(calendar.getTime());

        // 确保设置了用户ID
        if (StringUtils.isBlank(shopInfo.getbUserId())) {
            throw new ServiceException("用户ID不能为空");
        }

        // 保存店铺信息
        wendaoShopInfoMapper.insertWendaoShopInfo(shopInfo);
        log.info("创建试用版店铺成功, shopId={}, shopName={}, expireTime={}",
                shopId, shopInfo.getShopName(),
                new SimpleDateFormat("yyyy-MM-dd").format(shopInfo.getExpireTime()));
        return shopInfo;
    }

    @Override
    public WendaoShopInfo getShopInfo(String shopId) {
        WendaoShopInfo shopInfo = wendaoShopInfoMapper.selectWendaoShopInfoByShopId(shopId);
        if (shopInfo == null) {
            throw new ServiceException("店铺不存在");
        }
        return shopInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShopLastLogin(String shopId, String bUserId) {
        // 先将该用户的所有店铺last_login设为0
        wendaoShopInfoMapper.resetUserShopsLastLogin(bUserId);

        // 将当前访问的店铺last_login设为1
        wendaoShopInfoMapper.updateShopLastLogin(shopId, 1);

        log.info("更新店铺最后登录状态, shopId={}, bUserId={}", shopId, bUserId);
    }

    @Override
    public List<WendaoShopInfo> getShopsByUserId(String bUserId) {
        List<WendaoShopInfo> shops = wendaoShopInfoMapper.selectByUserId(bUserId);
        log.info("查询用户店铺列表, bUserId={}, count={}", bUserId, shops.size());
        return shops;
    }
}

package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysTenantIgnoreTable;
import com.ruoyi.system.service.ISysTenantIgnoreTableService;

/**
 * 租户忽略表配置Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/tenantIgnoreTable")
public class SysTenantIgnoreTableController extends BaseController
{
    @Autowired
    private ISysTenantIgnoreTableService tenantIgnoreTableService;

    /**
     * 查询租户忽略表配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenantIgnoreTable:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysTenantIgnoreTable sysTenantIgnoreTable)
    {
        startPage();
        List<SysTenantIgnoreTable> list = tenantIgnoreTableService.selectTenantIgnoreTableList(sysTenantIgnoreTable);
        return getDataTable(list);
    }

    /**
     * 导出租户忽略表配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenantIgnoreTable:export')")
    @Log(title = "租户忽略表配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysTenantIgnoreTable sysTenantIgnoreTable)
    {
        List<SysTenantIgnoreTable> list = tenantIgnoreTableService.selectTenantIgnoreTableList(sysTenantIgnoreTable);
        ExcelUtil<SysTenantIgnoreTable> util = new ExcelUtil<SysTenantIgnoreTable>(SysTenantIgnoreTable.class);
        util.exportExcel(response, list, "租户忽略表配置数据");
    }

    /**
     * 获取租户忽略表配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tenantIgnoreTable:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tenantIgnoreTableService.selectTenantIgnoreTableById(id));
    }

    /**
     * 新增租户忽略表配置
     */
    @PreAuthorize("@ss.hasPermi('system:tenantIgnoreTable:add')")
    @Log(title = "租户忽略表配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysTenantIgnoreTable sysTenantIgnoreTable)
    {
        if (!tenantIgnoreTableService.checkTableNameUnique(sysTenantIgnoreTable))
        {
            return error("新增表名'" + sysTenantIgnoreTable.getTableName() + "'失败，表名已存在");
        }
        sysTenantIgnoreTable.setCreateBy(getUsername());
        return toAjax(tenantIgnoreTableService.insertTenantIgnoreTable(sysTenantIgnoreTable));
    }

    /**
     * 修改租户忽略表配置
     */
    @PreAuthorize("@ss.hasPermi('system:tenantIgnoreTable:edit')")
    @Log(title = "租户忽略表配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysTenantIgnoreTable sysTenantIgnoreTable)
    {
        if (!tenantIgnoreTableService.checkTableNameUnique(sysTenantIgnoreTable))
        {
            return error("修改表名'" + sysTenantIgnoreTable.getTableName() + "'失败，表名已存在");
        }
        sysTenantIgnoreTable.setUpdateBy(getUsername());
        return toAjax(tenantIgnoreTableService.updateTenantIgnoreTable(sysTenantIgnoreTable));
    }

    /**
     * 删除租户忽略表配置
     */
    @PreAuthorize("@ss.hasPermi('system:tenantIgnoreTable:remove')")
    @Log(title = "租户忽略表配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        tenantIgnoreTableService.deleteTenantIgnoreTableByIds(ids);
        return success();
    }

    /**
     * 刷新缓存
     */
    @PreAuthorize("@ss.hasPermi('system:tenantIgnoreTable:remove')")
    @Log(title = "租户忽略表配置", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache()
    {
        tenantIgnoreTableService.resetIgnoreTableCache();
        return success();
    }
} 
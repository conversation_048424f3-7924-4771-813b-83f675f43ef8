package com.ruoyi.web.controller.system;

import java.util.UUID;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.system.domain.WxLogin;
import com.ruoyi.system.service.IWxLoginService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.CreatorInfo;
import com.ruoyi.system.service.ICreatorInfoService;

/**
 * 创作者小程序扫码登录接口
 */
@RestController
@RequestMapping("/creator_wx_login")
public class WxCreatorInfoController extends BaseController {
    @Autowired
    private ICreatorInfoService creatorInfoService;
    @Autowired
    private WxMaService wxMaService;
    @Autowired
    private IWxLoginService wxLoginService;

    /**
     * 小程序端扫码进入页面即请求,更新扫码状态,并在pc端登录页面显示扫码成功
     *
     * @param uuid
     * @return
     */
    @Anonymous
    @PostMapping(value = "/updateScanStatus")
    public AjaxResult updateScanStatus(@RequestParam("uuid") String uuid) {
        WxLogin wxLogin = wxLoginService.selectWxLoginByUuid(uuid);
        if (wxLogin == null) {
            return error("非法进入,请扫码进入!");
        }
        wxLogin.setScanSuccess(1);
        wxLoginService.updateWxLogin(wxLogin);
        return success("更新扫码状态成功!");
    }

    /**
     * 小程序扫码进入后,用户选择取消登录接口!
     *
     * @param uuid
     * @return
     */
    @Anonymous
    @PostMapping(value = "/cancelLogin")
    public AjaxResult cancelLogin(@RequestParam("uuid") String uuid) {
        WxLogin wxLogin = wxLoginService.selectWxLoginByUuid(uuid);
        if (wxLogin == null) {
            return error("非法进入,请扫码进入!");
        }
        wxLogin.setScanSuccess(2);
        wxLoginService.updateWxLogin(wxLogin);
        return success("取消登录成功!");
    }

    /**
     * 小程序扫码进入后,用户看到显示的手机号(原来就有,或者新授权手机号),点击"同意并继续",然后修改登录记录状态为登录成功.
     *
     * @param uuid
     * @return
     */
    @Anonymous
    @PostMapping(value = "/agreeAndContinue")
    public AjaxResult agreeAndContinue(@RequestParam("uuid") String uuid) {
        WxLogin wxLogin = wxLoginService.selectWxLoginByUuid(uuid);
        if (wxLogin == null || StringUtils.isBlank(wxLogin.getUnionid())) {
            return error("系统错误,无法登录!");
        }
        CreatorInfo creatorInfo = creatorInfoService.selectCreatorInfoByUnionid(wxLogin.getUnionid());
        //已经存在!且手机号码不为空则直接在小程序中显示手机号码,并显示按钮"同意并继续"
        if (creatorInfo == null || StringUtils.isBlank(creatorInfo.getPhoneNumber())) {
            return error("账号不存在或手机号码未授权!");
        }
        wxLogin.setCreatorId(creatorInfo.getCreatorId());
        wxLogin.setScanSuccess(3);
        wxLoginService.updateWxLogin(wxLogin);
        return success("登录成功!");
    }

    /**
     * 微信登录,获取openid和unionid,并返回给前端
     *
     * @param code wx.login接口返回的code
     * @return
     */
    @Anonymous
    @PostMapping(value = "/code2session")
    public AjaxResult code2session(@RequestParam("code") String code, @RequestParam("uuid") String uuid) {
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(code);
            //sessionKey暂时没用
            String sessionKey = sessionInfo.getSessionKey();
            sessionInfo.setSessionKey(null);
            //报错openId和unionid
            WxLogin wxLogin = wxLoginService.selectWxLoginByUuid(uuid);
            if (wxLogin == null) {
                return error("系统错误,无法登录!");
            }
            wxLogin.setOpenid(sessionInfo.getOpenid());
            wxLogin.setUnionid(sessionInfo.getUnionid());
            wxLoginService.updateWxLogin(wxLogin);
            //查询创作者用户信息
            CreatorInfo creatorInfo = creatorInfoService.selectCreatorInfoByUnionid(sessionInfo.getUnionid());
            //已经存在!且手机号码不为空则直接在小程序中显示手机号码,并显示按钮"同意并继续"
            if (creatorInfo != null && StringUtils.isNotBlank(creatorInfo.getPhoneNumber())) {
                creatorInfo.setPassword(null);
                return success(creatorInfo);
            } else {
                creatorInfo = new CreatorInfo();
                creatorInfo.setOpenid(sessionInfo.getOpenid());
                creatorInfo.setUnionid(sessionInfo.getUnionid());
            }
            return success(creatorInfo);
        } catch (WxErrorException e) {
            return error(e.getError().getErrorMsg());
        }
    }

    /**
     * /code2session这个接口请求后返回的数据中如果不包含phoneNumber,则请求此接口让用户进行微信授权获取手机号
     * 此时根据用户unionid查询用户信息,如果有且手机号码为空则更新手机号码,如果没有则创建创作者账号并将unionid,openid和手机号码信息保存.
     *
     * @param code getPhoneNumber方法返回的code
     * @return
     */
    @Anonymous
    @PostMapping(value = "/getUserPhoneNumber")
    public AjaxResult getUserPhoneNumber(@RequestParam("code") String code, @RequestParam("uuid") String uuid) {
        try {
            WxMaPhoneNumberInfo phoneNumber = wxMaService.getUserService().getPhoneNumber(code);
            //报错openId和unionid
            WxLogin wxLogin = wxLoginService.selectWxLoginByUuid(uuid);
            if (wxLogin == null) {
                return error("系统错误,无法登录!");
            }
            String unionid = wxLogin.getUnionid();
            CreatorInfo creatorInfo = creatorInfoService.selectCreatorInfoByUnionid(unionid);
            if (creatorInfo != null) {
                boolean willUpdate = false;
                //如果creatorInfo中的PhoneNumber为空则设置
                if (StringUtils.isBlank(creatorInfo.getPhoneNumber())) {
                    creatorInfo.setPhoneNumber(phoneNumber.getPhoneNumber());
                    creatorInfo.setPurePhoneNumber(phoneNumber.getPurePhoneNumber());
                    creatorInfo.setCountryCode(phoneNumber.getCountryCode());
                    willUpdate = true;
                }
                if (StringUtils.isBlank(creatorInfo.getWxAccount())) {
                    creatorInfo.setWxAccount(phoneNumber.getPhoneNumber());
                    willUpdate = true;
                }
                if (willUpdate) {
                    creatorInfoService.updateCreatorInfo(creatorInfo);
                }
            } else {
                creatorInfo = new CreatorInfo();
                creatorInfo.setOpenid(wxLogin.getOpenid());
                creatorInfo.setUnionid(wxLogin.getUnionid());
                creatorInfo.setCreatorId(UUID.randomUUID().toString().replace("-", ""));
                creatorInfo.setPhoneNumber(phoneNumber.getPhoneNumber());
                creatorInfo.setPurePhoneNumber(phoneNumber.getPurePhoneNumber());
                creatorInfo.setCountryCode(phoneNumber.getCountryCode());
                creatorInfo.setWxAccount(phoneNumber.getPhoneNumber());
                creatorInfoService.insertCreatorInfo(creatorInfo);
            }
            creatorInfo.setPassword(null);
            wxLogin.setCreatorId(creatorInfo.getCreatorId());
            wxLogin.setPhoneNumber(phoneNumber.getPhoneNumber());
            wxLogin.setPurePhoneNumber(phoneNumber.getPurePhoneNumber());
            wxLogin.setCountryCode(phoneNumber.getCountryCode());
            wxLoginService.updateWxLogin(wxLogin);
            return success(creatorInfo);
        } catch (WxErrorException e) {
            return error(e.getError().getErrorMsg());
        }
    }
}

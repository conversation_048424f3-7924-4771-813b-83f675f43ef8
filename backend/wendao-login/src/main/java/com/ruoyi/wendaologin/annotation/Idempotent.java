package com.ruoyi.wendaologin.annotation;

import java.lang.annotation.*;

/**
 * 幂等性注解
 * 用法:
 * // 防止并发重复提交，方法执行完就删除key
 * //@Idempotent(immediateDelete = true)
 * public void submitOrder() {
 *     // 处理订单
 * }
 * // 限制操作频率，等待key自然过期
 * //@Idempotent(expire = 60, immediateDelete = false)
 * public void sendVerificationCode() {
 *     // 发送验证码
 * }
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Idempotent {
    /**
     * 过期时间，单位：秒
     */
    int expire() default 5;
    
    /**
     * 提示信息
     */
    String message() default "请勿重复提交";

    /**
     * 是否在方法执行完后立即删除key
     * true: 立即删除，适用于防止并发重复提交
     * false: 等待自然过期，适用于限制时间间隔的重复提交
     */
    boolean immediateDelete() default false;
} 
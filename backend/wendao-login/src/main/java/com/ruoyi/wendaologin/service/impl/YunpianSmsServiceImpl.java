package com.ruoyi.wendaologin.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.wendaologin.config.YunpianSmsConfig;
import com.ruoyi.wendaologin.model.SmsResult;
import com.ruoyi.wendaologin.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.Map;

@Slf4j
@Service("yunpianSmsService")
public class YunpianSmsServiceImpl implements SmsService {

    @Autowired
    private YunpianSmsConfig smsConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public SmsResult sendCode(String mobile, String code) {
        SmsResult result = new SmsResult();
        result.setSendTime(new Date());

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            // 设置请求参数
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("apikey", smsConfig.getApikey());
            params.add("mobile", mobile);
            params.add("tpl_id", String.valueOf(smsConfig.getTplId()));
            params.add("tpl_value", "#code#=" + code);

            // 发送请求
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            String response = restTemplate.postForObject(smsConfig.getApiUrl(), request, String.class);

            // 解析响应
            Map<String, Object> responseMap = JSON.parseObject(response);

            // 处理响应结果
            if (responseMap != null && responseMap.containsKey("code")) {
                int responseCode = Integer.parseInt(responseMap.get("code").toString());
                if (responseCode == 0) {
                    // 发送成功
                    result.setSuccess(true);
                    result.setCode(0);
                    result.setSid(responseMap.get("sid").toString());
                    // 设置其他字段
                    if (responseMap.containsKey("msg")) {
                        result.setMsg(responseMap.get("msg").toString());
                    }
                } else {
                    // 发送失败
                    result.setSuccess(false);
                    result.setCode(responseCode);
                    result.setMsg(responseMap.get("msg").toString());
                    log.error("短信发送失败：mobile={}, code={}, error={}", mobile, code, responseMap.get("msg"));
                }
            } else {
                // 异常情况：响应中没有code字段
                result.setSuccess(false);
                result.setCode(-1);
                result.setMsg("短信发送异常：响应结果异常");
                log.error("短信发送异常：响应中未包含code字段, mobile={}, response={}", mobile, responseMap);
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setCode(-1);
            result.setMsg("短信发送异常：" + e.getMessage());
            log.error("短信发送异常：mobile={}, code={}", mobile, code, e);
        }

        return result;
    }
} 
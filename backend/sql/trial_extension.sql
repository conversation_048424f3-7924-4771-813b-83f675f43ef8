-- 试用功能扩展SQL脚本

-- 1. 扩展租户表，添加试用相关字段
ALTER TABLE sys_tenant ADD COLUMN trial_start_time datetime COMMENT '试用开始时间';
ALTER TABLE sys_tenant ADD COLUMN trial_end_time datetime COMMENT '试用结束时间';
ALTER TABLE sys_tenant ADD COLUMN trial_status tinyint(1) DEFAULT 0 COMMENT '试用状态：0-未试用，1-试用中，2-试用过期，3-已转正';
ALTER TABLE sys_tenant ADD COLUMN trial_days int DEFAULT 7 COMMENT '试用天数';

-- 2. 创建试用权限配置表
DROP TABLE IF EXISTS sys_trial_permission;
CREATE TABLE sys_trial_permission (
  id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  permission_code varchar(100) NOT NULL COMMENT '权限编码',
  permission_name varchar(100) NOT NULL COMMENT '权限名称',
  permission_type tinyint(1) NOT NULL DEFAULT 1 COMMENT '权限类型：1-读权限，2-写权限',
  trial_available tinyint(1) NOT NULL DEFAULT 1 COMMENT '试用期是否可用：0-不可用，1-可用',
  expired_available tinyint(1) NOT NULL DEFAULT 0 COMMENT '试用过期后是否可用：0-不可用，1-可用',
  create_time datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_permission_code (permission_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='试用权限配置表';

-- 3. 插入默认试用权限配置
INSERT INTO sys_trial_permission (permission_code, permission_name, permission_type, trial_available, expired_available) VALUES
-- 系统管理 - 读权限（试用期和过期后都可用）
('system:user:list', '用户查询', 1, 1, 1),
('system:user:query', '用户详情查询', 1, 1, 1),
('system:user:export', '用户导出', 1, 1, 1),
('system:role:list', '角色查询', 1, 1, 1),
('system:role:query', '角色详情查询', 1, 1, 1),
('system:role:export', '角色导出', 1, 1, 1),
('system:menu:list', '菜单查询', 1, 1, 1),
('system:menu:query', '菜单详情查询', 1, 1, 1),
('system:dept:list', '部门查询', 1, 1, 1),
('system:dept:query', '部门详情查询', 1, 1, 1),
('system:post:list', '岗位查询', 1, 1, 1),
('system:post:query', '岗位详情查询', 1, 1, 1),
('system:post:export', '岗位导出', 1, 1, 1),
('system:dict:list', '字典查询', 1, 1, 1),
('system:dict:query', '字典详情查询', 1, 1, 1),
('system:dict:export', '字典导出', 1, 1, 1),
('system:config:list', '配置查询', 1, 1, 1),
('system:config:query', '配置详情查询', 1, 1, 1),
('system:config:export', '配置导出', 1, 1, 1),
('system:notice:list', '通知查询', 1, 1, 1),
('system:notice:query', '通知详情查询', 1, 1, 1),

-- 系统监控 - 读权限（试用期和过期后都可用）
('monitor:operlog:list', '操作日志查询', 1, 1, 1),
('monitor:operlog:export', '操作日志导出', 1, 1, 1),
('monitor:logininfor:list', '登录日志查询', 1, 1, 1),
('monitor:logininfor:export', '登录日志导出', 1, 1, 1),
('monitor:online:list', '在线用户查询', 1, 1, 1),

-- 系统管理 - 写权限（试用期可用，过期后不可用）
('system:user:add', '用户新增', 2, 1, 0),
('system:user:edit', '用户修改', 2, 1, 0),
('system:user:remove', '用户删除', 2, 1, 0),
('system:user:resetPwd', '重置密码', 2, 1, 0),
('system:user:import', '用户导入', 2, 1, 0),
('system:role:add', '角色新增', 2, 1, 0),
('system:role:edit', '角色修改', 2, 1, 0),
('system:role:remove', '角色删除', 2, 1, 0),
('system:menu:add', '菜单新增', 2, 1, 0),
('system:menu:edit', '菜单修改', 2, 1, 0),
('system:menu:remove', '菜单删除', 2, 1, 0),
('system:dept:add', '部门新增', 2, 1, 0),
('system:dept:edit', '部门修改', 2, 1, 0),
('system:dept:remove', '部门删除', 2, 1, 0),
('system:post:add', '岗位新增', 2, 1, 0),
('system:post:edit', '岗位修改', 2, 1, 0),
('system:post:remove', '岗位删除', 2, 1, 0),
('system:dict:add', '字典新增', 2, 1, 0),
('system:dict:edit', '字典修改', 2, 1, 0),
('system:dict:remove', '字典删除', 2, 1, 0),
('system:config:add', '配置新增', 2, 1, 0),
('system:config:edit', '配置修改', 2, 1, 0),
('system:config:remove', '配置删除', 2, 1, 0),
('system:notice:add', '通知新增', 2, 1, 0),
('system:notice:edit', '通知修改', 2, 1, 0),
('system:notice:remove', '通知删除', 2, 1, 0),

-- 系统监控 - 写权限（试用期可用，过期后不可用）
('monitor:operlog:remove', '操作日志删除', 2, 1, 0),
('monitor:logininfor:remove', '登录日志删除', 2, 1, 0),
('monitor:online:forceLogout', '强制退出', 2, 1, 0); 
package com.ruoyi;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.*;

public class BatchAddAnnotation {
    
    // 需要扫描的包路径
    private static final String[] SCAN_PACKAGES = {
        "com.ruoyi.web.controller.system",
        "com.ruoyi.web.controller.monitor"
    };
    
    // 项目根路径
    private static final String PROJECT_ROOT = "ruoyi-admin/src/main/java/";
    
    // 权限类型映射
    private static final Map<String, Integer> PERMISSION_TYPE_MAP = new HashMap<>();
    static {
        // 读权限 (type = 1)
        PERMISSION_TYPE_MAP.put("list", 1);
        PERMISSION_TYPE_MAP.put("query", 1);
        PERMISSION_TYPE_MAP.put("export", 1);
        
        // 写权限 (type = 2)
        PERMISSION_TYPE_MAP.put("add", 2);
        PERMISSION_TYPE_MAP.put("edit", 2);
        PERMISSION_TYPE_MAP.put("remove", 2);
        PERMISSION_TYPE_MAP.put("import", 2);
        PERMISSION_TYPE_MAP.put("resetPwd", 2);
        PERMISSION_TYPE_MAP.put("changeStatus", 2);
        PERMISSION_TYPE_MAP.put("clean", 2);
        PERMISSION_TYPE_MAP.put("forceLogout", 2);
    }
    
    public static void main(String[] args) {
        try {
            System.out.println("开始批量添加@TrialPermission注解...");
            
            for (String packageName : SCAN_PACKAGES) {
                processPackage(packageName);
            }
            
            System.out.println("批量添加注解完成！");
        } catch (Exception e) {
            System.err.println("处理过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理指定包下的所有Java文件
     */
    private static void processPackage(String packageName) throws IOException {
        String packagePath = packageName.replace(".", "/");
        Path dirPath = Paths.get(PROJECT_ROOT + packagePath);
        
        if (!Files.exists(dirPath)) {
            System.out.println("包路径不存在: " + dirPath);
            return;
        }
        
        System.out.println("处理包: " + packageName);
        
        Files.walk(dirPath)
            .filter(path -> path.toString().endsWith(".java"))
            .filter(path -> path.getFileName().toString().endsWith("Controller.java"))
            .forEach(path -> {
                try {
                    processJavaFile(path);
                } catch (IOException e) {
                    System.err.println("处理文件失败: " + path + ", 错误: " + e.getMessage());
                }
            });
    }
    
    /**
     * 处理单个Java文件
     */
    private static void processJavaFile(Path filePath) throws IOException {
        System.out.println("处理文件: " + filePath.getFileName());
        
        List<String> lines = Files.readAllLines(filePath);
        List<String> newLines = new ArrayList<>();
        
        boolean hasTrialPermissionImport = false;
        boolean needsTrialPermissionImport = false;
        
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            newLines.add(line);
            
            // 检查是否已有TrialPermission导入
            if (line.contains("import com.ruoyi.common.annotation.TrialPermission;")) {
                hasTrialPermissionImport = true;
            }
            
            // 检查@PreAuthorize注解
            if (line.trim().startsWith("@PreAuthorize")) {
                String permissionCode = extractPermissionCode(line);
                if (permissionCode != null) {
                    // 检查下一行是否已经有@TrialPermission注解
                    if (i + 1 < lines.size() && lines.get(i + 1).trim().startsWith("@TrialPermission")) {
                        System.out.println("  跳过已有注解的方法: " + permissionCode);
                        continue;
                    }
                    
                    String trialAnnotation = generateTrialPermissionAnnotation(permissionCode);
                    if (trialAnnotation != null) {
                        newLines.add(trialAnnotation);
                        needsTrialPermissionImport = true;
                        System.out.println("  添加注解: " + permissionCode);
                    }
                }
            }
        }
        
        // 如果需要添加导入语句
        if (needsTrialPermissionImport && !hasTrialPermissionImport) {
            newLines = addTrialPermissionImport(newLines);
            System.out.println("  添加导入语句: TrialPermission");
        }
        
        // 写回文件
        if (needsTrialPermissionImport) {
            Files.write(filePath, newLines);
            System.out.println("  文件更新完成: " + filePath.getFileName());
        } else {
            System.out.println("  文件无需更新: " + filePath.getFileName());
        }
    }
    
    /**
     * 从@PreAuthorize注解中提取权限代码
     */
    private static String extractPermissionCode(String line) {
        // 匹配 @ss.hasPermi('权限代码') 或 @ss.hasPermi("权限代码")
        Pattern pattern = Pattern.compile("@ss\\.hasPermi\\(['\"]([^'\"]+)['\"]\\)");
        Matcher matcher = pattern.matcher(line);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
    
    /**
     * 生成@TrialPermission注解
     */
    private static String generateTrialPermissionAnnotation(String permissionCode) {
        String[] parts = permissionCode.split(":");
        if (parts.length < 3) {
            System.out.println("  权限代码格式不正确: " + permissionCode);
            return null;
        }
        
        String action = parts[parts.length - 1]; // 获取最后一个部分
        Integer type = PERMISSION_TYPE_MAP.get(action);
        
        if (type == null) {
            System.out.println("  未知的权限操作: " + action + " in " + permissionCode);
            return null;
        }
        
        boolean trialAvailable = true;
        boolean expiredAvailable = (type == 1); // 读权限过期后仍可用，写权限不可用
        
        return String.format("    @TrialPermission(value = \"%s\", type = %d, trialAvailable = %s, expiredAvailable = %s)",
                permissionCode, type, trialAvailable, expiredAvailable);
    }
    
    /**
     * 添加TrialPermission导入语句
     */
    private static List<String> addTrialPermissionImport(List<String> lines) {
        List<String> newLines = new ArrayList<>();
        boolean importAdded = false;
        
        for (String line : lines) {
            newLines.add(line);
            
            // 在其他import语句后添加TrialPermission导入
            if (!importAdded && line.startsWith("import") && 
                (line.contains("com.ruoyi.common.annotation.Log") || 
                 line.contains("com.ruoyi.common.core.controller.BaseController"))) {
                newLines.add("import com.ruoyi.common.annotation.TrialPermission;");
                importAdded = true;
            }
        }
        
        // 如果没有找到合适的位置，在package语句后添加
        if (!importAdded) {
            List<String> finalLines = new ArrayList<>();
            for (int i = 0; i < newLines.size(); i++) {
                finalLines.add(newLines.get(i));
                if (newLines.get(i).startsWith("package ")) {
                    finalLines.add("");
                    finalLines.add("import com.ruoyi.common.annotation.TrialPermission;");
                }
            }
            return finalLines;
        }
        
        return newLines;
    }
}

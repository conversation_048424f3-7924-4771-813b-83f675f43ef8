package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.CreatorInfoMapper;
import com.ruoyi.system.domain.CreatorInfo;
import com.ruoyi.system.service.ICreatorInfoService;

/**
 * 创作者信息管理 服务实现
 */
@Service
public class CreatorInfoServiceImpl implements ICreatorInfoService {
    @Autowired
    private CreatorInfoMapper creatorInfoMapper;

    /**
     * 查询创作者信息
     */
    @Override
    public CreatorInfo selectCreatorInfoById(Long id) {
        return creatorInfoMapper.selectCreatorInfoById(id);
    }

    /**
     * 根据创作者ID查询创作者信息
     */
    @Override
    public CreatorInfo selectCreatorInfoByCreatorId(String creatorId) {
        return creatorInfoMapper.selectCreatorInfoByCreatorId(creatorId);
    }

    /**
     * 根据手机号查询创作者信息
     */
    @Override
    public CreatorInfo selectCreatorInfoByPhoneNumber(String phoneNumber) {
        return creatorInfoMapper.selectCreatorInfoByPhoneNumber(phoneNumber);
    }

    /**
     * 根据OpenID查询创作者信息
     */
    @Override
    public CreatorInfo selectCreatorInfoByOpenid(String openid) {
        return creatorInfoMapper.selectCreatorInfoByOpenid(openid);
    }

    /**
     * 根据OpenID和微信小程序APPID查询创作者信息
     */
    @Override
    public CreatorInfo selectCreatorInfoByOpenidAndAppid(String openid, String wxMaAppid) {
        return creatorInfoMapper.selectCreatorInfoByOpenidAndAppid(openid, wxMaAppid);
    }

    /**
     * 查询创作者信息列表
     */
    @Override
    public List<CreatorInfo> selectCreatorInfoList(CreatorInfo creatorInfo) {
        return creatorInfoMapper.selectCreatorInfoList(creatorInfo);
    }

    /**
     * 新增创作者信息
     */
    @Override
    public int insertCreatorInfo(CreatorInfo creatorInfo) {
        // 如果有密码，进行加密
        if (StringUtils.isNotEmpty(creatorInfo.getPassword())) {
            creatorInfo.setPassword(SecurityUtils.encryptPassword(creatorInfo.getPassword()));
        }
        return creatorInfoMapper.insertCreatorInfo(creatorInfo);
    }

    /**
     * 修改创作者信息
     */
    @Override
    public int updateCreatorInfo(CreatorInfo creatorInfo) {
        // 如果有密码，进行加密
        if (StringUtils.isNotEmpty(creatorInfo.getPassword())) {
            creatorInfo.setPassword(SecurityUtils.encryptPassword(creatorInfo.getPassword()));
        }
        return creatorInfoMapper.updateCreatorInfo(creatorInfo);
    }

    /**
     * 删除创作者信息
     */
    @Override
    public int deleteCreatorInfoByIds(Long[] ids) {
        return creatorInfoMapper.deleteCreatorInfoByIds(ids);
    }

    /**
     * 校验创作者ID是否唯一
     */
    @Override
    public boolean checkCreatorIdUnique(String creatorId) {
        CreatorInfo info = creatorInfoMapper.checkCreatorIdUnique(creatorId);
        return StringUtils.isNull(info);
    }

    /**
     * 校验手机号是否唯一
     */
    @Override
    public boolean checkPhoneNumberUnique(String phoneNumber) {
        CreatorInfo info = creatorInfoMapper.checkPhoneNumberUnique(phoneNumber);
        return StringUtils.isNull(info);
    }

    /**
     * 校验OpenID是否唯一
     */
    @Override
    public boolean checkOpenidUnique(String openid) {
        CreatorInfo info = creatorInfoMapper.checkOpenidUnique(openid);
        return StringUtils.isNull(info);
    }

    /**
     * 创作者登录验证
     */
    @Override
    public CreatorInfo login(String phoneNumber, String password) {
        CreatorInfo creatorInfo = creatorInfoMapper.selectCreatorInfoByPhoneNumber(phoneNumber);
        if (StringUtils.isNotNull(creatorInfo) && SecurityUtils.matchesPassword(password, creatorInfo.getPassword())) {
            return creatorInfo;
        }
        return null;
    }

    /**
     * 微信登录验证
     */
    @Override
    public CreatorInfo wxLogin(String openid, String wxMaAppid) {
        return creatorInfoMapper.selectCreatorInfoByOpenidAndAppid(openid, wxMaAppid);
    }

    @Override
    public CreatorInfo selectCreatorInfoByUnionid(String unionid) {
        return creatorInfoMapper.selectCreatorInfoByUnionid(unionid);
    }
}

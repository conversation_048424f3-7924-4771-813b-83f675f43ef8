package com.ruoyi.system.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ruoyi.system.domain.SysTrialPermission;
import com.ruoyi.system.mapper.SysTrialPermissionMapper;

/**
 * 试用权限配置Service单元测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class SysTrialPermissionServiceImplTest {

    @Mock
    private SysTrialPermissionMapper sysTrialPermissionMapper;

    @InjectMocks
    private SysTrialPermissionServiceImpl sysTrialPermissionService;

    private SysTrialPermission testPermission;

    @BeforeEach
    void setUp() {
        testPermission = new SysTrialPermission();
        testPermission.setId(1L);
        testPermission.setPermissionCode("system:user:list");
        testPermission.setPermissionName("用户查询");
        testPermission.setPermissionType(1);
        testPermission.setTrialAvailable(1);
        testPermission.setExpiredAvailable(1);
        testPermission.setCreateTime(new Date());
        testPermission.setUpdateTime(new Date());
    }

    @Test
    void testSelectSysTrialPermissionById() {
        // Given
        Long id = 1L;
        when(sysTrialPermissionMapper.selectSysTrialPermissionById(id)).thenReturn(testPermission);

        // When
        SysTrialPermission result = sysTrialPermissionService.selectSysTrialPermissionById(id);

        // Then
        assertNotNull(result);
        assertEquals(testPermission.getId(), result.getId());
        assertEquals(testPermission.getPermissionCode(), result.getPermissionCode());
        verify(sysTrialPermissionMapper, times(1)).selectSysTrialPermissionById(id);
    }

    @Test
    void testSelectSysTrialPermissionById_NotFound() {
        // Given
        Long id = 999L;
        when(sysTrialPermissionMapper.selectSysTrialPermissionById(id)).thenReturn(null);

        // When
        SysTrialPermission result = sysTrialPermissionService.selectSysTrialPermissionById(id);

        // Then
        assertNull(result);
        verify(sysTrialPermissionMapper, times(1)).selectSysTrialPermissionById(id);
    }

    @Test
    void testSelectByPermissionCode() {
        // Given
        String permissionCode = "system:user:list";
        when(sysTrialPermissionMapper.selectByPermissionCode(permissionCode)).thenReturn(testPermission);

        // When
        SysTrialPermission result = sysTrialPermissionService.selectByPermissionCode(permissionCode);

        // Then
        assertNotNull(result);
        assertEquals(testPermission.getPermissionCode(), result.getPermissionCode());
        verify(sysTrialPermissionMapper, times(1)).selectByPermissionCode(permissionCode);
    }

    @Test
    void testSelectByPermissionCode_NotFound() {
        // Given
        String permissionCode = "nonexistent:permission";
        when(sysTrialPermissionMapper.selectByPermissionCode(permissionCode)).thenReturn(null);

        // When
        SysTrialPermission result = sysTrialPermissionService.selectByPermissionCode(permissionCode);

        // Then
        assertNull(result);
        verify(sysTrialPermissionMapper, times(1)).selectByPermissionCode(permissionCode);
    }

    @Test
    void testSelectSysTrialPermissionList() {
        // Given
        SysTrialPermission queryParam = new SysTrialPermission();
        queryParam.setPermissionType(1);
        
        List<SysTrialPermission> expectedList = Arrays.asList(testPermission);
        when(sysTrialPermissionMapper.selectSysTrialPermissionList(queryParam)).thenReturn(expectedList);

        // When
        List<SysTrialPermission> result = sysTrialPermissionService.selectSysTrialPermissionList(queryParam);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testPermission.getId(), result.get(0).getId());
        verify(sysTrialPermissionMapper, times(1)).selectSysTrialPermissionList(queryParam);
    }

    @Test
    void testInsertSysTrialPermission() {
        // Given
        SysTrialPermission newPermission = new SysTrialPermission();
        newPermission.setPermissionCode("system:user:add");
        newPermission.setPermissionName("用户新增");
        newPermission.setPermissionType(2);
        newPermission.setTrialAvailable(1);
        newPermission.setExpiredAvailable(0);
        
        when(sysTrialPermissionMapper.insertSysTrialPermission(newPermission)).thenReturn(1);

        // When
        int result = sysTrialPermissionService.insertSysTrialPermission(newPermission);

        // Then
        assertEquals(1, result);
        verify(sysTrialPermissionMapper, times(1)).insertSysTrialPermission(newPermission);
    }

    @Test
    void testInsertSysTrialPermission_Failed() {
        // Given
        SysTrialPermission newPermission = new SysTrialPermission();
        when(sysTrialPermissionMapper.insertSysTrialPermission(newPermission)).thenReturn(0);

        // When
        int result = sysTrialPermissionService.insertSysTrialPermission(newPermission);

        // Then
        assertEquals(0, result);
        verify(sysTrialPermissionMapper, times(1)).insertSysTrialPermission(newPermission);
    }

    @Test
    void testUpdateSysTrialPermission() {
        // Given
        testPermission.setPermissionName("用户查询-更新");
        when(sysTrialPermissionMapper.updateSysTrialPermission(testPermission)).thenReturn(1);

        // When
        int result = sysTrialPermissionService.updateSysTrialPermission(testPermission);

        // Then
        assertEquals(1, result);
        verify(sysTrialPermissionMapper, times(1)).updateSysTrialPermission(testPermission);
    }

    @Test
    void testUpdateSysTrialPermission_NotFound() {
        // Given
        testPermission.setId(999L);
        when(sysTrialPermissionMapper.updateSysTrialPermission(testPermission)).thenReturn(0);

        // When
        int result = sysTrialPermissionService.updateSysTrialPermission(testPermission);

        // Then
        assertEquals(0, result);
        verify(sysTrialPermissionMapper, times(1)).updateSysTrialPermission(testPermission);
    }

    @Test
    void testDeleteSysTrialPermissionById() {
        // Given
        Long id = 1L;
        when(sysTrialPermissionMapper.deleteSysTrialPermissionById(id)).thenReturn(1);

        // When
        int result = sysTrialPermissionService.deleteSysTrialPermissionById(id);

        // Then
        assertEquals(1, result);
        verify(sysTrialPermissionMapper, times(1)).deleteSysTrialPermissionById(id);
    }

    @Test
    void testDeleteSysTrialPermissionById_NotFound() {
        // Given
        Long id = 999L;
        when(sysTrialPermissionMapper.deleteSysTrialPermissionById(id)).thenReturn(0);

        // When
        int result = sysTrialPermissionService.deleteSysTrialPermissionById(id);

        // Then
        assertEquals(0, result);
        verify(sysTrialPermissionMapper, times(1)).deleteSysTrialPermissionById(id);
    }

    @Test
    void testDeleteSysTrialPermissionByIds() {
        // Given
        Long[] ids = {1L, 2L, 3L};
        when(sysTrialPermissionMapper.deleteSysTrialPermissionByIds(ids)).thenReturn(3);

        // When
        int result = sysTrialPermissionService.deleteSysTrialPermissionByIds(ids);

        // Then
        assertEquals(3, result);
        verify(sysTrialPermissionMapper, times(1)).deleteSysTrialPermissionByIds(ids);
    }

    @Test
    void testDeleteSysTrialPermissionByIds_PartialSuccess() {
        // Given
        Long[] ids = {1L, 2L, 999L};
        when(sysTrialPermissionMapper.deleteSysTrialPermissionByIds(ids)).thenReturn(2);

        // When
        int result = sysTrialPermissionService.deleteSysTrialPermissionByIds(ids);

        // Then
        assertEquals(2, result);
        verify(sysTrialPermissionMapper, times(1)).deleteSysTrialPermissionByIds(ids);
    }

    @Test
    void testDeleteSysTrialPermissionByIds_EmptyArray() {
        // Given
        Long[] ids = {};
        when(sysTrialPermissionMapper.deleteSysTrialPermissionByIds(ids)).thenReturn(0);

        // When
        int result = sysTrialPermissionService.deleteSysTrialPermissionByIds(ids);

        // Then
        assertEquals(0, result);
        verify(sysTrialPermissionMapper, times(1)).deleteSysTrialPermissionByIds(ids);
    }
} 
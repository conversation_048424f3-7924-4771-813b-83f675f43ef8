package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.WxLogin;
import org.apache.ibatis.annotations.Param;

/**
 * 微信扫码登录 数据层
 */
public interface WxLoginMapper {
    /**
     * 查询微信登录信息
     */
    public WxLogin selectWxLoginById(Long id);

    /**
     * 根据UUID查询微信登录信息
     */
    public WxLogin selectWxLoginByUuid(String uuid);

    /**
     * 根据创作者ID查询微信登录信息
     */
    public WxLogin selectWxLoginByCreatorId(String creatorId);

    /**
     * 根据OpenID查询微信登录信息
     */
    public WxLogin selectWxLoginByOpenid(String openid);

    /**
     * 根据UnionId查询微信登录信息
     */
    public WxLogin selectWxLoginByUnionid(String unionid);

    /**
     * 查询微信登录信息列表
     */
    public List<WxLogin> selectWxLoginList(WxLogin wxLogin);

    /**
     * 新增微信登录信息
     */
    public int insertWxLogin(WxLogin wxLogin);

    /**
     * 修改微信登录信息
     */
    public int updateWxLogin(WxLogin wxLogin);

    /**
     * 删除微信登录信息
     */
    public int deleteWxLoginById(Long id);

    /**
     * 批量删除微信登录信息
     */
    public int deleteWxLoginByIds(Long[] ids);

    /**
     * 删除过期的微信登录记录
     */
    public int deleteExpiredWxLogin();

    /**
     * 校验UUID是否唯一
     */
    public WxLogin checkUuidUnique(String uuid);

    /**
     * 根据创作者ID和微信小程序APPID查询最新的登录记录
     */
    public WxLogin selectLatestWxLoginByCreatorIdAndAppid(@Param("creatorId") String creatorId, @Param("wxMaAppid") String wxMaAppid);
}

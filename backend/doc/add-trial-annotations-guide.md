# 为控制器添加试用权限注解指南

## 概述

本文档说明如何为 system 和 monitor 目录下的所有控制器添加 `@TrialPermission` 注解。

## 注解规则

### 1. 权限类型分类

- **读权限（type=1）**：查询、导出、获取详情等操作
  - `trialAvailable = true`：试用期可用
  - `expiredAvailable = true`：试用过期后仍可用

- **写权限（type=2）**：新增、修改、删除、导入等操作
  - `trialAvailable = true`：试用期可用
  - `expiredAvailable = false`：试用过期后不可用

### 2. 注解格式

```java
// 读权限示例
@TrialPermission(value = "system:user:list", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限示例
@TrialPermission(value = "system:user:add", type = 2, trialAvailable = true, expiredAvailable = false)
```

## 需要添加注解的控制器

### System 目录

#### 1. SysUserController.java ✅ (已完成)
- 已添加所有方法的 `@TrialPermission` 注解

#### 2. SysRoleController.java
```java
// 需要添加的导入
import com.ruoyi.common.annotation.TrialPermission;

// 读权限方法
@TrialPermission(value = "system:role:list", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:role:query", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:role:export", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限方法
@TrialPermission(value = "system:role:add", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:role:edit", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:role:remove", type = 2, trialAvailable = true, expiredAvailable = false)
```

#### 3. SysMenuController.java
```java
// 读权限
@TrialPermission(value = "system:menu:list", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:menu:query", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限
@TrialPermission(value = "system:menu:add", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:menu:edit", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:menu:remove", type = 2, trialAvailable = true, expiredAvailable = false)
```

#### 4. SysDeptController.java
```java
// 读权限
@TrialPermission(value = "system:dept:list", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:dept:query", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限
@TrialPermission(value = "system:dept:add", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:dept:edit", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:dept:remove", type = 2, trialAvailable = true, expiredAvailable = false)
```

#### 5. SysPostController.java
```java
// 读权限
@TrialPermission(value = "system:post:list", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:post:query", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:post:export", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限
@TrialPermission(value = "system:post:add", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:post:edit", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:post:remove", type = 2, trialAvailable = true, expiredAvailable = false)
```

#### 6. SysDictTypeController.java & SysDictDataController.java
```java
// 读权限
@TrialPermission(value = "system:dict:list", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:dict:query", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:dict:export", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限
@TrialPermission(value = "system:dict:add", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:dict:edit", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:dict:remove", type = 2, trialAvailable = true, expiredAvailable = false)
```

#### 7. SysNoticeController.java
```java
// 读权限
@TrialPermission(value = "system:notice:list", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "system:notice:query", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限
@TrialPermission(value = "system:notice:add", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:notice:edit", type = 2, trialAvailable = true, expiredAvailable = false)
@TrialPermission(value = "system:notice:remove", type = 2, trialAvailable = true, expiredAvailable = false)
```

### Monitor 目录

#### 1. SysOperlogController.java ✅ (已完成)
- 已添加所有方法的 `@TrialPermission` 注解

#### 2. SysLogininforController.java
```java
// 需要添加的导入
import com.ruoyi.common.annotation.TrialPermission;

// 读权限
@TrialPermission(value = "monitor:logininfor:list", type = 1, trialAvailable = true, expiredAvailable = true)
@TrialPermission(value = "monitor:logininfor:export", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限
@TrialPermission(value = "monitor:logininfor:remove", type = 2, trialAvailable = true, expiredAvailable = false)
```

#### 3. SysUserOnlineController.java
```java
// 读权限
@TrialPermission(value = "monitor:online:list", type = 1, trialAvailable = true, expiredAvailable = true)

// 写权限
@TrialPermission(value = "monitor:online:forceLogout", type = 2, trialAvailable = true, expiredAvailable = false)
```

## 实施步骤

### 1. 添加导入语句
在每个控制器文件的导入部分添加：
```java
import com.ruoyi.common.annotation.TrialPermission;
```

### 2. 为每个方法添加注解
根据方法的 `@PreAuthorize` 注解中的权限字符串，添加对应的 `@TrialPermission` 注解。

### 3. 权限类型判断规则
- **GET 请求**：通常是读权限（type=1）
- **POST/PUT/DELETE 请求**：通常是写权限（type=2）
- **导出操作**：虽然是POST请求，但属于读权限（type=1）
- **导入操作**：属于写权限（type=2）

### 4. 注解位置
将 `@TrialPermission` 注解放在 `@PreAuthorize` 注解之后，其他注解之前：

```java
@PreAuthorize("@ss.hasPermi('system:user:list')")
@TrialPermission(value = "system:user:list", type = 1, trialAvailable = true, expiredAvailable = true)
@GetMapping("/list")
public TableDataInfo list(SysUser user) {
    // 方法实现
}
```

## 验证方法

### 1. 编译检查
确保添加注解后项目能正常编译。

### 2. 功能测试
- 创建试用租户，测试试用期间的权限
- 设置租户为过期状态，测试过期后的权限限制
- 验证读权限在过期后仍可用，写权限被限制

### 3. 日志检查
观察权限验证的日志输出，确认注解生效。

## 注意事项

1. **权限字符串一致性**：确保 `@TrialPermission` 的 value 值与 `@PreAuthorize` 中的权限字符串一致
2. **导入语句**：不要忘记添加 `@TrialPermission` 的导入语句
3. **权限类型**：仔细区分读权限和写权限，避免分类错误
4. **数据库配置**：确保 SQL 脚本中已包含所有权限的配置
5. **测试覆盖**：对每个添加注解的方法进行功能测试

## 批量处理建议

由于需要修改的文件较多，建议：
1. 先完成一个控制器的所有注解添加
2. 进行测试验证
3. 确认无误后，按相同模式处理其他控制器
4. 定期提交代码，避免一次性修改过多文件

这样可以确保修改的正确性，并便于问题定位和回滚。 
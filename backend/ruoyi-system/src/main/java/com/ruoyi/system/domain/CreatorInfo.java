package com.ruoyi.system.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Pattern;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 创作者信息对象 creator_info
 */
public class CreatorInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId
    private Long id;

    /** 创作者id,uuid去除-后的值 */
    @Excel(name = "创作者ID")
    private String creatorId;

    /** 头像地址 */
    @Excel(name = "头像地址")
    private String avatarUrl;

    /** 密码6-16个字,同时包含数字和字母 */
    private String password;

    /** 微信的openid */
    @Excel(name = "微信OpenID")
    private String openid;

    /** 微信的unionid */
    @Excel(name = "微信UnionID")
    private String unionid;

    /** 用户微信账号,即手机号phone_number或则微信昵称 */
    @Excel(name = "微信账号")
    private String wxAccount;

    /** 用户绑定的手机号（国外手机号会有区号）手机登录使用这个号码 */
    @Excel(name = "手机号")
    private String phoneNumber;

    /** 没有国家区号的手机号 */
    @Excel(name = "纯手机号")
    private String purePhoneNumber;

    /** 国家区号 */
    @Excel(name = "国家区号")
    private String countryCode;

    /** 登录的微信小程序appid */
    @Excel(name = "微信小程序APPID")
    private String wxMaAppid;

    /** 微信昵称（含emoji） */
    @Excel(name = "微信昵称")
    private String wxNickName;

    /** 微信头像URL */
    @Excel(name = "微信头像URL")
    private String wxHeadimgurl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @NotBlank(message = "创作者ID不能为空")
    @Size(min = 0, max = 64, message = "创作者ID长度不能超过64个字符")
    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    @Size(min = 0, max = 255, message = "头像地址长度不能超过255个字符")
    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-zA-Z]).{6,16}$", message = "密码必须是6-16位，同时包含数字和字母")
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Size(min = 0, max = 128, message = "微信OpenID长度不能超过128个字符")
    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    @Size(min = 0, max = 128, message = "微信UnionID长度不能超过128个字符")
    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    @Size(min = 0, max = 128, message = "微信账号长度不能超过128个字符")
    public String getWxAccount() {
        return wxAccount;
    }

    public void setWxAccount(String wxAccount) {
        this.wxAccount = wxAccount;
    }

    @Size(min = 0, max = 30, message = "手机号长度不能超过30个字符")
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Size(min = 0, max = 30, message = "纯手机号长度不能超过30个字符")
    public String getPurePhoneNumber() {
        return purePhoneNumber;
    }

    public void setPurePhoneNumber(String purePhoneNumber) {
        this.purePhoneNumber = purePhoneNumber;
    }

    @Size(min = 0, max = 10, message = "国家区号长度不能超过10个字符")
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    @Size(min = 0, max = 32, message = "微信小程序APPID长度不能超过32个字符")
    public String getWxMaAppid() {
        return wxMaAppid;
    }

    public void setWxMaAppid(String wxMaAppid) {
        this.wxMaAppid = wxMaAppid;
    }

    @Size(min = 0, max = 100, message = "微信昵称长度不能超过100个字符")
    public String getWxNickName() {
        return wxNickName;
    }

    public void setWxNickName(String wxNickName) {
        this.wxNickName = wxNickName;
    }

    @Size(min = 0, max = 500, message = "微信头像URL长度不能超过500个字符")
    public String getWxHeadimgurl() {
        return wxHeadimgurl;
    }

    public void setWxHeadimgurl(String wxHeadimgurl) {
        this.wxHeadimgurl = wxHeadimgurl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("creatorId", getCreatorId())
                .append("avatarUrl", getAvatarUrl())
                .append("password", "******")
                .append("openid", getOpenid())
                .append("unionid", getUnionid())
                .append("wxAccount", getWxAccount())
                .append("phoneNumber", getPhoneNumber())
                .append("purePhoneNumber", getPurePhoneNumber())
                .append("countryCode", getCountryCode())
                .append("wxMaAppid", getWxMaAppid())
                .append("wxNickName", getWxNickName())
                .append("wxHeadimgurl", getWxHeadimgurl())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

package com.ruoyi.system.service;

import java.util.List;
import java.util.Set;
import com.ruoyi.system.domain.SysTenantIgnoreTable;

/**
 * 租户忽略表配置 服务层
 * 
 * <AUTHOR>
 */
public interface ISysTenantIgnoreTableService
{
    /**
     * 查询租户忽略表配置信息
     * 
     * @param id 主键ID
     * @return 租户忽略表配置信息
     */
    public SysTenantIgnoreTable selectTenantIgnoreTableById(Long id);

    /**
     * 查询租户忽略表配置列表
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 租户忽略表配置集合
     */
    public List<SysTenantIgnoreTable> selectTenantIgnoreTableList(SysTenantIgnoreTable sysTenantIgnoreTable);

    /**
     * 获取所有有效的忽略表名集合
     * 
     * @return 表名集合
     */
    public Set<String> getActiveIgnoreTableNames();

    /**
     * 新增租户忽略表配置
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 结果
     */
    public int insertTenantIgnoreTable(SysTenantIgnoreTable sysTenantIgnoreTable);

    /**
     * 修改租户忽略表配置
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 结果
     */
    public int updateTenantIgnoreTable(SysTenantIgnoreTable sysTenantIgnoreTable);

    /**
     * 批量删除租户忽略表配置
     * 
     * @param ids 需要删除的主键ID
     */
    public void deleteTenantIgnoreTableByIds(Long[] ids);

    /**
     * 校验表名是否唯一
     * 
     * @param sysTenantIgnoreTable 租户忽略表配置信息
     * @return 结果
     */
    public boolean checkTableNameUnique(SysTenantIgnoreTable sysTenantIgnoreTable);

    /**
     * 加载忽略表缓存数据
     */
    public void loadingIgnoreTableCache();

    /**
     * 清空忽略表缓存数据
     */
    public void clearIgnoreTableCache();

    /**
     * 重置忽略表缓存数据
     */
    public void resetIgnoreTableCache();
} 
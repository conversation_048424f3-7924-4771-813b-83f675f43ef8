package com.ruoyi.framework.config;

import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.ruoyi.common.utils.http.HttpUtils;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class WxMiniAppConfigBean extends WxMaDefaultConfigImpl {
    private static final String key = "87er8wefsd5f4e8wr8ew78rew";
    @Override
    public String getAccessToken() {
        String url  = "https://goodminiapp.wendao101.com/kt_access_token_controller/getKtWxToken?requestKey="+key;
        return HttpUtils.sendGet(url);
    }

    @Override
    public boolean isAccessTokenExpired() {
        return false;
    }
}

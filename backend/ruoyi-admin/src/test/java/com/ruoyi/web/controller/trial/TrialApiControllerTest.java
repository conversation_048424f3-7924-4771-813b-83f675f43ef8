package com.ruoyi.web.controller.trial;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.TrialPermissionService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.domain.SysTrialPermission;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.ISysTrialPermissionService;
import com.ruoyi.web.controller.trial.TrialApiController.TrialPermissionRequest;

/**
 * 试用权限API控制器单元测试
 * 
 * <AUTHOR>
 */
@SpringBootTest
@AutoConfigureMockMvc
@RunWith(SpringRunner.class)
public class TrialApiControllerTest {

    @Mock
    private TrialPermissionService trialPermissionService;

    @Mock
    private ISysTenantService tenantService;

    @Mock
    private ISysTrialPermissionService trialPermissionConfigService;

    @InjectMocks
    private TrialApiController trialApiController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    private SysUser testUser;
    private SysTenant testTenant;
    private SysTrialPermission testPermission;
    private LoginUser testLoginUser;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(trialApiController).build();
        objectMapper = new ObjectMapper();

        // 初始化测试数据
        testUser = new SysUser();
        testUser.setUserId(1L);
        testUser.setUserName("testuser");
        testUser.setShopId("tenant001");

        testLoginUser = new LoginUser();
        testLoginUser.setUser(testUser);

        testTenant = new SysTenant();
        testTenant.setShopId("tenant001");
        testTenant.setTenantName("测试租户");
        testTenant.setTrialStatus(1);
        testTenant.setTrialDays(7);
        testTenant.setTrialStartTime(new Date());
        testTenant.setTrialEndTime(new Date(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L));

        testPermission = new SysTrialPermission();
        testPermission.setId(1L);
        testPermission.setPermissionCode("system:user:list");
        testPermission.setPermissionName("用户查询");
        testPermission.setPermissionType(1);
        testPermission.setTrialAvailable(1);
        testPermission.setExpiredAvailable(1);
    }

    @Test
    public void testCheckPermission_Success() throws Exception {
        // 准备测试数据
        TrialPermissionRequest request = new TrialPermissionRequest();
        request.setPermission("system:user:list");
        request.setType(1);

        // Mock 服务方法
        when(trialPermissionService.hasTrialPermi("system:user:list")).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/trial/checkPermission")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value(true));

        // 验证方法调用
        verify(trialPermissionService).hasTrialPermi("system:user:list");
    }

    @Test
    public void testCheckPermission_EmptyPermission() throws Exception {
        // 准备测试数据
        TrialPermissionRequest request = new TrialPermissionRequest();
        request.setPermission("");
        request.setType(1);

        // 执行测试
        mockMvc.perform(post("/trial/checkPermission")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("权限代码不能为空"));
    }

    @Test
    public void testCheckPermission_ServiceException() throws Exception {
        // 准备测试数据
        TrialPermissionRequest request = new TrialPermissionRequest();
        request.setPermission("system:user:list");
        request.setType(1);

        // Mock 服务方法抛出异常
        when(trialPermissionService.hasTrialPermi("system:user:list"))
                .thenThrow(new RuntimeException("Service error"));

        // 执行测试
        mockMvc.perform(post("/trial/checkPermission")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("权限检查失败"));
    }

    @Test
    public void testGetTenantInfo_Success() throws Exception {
        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // Mock 服务方法
            when(tenantService.selectTenantById("tenant001")).thenReturn(testTenant);

            // 执行测试
            mockMvc.perform(get("/trial/tenantInfo"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.data.tenantId").value("tenant001"))
                    .andExpect(jsonPath("$.data.tenantName").value("测试租户"))
                    .andExpect(jsonPath("$.data.trialStatus").value(1));

            // 验证方法调用
            verify(tenantService).selectTenantById("tenant001");
        }
    }

    @Test
    public void testGetTenantInfo_UserNotFound() throws Exception {
        // Mock SecurityUtils 返回空用户
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            LoginUser emptyLoginUser = new LoginUser();
            emptyLoginUser.setUser(null);
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(emptyLoginUser);

            // 执行测试
            mockMvc.perform(get("/trial/tenantInfo"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("用户信息异常"));
        }
    }

    @Test
    public void testGetTenantInfo_TenantNotFound() throws Exception {
        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // Mock 服务方法返回空
            when(tenantService.selectTenantById("tenant001")).thenReturn(null);

            // 执行测试
            mockMvc.perform(get("/trial/tenantInfo"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("租户信息不存在"));
        }
    }

    @Test
    public void testApplyTrial_Success() throws Exception {
        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // 设置租户为未试用状态
            testTenant.setTrialStatus(0);
            when(tenantService.selectTenantById("tenant001")).thenReturn(testTenant);
            when(trialPermissionService.startTrial("tenant001", 7)).thenReturn(true);

            // 执行测试
            mockMvc.perform(post("/trial/apply")
                    .param("trialDays", "7"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.msg").value("试用申请成功，试用期为 7 天"));

            // 验证方法调用
            verify(tenantService).selectTenantById("tenant001");
            verify(trialPermissionService).startTrial("tenant001", 7);
        }
    }

    @Test
    public void testApplyTrial_AlreadyApplied() throws Exception {
        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // 设置租户为已试用状态
            testTenant.setTrialStatus(1);
            when(tenantService.selectTenantById("tenant001")).thenReturn(testTenant);

            // 执行测试
            mockMvc.perform(post("/trial/apply")
                    .param("trialDays", "7"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("该租户已经申请过试用"));
        }
    }

    @Test
    public void testApplyTrial_StartTrialFailed() throws Exception {
        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // 设置租户为未试用状态
            testTenant.setTrialStatus(0);
            when(tenantService.selectTenantById("tenant001")).thenReturn(testTenant);
            when(trialPermissionService.startTrial("tenant001", 7)).thenReturn(false);

            // 执行测试
            mockMvc.perform(post("/trial/apply")
                    .param("trialDays", "7"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(500))
                    .andExpect(jsonPath("$.msg").value("试用申请失败"));
        }
    }

    @Test
    public void testGetTrialPermissions_Success() throws Exception {
        // 准备测试数据
        List<SysTrialPermission> permissions = Arrays.asList(testPermission);

        // Mock 服务方法
        when(trialPermissionConfigService.selectSysTrialPermissionList(any(SysTrialPermission.class)))
                .thenReturn(permissions);

        // 执行测试
        mockMvc.perform(get("/trial/permissions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].permissionCode").value("system:user:list"));

        // 验证方法调用
        verify(trialPermissionConfigService).selectSysTrialPermissionList(any(SysTrialPermission.class));
    }

    @Test
    public void testGetTrialPermissions_ServiceException() throws Exception {
        // Mock 服务方法抛出异常
        when(trialPermissionConfigService.selectSysTrialPermissionList(any(SysTrialPermission.class)))
                .thenThrow(new RuntimeException("Database error"));

        // 执行测试
        mockMvc.perform(get("/trial/permissions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("获取权限列表失败"));
    }

    @Test
    public void testGetTrialStatistics_TrialActive() throws Exception {
        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // 准备测试数据 - 试用中
            testTenant.setTrialStatus(1);
            when(tenantService.selectTenantById("tenant001")).thenReturn(testTenant);

            // 准备权限数据
            SysTrialPermission readPermission = new SysTrialPermission();
            readPermission.setPermissionType(1);
            readPermission.setExpiredAvailable(1);

            SysTrialPermission writePermission = new SysTrialPermission();
            writePermission.setPermissionType(2);
            writePermission.setExpiredAvailable(0);

            List<SysTrialPermission> permissions = Arrays.asList(readPermission, writePermission);
            when(trialPermissionConfigService.selectSysTrialPermissionList(any(SysTrialPermission.class)))
                    .thenReturn(permissions);

            // 执行测试
            mockMvc.perform(get("/trial/statistics"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.data.tenantId").value("tenant001"))
                    .andExpect(jsonPath("$.data.trialStatus").value(1))
                    .andExpect(jsonPath("$.data.totalReadPermissions").value(1))
                    .andExpect(jsonPath("$.data.totalWritePermissions").value(1))
                    .andExpect(jsonPath("$.data.availableReadPermissions").value(1))
                    .andExpect(jsonPath("$.data.availableWritePermissions").value(1));
        }
    }

    @Test
    public void testGetTrialStatistics_TrialExpired() throws Exception {
        // Mock SecurityUtils
        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getLoginUser).thenReturn(testLoginUser);

            // 准备测试数据 - 试用过期
            testTenant.setTrialStatus(2);
            when(tenantService.selectTenantById("tenant001")).thenReturn(testTenant);

            // 准备权限数据
            SysTrialPermission readPermission = new SysTrialPermission();
            readPermission.setPermissionType(1);
            readPermission.setExpiredAvailable(1);

            SysTrialPermission writePermission = new SysTrialPermission();
            writePermission.setPermissionType(2);
            writePermission.setExpiredAvailable(0);

            List<SysTrialPermission> permissions = Arrays.asList(readPermission, writePermission);
            when(trialPermissionConfigService.selectSysTrialPermissionList(any(SysTrialPermission.class)))
                    .thenReturn(permissions);

            // 执行测试
            mockMvc.perform(get("/trial/statistics"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200))
                    .andExpect(jsonPath("$.data.tenantId").value("tenant001"))
                    .andExpect(jsonPath("$.data.trialStatus").value(2))
                    .andExpect(jsonPath("$.data.totalReadPermissions").value(1))
                    .andExpect(jsonPath("$.data.totalWritePermissions").value(1))
                    .andExpect(jsonPath("$.data.availableReadPermissions").value(1))
                    .andExpect(jsonPath("$.data.availableWritePermissions").value(0));
        }
    }
} 
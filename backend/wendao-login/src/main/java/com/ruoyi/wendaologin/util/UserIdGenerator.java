package com.ruoyi.wendaologin.util;

import java.security.SecureRandom;

public class UserIdGenerator {
    
    private static final String LOWERCASE_CHARACTERS = "abcdefghijklmnopqrstuvwxyz0123456789";
    private static final int USER_ID_LENGTH = 24; // 用户ID长度
    private static final SecureRandom random = new SecureRandom();

    /**
     * 生成随机用户ID
     *
     * @return 随机生成的用户ID，前缀为 b_u_
     */
    public static String generateUserId() {
        StringBuilder userId = new StringBuilder("b_u_"); // 添加前缀
        
        // 确保第一个字符是小写字母
        int index = random.nextInt(26); // 26个小写字母
        userId.append(LOWERCASE_CHARACTERS.charAt(index));
        
        // 生成剩余字符
        for (int i = 1; i < USER_ID_LENGTH - 3; i++) { // 减去前缀长度
            index = random.nextInt(LOWERCASE_CHARACTERS.length());
            userId.append(LOWERCASE_CHARACTERS.charAt(index));
        }
        return userId.toString();
    }
} 
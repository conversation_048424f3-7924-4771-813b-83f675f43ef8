package com.ruoyi.framework.task;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.ruoyi.framework.web.service.TrialPermissionService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.service.ISysTenantService;

/**
 * 试用状态检查定时任务单元测试
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TrialStatusTaskTest {

    @Mock
    private ISysTenantService tenantService;

    @Mock
    private TrialPermissionService trialPermissionService;

    @InjectMocks
    private TrialStatusTask trialStatusTask;

    private SysTenant trialTenant;
    private SysTenant expiredTenant;

    @Before
    public void setUp() {
        // 设置试用中的租户
        trialTenant = new SysTenant();
        trialTenant.setShopId("tenant001");
        trialTenant.setTenantName("试用租户");
        trialTenant.setTrialStatus(1); // 试用中
        trialTenant.setTrialStartTime(new Date(System.currentTimeMillis() - 5 * 24 * 60 * 60 * 1000L)); // 5天前开始
        trialTenant.setTrialEndTime(new Date(System.currentTimeMillis() + 2 * 24 * 60 * 60 * 1000L)); // 2天后结束
        trialTenant.setTrialDays(7);

        // 设置已过期的租户
        expiredTenant = new SysTenant();
        expiredTenant.setShopId("tenant002");
        expiredTenant.setTenantName("过期租户");
        expiredTenant.setTrialStatus(1); // 试用中但已过期
        expiredTenant.setTrialStartTime(new Date(System.currentTimeMillis() - 8 * 24 * 60 * 60 * 1000L)); // 8天前开始
        expiredTenant.setTrialEndTime(new Date(System.currentTimeMillis() - 1 * 24 * 60 * 60 * 1000L)); // 1天前过期
        expiredTenant.setTrialDays(7);
    }

    @Test
    public void testCheckTrialExpiredStatus_WithExpiredTenants() {
        // Given
        List<SysTenant> trialTenants = Arrays.asList(trialTenant, expiredTenant);
        
        when(tenantService.selectTenantList(any(SysTenant.class))).thenReturn(trialTenants);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(1);
        doNothing().when(trialPermissionService).clearTenantTrialCache(anyString());

        // When
        trialStatusTask.checkTrialExpiredStatus();

        // Then
        // 验证查询试用中的租户
        verify(tenantService).selectTenantList(argThat(tenant -> 
            tenant.getTrialStatus() != null && tenant.getTrialStatus().equals(1)
        ));
        
        // 验证只有过期的租户被更新状态
        verify(tenantService).updateTenant(argThat(tenant -> 
            tenant.getShopId().equals("tenant002") && tenant.getTrialStatus().equals(2)
        ));
        
        // 验证清除了过期租户的缓存
        verify(trialPermissionService).clearTenantTrialCache("tenant002");
        
        // 验证未过期的租户没有被更新
        verify(tenantService, never()).updateTenant(argThat(tenant -> 
            tenant.getShopId().equals("tenant001")
        ));
    }

    @Test
    public void testCheckTrialExpiredStatus_NoExpiredTenants() {
        // Given
        List<SysTenant> trialTenants = Arrays.asList(trialTenant); // 只有未过期的租户
        
        when(tenantService.selectTenantList(any(SysTenant.class))).thenReturn(trialTenants);

        // When
        trialStatusTask.checkTrialExpiredStatus();

        // Then
        verify(tenantService).selectTenantList(any(SysTenant.class));
        verify(tenantService, never()).updateTenant(any(SysTenant.class));
        verify(trialPermissionService, never()).clearTenantTrialCache(anyString());
    }

    @Test
    public void testCheckTrialExpiredStatus_EmptyTenantList() {
        // Given
        when(tenantService.selectTenantList(any(SysTenant.class))).thenReturn(Arrays.asList());

        // When
        trialStatusTask.checkTrialExpiredStatus();

        // Then
        verify(tenantService).selectTenantList(any(SysTenant.class));
        verify(tenantService, never()).updateTenant(any(SysTenant.class));
        verify(trialPermissionService, never()).clearTenantTrialCache(anyString());
    }

    @Test
    public void testCheckTrialExpiredStatus_UpdateFailed() {
        // Given
        List<SysTenant> trialTenants = Arrays.asList(expiredTenant);
        
        when(tenantService.selectTenantList(any(SysTenant.class))).thenReturn(trialTenants);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(0); // 更新失败

        // When
        trialStatusTask.checkTrialExpiredStatus();

        // Then
        verify(tenantService).updateTenant(any(SysTenant.class));
        // 即使更新失败，也不应该清除缓存
        verify(trialPermissionService, never()).clearTenantTrialCache(anyString());
    }

    @Test
    public void testCheckTrialExpiredStatus_ExceptionHandling() {
        // Given
        when(tenantService.selectTenantList(any(SysTenant.class))).thenThrow(new RuntimeException("数据库异常"));

        // When
        trialStatusTask.checkTrialExpiredStatus();

        // Then
        // 验证异常被捕获，方法正常结束
        verify(tenantService).selectTenantList(any(SysTenant.class));
        verify(tenantService, never()).updateTenant(any(SysTenant.class));
    }

    @Test
    public void testTrialExpirationReminder_WithExpiringTenants() {
        // Given
        // 设置即将过期的租户（1天后过期）
        SysTenant expiringTenant = new SysTenant();
        expiringTenant.setShopId("tenant003");
        expiringTenant.setTenantName("即将过期租户");
        expiringTenant.setTrialStatus(1);
        expiringTenant.setTrialEndTime(new Date(System.currentTimeMillis() + 12 * 60 * 60 * 1000L)); // 12小时后过期
        
        List<SysTenant> trialTenants = Arrays.asList(trialTenant, expiringTenant);
        
        when(tenantService.selectTenantList(any(SysTenant.class))).thenReturn(trialTenants);

        // When
        trialStatusTask.trialExpirationReminder();

        // Then
        verify(tenantService).selectTenantList(argThat(tenant -> 
            tenant.getTrialStatus() != null && tenant.getTrialStatus().equals(1)
        ));
        
        // 这里可以验证发送提醒的逻辑，但由于当前实现只是日志输出，所以主要验证方法执行完成
    }

    @Test
    public void testTrialExpirationReminder_NoExpiringTenants() {
        // Given
        List<SysTenant> trialTenants = Arrays.asList(trialTenant); // 还有2天才过期，不需要提醒
        
        when(tenantService.selectTenantList(any(SysTenant.class))).thenReturn(trialTenants);

        // When
        trialStatusTask.trialExpirationReminder();

        // Then
        verify(tenantService).selectTenantList(any(SysTenant.class));
        // 由于没有即将过期的租户，不会发送提醒
    }

    @Test
    public void testTrialExpirationReminder_NullEndTime() {
        // Given
        SysTenant tenantWithNullEndTime = new SysTenant();
        tenantWithNullEndTime.setShopId("tenant004");
        tenantWithNullEndTime.setTrialStatus(1);
        tenantWithNullEndTime.setTrialEndTime(null); // 结束时间为空
        
        List<SysTenant> trialTenants = Arrays.asList(tenantWithNullEndTime);
        
        when(tenantService.selectTenantList(any(SysTenant.class))).thenReturn(trialTenants);

        // When
        trialStatusTask.trialExpirationReminder();

        // Then
        verify(tenantService).selectTenantList(any(SysTenant.class));
        // 方法应该正常执行，不会因为null值而抛出异常
    }

    @Test
    public void testTrialExpirationReminder_ExceptionHandling() {
        // Given
        when(tenantService.selectTenantList(any(SysTenant.class))).thenThrow(new RuntimeException("数据库异常"));

        // When
        trialStatusTask.trialExpirationReminder();

        // Then
        // 验证异常被捕获，方法正常结束
        verify(tenantService).selectTenantList(any(SysTenant.class));
    }

    @Test
    public void testCheckTrialExpiredStatus_MultipleExpiredTenants() {
        // Given
        SysTenant expiredTenant2 = new SysTenant();
        expiredTenant2.setShopId("tenant005");
        expiredTenant2.setTrialStatus(1);
        expiredTenant2.setTrialEndTime(new Date(System.currentTimeMillis() - 2 * 24 * 60 * 60 * 1000L)); // 2天前过期
        
        List<SysTenant> trialTenants = Arrays.asList(expiredTenant, expiredTenant2);
        
        when(tenantService.selectTenantList(any(SysTenant.class))).thenReturn(trialTenants);
        when(tenantService.updateTenant(any(SysTenant.class))).thenReturn(1);
        doNothing().when(trialPermissionService).clearTenantTrialCache(anyString());

        // When
        trialStatusTask.checkTrialExpiredStatus();

        // Then
        // 验证两个过期租户都被更新
        verify(tenantService, times(2)).updateTenant(any(SysTenant.class));
        verify(trialPermissionService).clearTenantTrialCache("tenant002");
        verify(trialPermissionService).clearTenantTrialCache("tenant005");
    }
} 
package com.ruoyi.system.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.ruoyi.common.annotation.TableId;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 微信扫码登录对象 wx_login
 */
public class WxLogin extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId
    private Long id;

    /** uuid */
    @Excel(name = "UUID")
    private String uuid;

    /** 创作者id */
    @Excel(name = "创作者ID")
    private String creatorId;

    /** 创作者的openid */
    @Excel(name = "OpenID")
    private String openid;

    /** 创作者的微信的unionid */
    @Excel(name = "UnionID")
    private String unionid;

    /** 登录的微信小程序appid */
    @Excel(name = "微信小程序APPID")
    private String wxMaAppid;

    /** 过期时间, 为创建时间后的5分钟 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expirationTime;

    /** 是否扫码成功,0:未扫码,1:扫码成功,2:取消登录,3:登录成功 */
    @Excel(name = "扫码状态")
    private Integer scanSuccess;

    /** 是否过期,0:未过期,1:已过期 */
    @Excel(name = "是否过期")
    private Integer isExpired;

    /** 用户绑定的手机号（国外手机号会有区号）手机登录使用这个号码 */
    @Excel(name = "手机号")
    private String phoneNumber;

    /** 没有国家区号的手机号 */
    @Excel(name = "纯手机号")
    private String purePhoneNumber;

    /** 国家区号 */
    @Excel(name = "国家区号")
    private String countryCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @NotBlank(message = "UUID不能为空")
    @Size(min = 0, max = 100, message = "UUID长度不能超过100个字符")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Size(min = 0, max = 64, message = "创作者ID长度不能超过64个字符")
    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    @Size(min = 0, max = 128, message = "OpenID长度不能超过128个字符")
    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    @Size(min = 0, max = 128, message = "UnionId长度不能超过128个字符")
    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid;
    }

    @Size(min = 0, max = 32, message = "微信小程序APPID长度不能超过32个字符")
    public String getWxMaAppid() {
        return wxMaAppid;
    }

    public void setWxMaAppid(String wxMaAppid) {
        this.wxMaAppid = wxMaAppid;
    }

    public Date getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
    }

    public Integer getScanSuccess() {
        return scanSuccess;
    }

    public void setScanSuccess(Integer scanSuccess) {
        this.scanSuccess = scanSuccess;
    }

    public Integer getIsExpired() {
        return isExpired;
    }

    public void setIsExpired(Integer isExpired) {
        this.isExpired = isExpired;
    }

    @Size(min = 0, max = 30, message = "手机号长度不能超过30个字符")
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Size(min = 0, max = 30, message = "纯手机号长度不能超过30个字符")
    public String getPurePhoneNumber() {
        return purePhoneNumber;
    }

    public void setPurePhoneNumber(String purePhoneNumber) {
        this.purePhoneNumber = purePhoneNumber;
    }

    @Size(min = 0, max = 10, message = "国家区号长度不能超过10个字符")
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    /**
     * 判断是否已过期
     */
    public boolean isExpired() {
        return expirationTime != null && expirationTime.before(new Date());
    }

    /**
     * 判断是否有效（未过期）
     */
    public boolean isValid() {
        return expirationTime != null && expirationTime.after(new Date());
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("uuid", getUuid())
                .append("creatorId", getCreatorId())
                .append("openid", getOpenid())
                .append("unionid", getUnionid())
                .append("wxMaAppid", getWxMaAppid())
                .append("expirationTime", getExpirationTime())
                .append("scanSuccess", getScanSuccess())
                .append("isExpired", getIsExpired())
                .append("phoneNumber", getPhoneNumber())
                .append("purePhoneNumber", getPurePhoneNumber())
                .append("countryCode", getCountryCode())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

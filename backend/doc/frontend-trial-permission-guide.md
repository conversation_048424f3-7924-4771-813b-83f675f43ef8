# 前端试用权限控制实现指南

## 概述

本文档说明如何在Vue 2.6.12前端项目中实现试用权限控制，包括自定义指令、API集成和使用示例。

## 1. 试用权限指令实现

### 1.1 创建 hasTrialPermi.js 指令

在 `src/directive/permission/` 目录下创建 `hasTrialPermi.js`：

```javascript
import store from '@/store'
import { checkTrialPermission } from '@/api/trial'

export default {
  async inserted(el, binding, vnode) {
    const { value } = binding
    
    if (!value || !value.permission) {
      throw new Error(`请设置试用权限标签值，格式：{ permission: 'system:user:list', type: 1 }`)
    }

    const { permission, type = 1 } = value
    const permissions = store.getters && store.getters.permissions
    const all_permission = "*:*:*"

    // 首先检查基础权限
    const hasBasicPermission = permissions.some(p => {
      return all_permission === p || p === permission
    })

    if (!hasBasicPermission) {
      // 没有基础权限，直接隐藏
      el.parentNode && el.parentNode.removeChild(el)
      return
    }

    try {
      // 检查试用权限
      const hasTrialPermission = await checkTrialPermission(permission, type)
      
      if (!hasTrialPermission) {
        // 没有试用权限，隐藏元素
        el.parentNode && el.parentNode.removeChild(el)
      } else {
        // 有权限，显示元素（可能需要添加试用标识）
        const tenantInfo = store.getters.tenantInfo
        if (tenantInfo && tenantInfo.trialStatus === 1) {
          // 试用期间，添加试用标识
          addTrialBadge(el, type)
        }
      }
    } catch (error) {
      console.error('检查试用权限失败:', error)
      // 出错时隐藏元素，保证安全
      el.parentNode && el.parentNode.removeChild(el)
    }
  },

  async update(el, binding, vnode) {
    // 当绑定值更新时重新检查权限
    const { value } = binding
    if (value && value.permission) {
      // 重新执行权限检查逻辑
      this.inserted(el, binding, vnode)
    }
  }
}

/**
 * 添加试用标识
 */
function addTrialBadge(el, type) {
  // 如果已经有试用标识，不重复添加
  if (el.querySelector('.trial-badge')) {
    return
  }

  const badge = document.createElement('span')
  badge.className = 'trial-badge'
  badge.style.cssText = `
    position: relative;
    margin-left: 4px;
    padding: 1px 4px;
    font-size: 10px;
    background: ${type === 2 ? '#ff4d4f' : '#52c41a'};
    color: white;
    border-radius: 2px;
    vertical-align: top;
  `
  badge.textContent = type === 2 ? '试用' : '免费'
  
  // 将标识添加到元素后面
  if (el.tagName === 'BUTTON' || el.tagName === 'A') {
    el.appendChild(badge)
  } else {
    el.style.position = 'relative'
    el.appendChild(badge)
  }
}
```

### 1.2 创建 API 接口

在 `src/api/` 目录下创建 `trial.js`：

```javascript
import request from '@/utils/request'

/**
 * 检查试用权限
 * @param {string} permission 权限代码
 * @param {number} type 权限类型 1-读权限 2-写权限
 */
export function checkTrialPermission(permission, type) {
  return request({
    url: '/trial/checkPermission',
    method: 'post',
    data: {
      permission,
      type
    }
  })
}

/**
 * 获取租户试用信息
 */
export function getTenantTrialInfo() {
  return request({
    url: '/trial/tenantInfo',
    method: 'get'
  })
}

/**
 * 申请试用
 * @param {number} trialDays 试用天数，默认7天
 */
export function applyTrial(trialDays = 7) {
  return request({
    url: '/trial/apply',
    method: 'post',
    params: {
      trialDays
    }
  })
}

/**
 * 获取试用权限列表
 */
export function getTrialPermissions() {
  return request({
    url: '/trial/permissions',
    method: 'get'
  })
}

/**
 * 获取试用统计信息
 */
export function getTrialStatistics() {
  return request({
    url: '/trial/statistics',
    method: 'get'
  })
}
```

### 1.3 API 接口说明

#### 后端API接口详情

**1. 检查试用权限**
- **URL**: `POST /trial/checkPermission`
- **参数**: 
  ```json
  {
    "permission": "system:user:list",
    "type": 1
  }
  ```
- **返回**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": true
  }
  ```

**2. 获取租户试用信息**
- **URL**: `GET /trial/tenantInfo`
- **返回**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": {
      "tenantId": "tenant001",
      "tenantName": "测试租户",
      "trialStatus": 1,
      "trialDays": 7,
      "trialStartTime": "2024-01-01 00:00:00",
      "trialEndTime": "2024-01-08 00:00:00",
      "remainingDays": 5
    }
  }
  ```

**3. 申请试用**
- **URL**: `POST /trial/apply?trialDays=7`
- **返回**: 
  ```json
  {
    "code": 200,
    "msg": "试用申请成功，试用期为 7 天"
  }
  ```

**4. 获取试用权限列表**
- **URL**: `GET /trial/permissions`
- **返回**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": [
      {
        "id": 1,
        "permissionCode": "system:user:list",
        "permissionName": "用户查询",
        "permissionType": 1,
        "trialAvailable": 1,
        "expiredAvailable": 1
      }
    ]
  }
  ```

**5. 获取试用统计信息**
- **URL**: `GET /trial/statistics`
- **返回**: 
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": {
      "tenantId": "tenant001",
      "trialStatus": 1,
      "totalReadPermissions": 10,
      "totalWritePermissions": 15,
      "availableReadPermissions": 10,
      "availableWritePermissions": 15
    }
  }
  ```

### 1.4 更新指令注册

修改 `src/directive/permission/index.js`：

```javascript
import hasRole from './hasRole'
import hasPermi from './hasPermi'
import hasTrialPermi from './hasTrialPermi'

const install = function(Vue) {
  Vue.directive('hasRole', hasRole)
  Vue.directive('hasPermi', hasPermi)
  Vue.directive('hasTrialPermi', hasTrialPermi)
}

if (window.Vue) {
  window['hasRole'] = hasRole
  window['hasPermi'] = hasPermi
  window['hasTrialPermi'] = hasTrialPermi
  Vue.use(install)
}

export default install
```

## 2. Vuex 状态管理

### 2.1 添加试用相关状态

在 `src/store/modules/user.js` 中添加：

```javascript
const state = {
  // ... 现有状态
  tenantInfo: null,
  trialPermissions: []
}

const mutations = {
  // ... 现有mutations
  SET_TENANT_INFO: (state, tenantInfo) => {
    state.tenantInfo = tenantInfo
  },
  SET_TRIAL_PERMISSIONS: (state, permissions) => {
    state.trialPermissions = permissions
  }
}

const actions = {
  // ... 现有actions
  
  // 获取租户试用信息
  async getTenantInfo({ commit }) {
    try {
      const response = await getTenantTrialInfo()
      commit('SET_TENANT_INFO', response.data)
      return response.data
    } catch (error) {
      console.error('获取租户信息失败:', error)
      return null
    }
  },

  // 获取试用权限列表
  async getTrialPermissions({ commit }) {
    try {
      const response = await getTrialPermissions()
      commit('SET_TRIAL_PERMISSIONS', response.data)
      return response.data
    } catch (error) {
      console.error('获取试用权限失败:', error)
      return []
    }
  }
}

// 添加getters
const getters = {
  // ... 现有getters
  tenantInfo: state => state.tenantInfo,
  trialPermissions: state => state.trialPermissions,
  isTrialExpired: state => {
    return state.tenantInfo && state.tenantInfo.trialStatus === 2
  },
  isInTrial: state => {
    return state.tenantInfo && state.tenantInfo.trialStatus === 1
  }
}
```

## 3. 使用示例

### 3.1 基本使用

```vue
<template>
  <div>
    <!-- 读权限：试用期和过期后都可用 -->
    <el-button 
      v-hasTrialPermi="{ permission: 'system:user:list', type: 1 }"
      @click="handleQuery">
      查询用户
    </el-button>

    <!-- 写权限：试用期可用，过期后不可用 -->
    <el-button 
      v-hasTrialPermi="{ permission: 'system:user:add', type: 2 }"
      type="primary"
      @click="handleAdd">
      新增用户
    </el-button>

    <!-- 导出权限：读权限，过期后仍可用 -->
    <el-button 
      v-hasTrialPermi="{ permission: 'system:user:export', type: 1 }"
      @click="handleExport">
      导出数据
    </el-button>
  </div>
</template>

<script>
export default {
  methods: {
    handleQuery() {
      // 查询逻辑
    },
    handleAdd() {
      // 新增逻辑
    },
    handleExport() {
      // 导出逻辑
    }
  }
}
</script>
```

### 3.2 表格操作列

```vue
<template>
  <el-table-column label="操作" width="200">
    <template slot-scope="scope">
      <el-button
        v-hasTrialPermi="{ permission: 'system:user:edit', type: 2 }"
        size="mini"
        type="text"
        @click="handleUpdate(scope.row)">
        修改
      </el-button>
      
      <el-button
        v-hasTrialPermi="{ permission: 'system:user:remove', type: 2 }"
        size="mini"
        type="text"
        @click="handleDelete(scope.row)">
        删除
      </el-button>
    </template>
  </el-table-column>
</template>
```

### 3.3 菜单权限控制

```vue
<template>
  <div>
    <!-- 菜单项 -->
    <el-menu-item 
      v-hasTrialPermi="{ permission: 'system:user:list', type: 1 }"
      index="user">
      <i class="el-icon-user"></i>
      <span>用户管理</span>
    </el-menu-item>
  </div>
</template>
```

## 4. 试用状态提示组件

### 4.1 创建试用状态组件

在 `src/components/Trial/` 目录下创建 `TrialStatus.vue`：

```vue
<template>
  <div v-if="showTrialStatus" class="trial-status">
    <el-alert
      :title="alertTitle"
      :type="alertType"
      :description="alertDescription"
      :closable="false"
      show-icon>
      <template slot="title">
        <span>{{ alertTitle }}</span>
        <el-button 
          v-if="tenantInfo.trialStatus === 2"
          type="primary" 
          size="mini" 
          style="margin-left: 10px"
          @click="handleUpgrade">
          立即升级
        </el-button>
      </template>
    </el-alert>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TrialStatus',
  computed: {
    ...mapGetters(['tenantInfo', 'isTrialExpired', 'isInTrial']),
    
    showTrialStatus() {
      return this.tenantInfo && (this.isInTrial || this.isTrialExpired)
    },
    
    alertTitle() {
      if (this.isTrialExpired) {
        return '试用已过期'
      } else if (this.isInTrial) {
        const remainingDays = this.getRemainingDays()
        return `试用期剩余 ${remainingDays} 天`
      }
      return ''
    },
    
    alertType() {
      return this.isTrialExpired ? 'error' : 'warning'
    },
    
    alertDescription() {
      if (this.isTrialExpired) {
        return '您的试用期已结束，部分功能已被限制。您仍可以查看和导出数据，但无法进行新增、修改、删除等操作。'
      } else if (this.isInTrial) {
        return '您正在使用试用版本，可以体验所有功能。试用期结束后，写入功能将被限制。'
      }
      return ''
    }
  },
  
  methods: {
    getRemainingDays() {
      if (!this.tenantInfo || !this.tenantInfo.trialEndTime) {
        return 0
      }
      
      const endTime = new Date(this.tenantInfo.trialEndTime)
      const now = new Date()
      const diffTime = endTime - now
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      return Math.max(0, diffDays)
    },
    
    handleUpgrade() {
      // 跳转到升级页面或显示升级对话框
      this.$router.push('/upgrade')
    }
  }
}
</script>

<style scoped>
.trial-status {
  margin-bottom: 20px;
}
</style>
```

### 4.2 在主布局中使用

在 `src/layout/components/AppMain.vue` 中添加：

```vue
<template>
  <section class="app-main">
    <!-- 试用状态提示 -->
    <trial-status />
    
    <!-- 原有内容 -->
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key" />
    </transition>
  </section>
</template>

<script>
import TrialStatus from '@/components/Trial/TrialStatus'

export default {
  name: 'AppMain',
  components: {
    TrialStatus
  },
  // ... 其他代码
}
</script>
```

## 5. 初始化和权限检查

### 5.1 在登录后获取试用信息

在 `src/store/modules/user.js` 的 `getInfo` action中添加：

```javascript
// 获取用户信息
getInfo({ commit, state }) {
  return new Promise((resolve, reject) => {
    getInfo().then(response => {
      const { data } = response
      
      if (!data) {
        reject('验证失败，请重新登录。')
      }

      const { roles, permissions } = data

      // ... 现有逻辑

      commit('SET_ROLES', roles)
      commit('SET_PERMISSIONS', permissions)
      
      // 获取租户试用信息
      this.dispatch('user/getTenantInfo')
      this.dispatch('user/getTrialPermissions')
      
      resolve(data)
    }).catch(error => {
      reject(error)
    })
  })
}
```

## 6. 样式定义

在 `src/styles/index.scss` 中添加试用相关样式：

```scss
// 试用标识样式
.trial-badge {
  position: relative;
  margin-left: 4px;
  padding: 1px 4px;
  font-size: 10px;
  border-radius: 2px;
  vertical-align: top;
  
  &.trial-read {
    background: #52c41a;
    color: white;
  }
  
  &.trial-write {
    background: #ff4d4f;
    color: white;
  }
}

// 试用状态提示样式
.trial-status {
  margin-bottom: 20px;
  
  .el-alert {
    border-radius: 4px;
  }
}

// 禁用状态样式
.trial-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  
  &:hover {
    opacity: 0.6;
  }
}
```

## 7. 错误处理和降级策略

### 7.1 网络错误处理

```javascript
// 在 hasTrialPermi.js 中添加错误处理
export default {
  async inserted(el, binding, vnode) {
    try {
      // ... 权限检查逻辑
    } catch (error) {
      console.error('试用权限检查失败:', error)
      
      // 网络错误时的降级策略
      if (error.code === 'NETWORK_ERROR') {
        // 网络错误时，保持元素可见但添加警告
        addNetworkErrorBadge(el)
      } else {
        // 其他错误时隐藏元素
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}
```

## 8. 测试建议

### 8.1 功能测试
1. 测试试用期间所有功能可用
2. 测试试用过期后读权限可用，写权限被限制
3. 测试网络异常时的降级处理
4. 测试权限变更时的实时更新

### 8.2 用户体验测试
1. 验证试用标识显示正确
2. 验证过期提示友好明确
3. 验证升级引导流程顺畅

## 9. 部署注意事项

1. 确保后端API接口已实现
2. 配置正确的API地址
3. 测试生产环境的权限控制
4. 监控权限检查的性能影响

通过以上实现，前端可以完美配合后端的试用权限控制，为用户提供良好的试用体验和升级引导。 
package com.ruoyi.wendaologin.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 登录日志对象 wendao_sys_login_log
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class WendaoSysLoginLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private String bUserId;

    /** 登录方式:1微信扫码,2手机号 */
    @Excel(name = "登录方式:1微信扫码,2手机号")
    private Integer loginType;

    /** 登录IP */
    @Excel(name = "登录IP")
    private String ip;

    /** 设备信息 */
    @Excel(name = "设备信息")
    private String deviceInfo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setbUserId(String bUserId) 
    {
        this.bUserId = bUserId;
    }

    public String getbUserId() 
    {
        return bUserId;
    }
    public void setLoginType(Integer loginType) 
    {
        this.loginType = loginType;
    }

    public Integer getLoginType() 
    {
        return loginType;
    }
    public void setIp(String ip) 
    {
        this.ip = ip;
    }

    public String getIp() 
    {
        return ip;
    }
    public void setDeviceInfo(String deviceInfo) 
    {
        this.deviceInfo = deviceInfo;
    }

    public String getDeviceInfo() 
    {
        return deviceInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bUserId", getbUserId())
            .append("loginType", getLoginType())
            .append("ip", getIp())
            .append("deviceInfo", getDeviceInfo())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
